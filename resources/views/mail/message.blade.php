<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>
<body>
<style>
    body, body *:not(html):not(style):not(br):not(tr):not(code) {
        font-family: Verdana, Arial, Tahoma, sans-serif !important;
        box-sizing: border-box;
        font-size: 14px;
    }

    body {
        background-color: #ffffff;
        color: #000000;
        height: 100%;
        hyphens: auto;
        line-height: 1.4;
        margin: 0;
        -moz-hyphens: auto;
        width: 100% !important;
        -webkit-hyphens: auto;
        -webkit-text-size-adjust: none;
        word-break: break-word;
    }

    @media only screen and (max-width: 600px) {
        .inner-body {
            width: 100% !important;
        }

        .footer {
            width: 100% !important;
        }
    }

    @media only screen and (max-width: 500px) {
        .button {
            width: 100% !important;
        }
    }

    hr {
        margin: 50px 0 30px;
        border: 1px dotted #cccccc;
    }

    thead,
    tfoot {
        color: #000000;
    }

    .table-title-black {
        color: #000000 !important;
        font-size: 15px;
        text-transform: uppercase;
        margin-top: 32px;
        font-weight: 400;
    }

    .text-black {
        color: #000000 !important;
    }

    h3.table-title {
        text-transform: none;
    }

    .half-table,
    .three-piece-table {
        width: 100%;
        border-collapse: collapse;
        margin: 0 auto 32px;
    }

    .half-table tr,
    three-piece-table tr {
        word-wrap: break-word;
        position: relative;
    }

    .half-table tfoot tr,
    .three-piece-table tfoot tr {
        border-bottom: 0;
    }

    .half-table td {
        width: 55%;
        padding: 10px;
    }

    .three-piece-table td {
        width: 33%;
        padding: 10px;
    }

    .button-table {
        width: 100%;
    }

    .button-table td a {
        margin: 0 auto;
    }

    .table-details tbody tr {
        border: 0;
        color: #000000;
    }

    .table-details tbody tr:first-child {
        background-color: rgba(0, 0, 0, .04);
        border-radius: 5px;
        color: #000000;
    }

    .table-details tbody tr:first-child td {
        padding-left: 10px;
    }

    .compiance-table {
        border: 1px solid #cccccc;
        border-radius: 10px;
        padding: 10px 10px 15px;
        width: 100%;
    }

    .cell {
        display: table-cell;
        vertical-align: middle;
        padding-right: 5px;
        color: #000000;
    }

    .d-block {
        display: block;
    }

    .text-small {
        font-size: 0.75rem;
    }

    .value {
        white-space: nowrap;
    }
</style>

@yield('style')


<table class="wrapper" width="100%" cellpadding="0" cellspacing="0">
    <tr>
        <td align="center">
            <table class="content" width="100%" cellpadding="0" cellspacing="0">
                <tr>
                    {{--@include('mail.header')--}}
                </tr>

                <!-- Email Body -->
                <tr>
                    <td class="body" width="100%" cellpadding="0" cellspacing="0">
                        @yield('body')
                    </td>
                </tr>

                <tr>
                    <td>
{{--                        @include('mail.footer')--}}
                    </td>
                </tr>

            </table>
        </td>
    </tr>
</table>
</body>
</html>
