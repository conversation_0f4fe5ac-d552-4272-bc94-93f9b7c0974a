@extends('mail.message')
@section('style')
    <style>
        table {
            margin: 10px 0;
        }
    </style>
@endsection

@php
/** @var \Modules\Accounting\Pub\ViewObjects\ProviderViewObject $providerViewObject */
@endphp
@section('body')
    <p class="text-black">Please create new supplier in your ERP based on the below request:</p>
    @if($providerViewObject->getCreator())
        <p class="text-black">User: {{$providerViewObject->getCreator()->getEmail()}}</p>
    @endif
    <table class="inner-body" align="left" width="800" cellpadding="0" cellspacing="0">

        <tr>
            <td class="content-cell text-black">Supplier data:</td>
        </tr>

        <tr>
            <td class="content-cell text-black"><strong>name:</strong> {{ $providerViewObject->getName() }}</td>
        </tr>

        @if($providerViewObject->getTaxReferenceNo())
            <tr>
                <td class="content-cell text-black"><strong>tax_id:</strong> {{ $providerViewObject->getTaxReferenceNo() }}</td>
            </tr>
        @endif

        @if($providerViewObject->getAddress())
            <tr>
                <td class="content-cell text-black"><strong>address:</strong> {{ $providerViewObject->getAddress() }}</td>
            </tr>
        @endif

        @if($providerViewObject->getCity())
            <tr>
                <td class="content-cell text-black"><strong>city:</strong> {{ $providerViewObject->getCity() }}</td>
            </tr>
        @endif

        @if($providerViewObject->getPostCode())
            <tr>
                <td class="content-cell text-black"><strong>post code:</strong> {{ $providerViewObject->getPostCode() }}</td>
            </tr>
        @endif

        @if($providerViewObject->getCountryCode())
            <tr>
                <td class="content-cell text-black"><strong>country</strong> {{ $providerViewObject->getCountryCode() }}</td>
            </tr>
        @endif
    </table>
@endsection
