@extends('mail.message')
@section('style')
    <style>
        table {
            margin: 30px 0;
        }

        .user-email a{
            color: black !important;
        }

    </style>
@endsection

@section('body')
    <p class="text-black">Podczas synchronizacji SAP DMS w instancji {{ $instanceDomain }} wystąpiło następujące zdarzenie:</p>
    <table class="inner-body" align="left" width="800" cellpadding="0" cellspacing="0">
        <tr>
            <td class="content-cell text-black">{{ $messageContent }}</td>
        </tr>
    </table>

@endsection
