@extends('mail.message')
@section('style')
    <style>
        table {
            margin: 30px 0;
        }

        .user-email a {
            color: black !important;
        }

    </style>
@endsection

@section('body')
    <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0">
        <!-- Body content -->
        <tr>
            <td class="content-cell">
                <p class="text-black">
                    Użytkownik {{ $user  }} {{ $email }} wybrał opcję Zakończ podróż już teraz lub Podróż się nie odbyła  i prosi o anulowanie
                    wszystkich
                    niewykupionych rezerwacje dla {{ $name }}.
                </p>
                <br>
                <p class="text-black">Szczegóły:</p>
                @foreach($ticket_numbers_and_type as $ticketNumberAndType)
                    @if($ticketNumberAndType['type'] == 'plane_trip')
                    <p class="text-black">
                        Przelot: nr biletu {{$ticketNumberAndType['ticket_number']}}
                        Przelot: nr rezerwacji {{$ticketNumberAndType['ticket_number']}}
                        Przelot: nr wniosku <a href="{{$request_url}}">{{$request_number}}</a>
                    </p>
                    @endif
                    @if($ticketNumberAndType['type'] == 'accomodation')
                    <p class="text-black">
                        Nocleg: nr rezerwacji {{$ticketNumberAndType['ticket_number']}}
                        Nocleg: nr wniosku <a href="{{$request_url}}">{{$request_number}}</a>
                    </p>
                    @endif
                    @if($ticketNumberAndType['type'] == 'train_trip')
                    <p class="text-black">
                        Przejazd pociągiem: nr biletu {{$ticketNumberAndType['ticket_number']}}
                        Przejazd pociągiem: nr wniosku <a href="{{$request_url}}">{{$request_number}}</a>
                    </p>
                    @endif
                @endforeach
        </tr>
    </table>
@endsection
