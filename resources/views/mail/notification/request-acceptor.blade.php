@extends('mail.message')
@section('style')
    <style>
        table {
            margin: 0 0;
        }

        .text-black {
            color: black !important;
        }

        .warning-icon {
            max-width: 25px;
            width: 25px;
            height: 25px;
        }

        .not-bold {
            font-weight: 400;
        }

        .user-email a{
            color: black !important;
            text-decoration: none;
        }

        font {
            display: none;
        }

        .desc {
            width: 65% !important;
        }

        .text-right {
            text-align: right;
        }

        .value {
            width: 35% !important;
        }

        .vertical-align-top {
            vertical-align: top;
        }

        .content-cell {
            padding: 0 0;
        }
    </style>
@endsection

@section('body')
    <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0">
        <!-- Body content -->
        <tr class="text-black">
            <td class="content-cell">

                <table class="header half-table table-details">
                    <thead>
                    <tr>
                        <td>&nbsp;</td>
                        <td class="text-right header__td header__td-second" style="width: 85% !important;">
                            <div class="text-black">
                                {{$user->first_name}} {{$user->last_name}}
                            </div>
                            <div>
                                <span class="not-bold user-email"><font style="display: none;">@</font>{{strtolower($user->email)}}</span>
                            </div>
                            @if(!empty($user->phone))
                                <div class="text-black">
                                    <span class="not-bold">{{$user->phone}}</span>
                                </div>
                            @endif
                        </td>
                    </tr>
                    </thead>
                </table>

                <h2 class="table-title-black">{{trans('request.state_request')}} {{$request->number}}</h2>
                <h3 class="table-title-black">{{strtoupper(trans($request->getNameAttribute($locale)))}}</h3>
                <table class="half-table">
                    <thead>
                    <tr>
                        <td class="desc text-black">{{trans('global.name')}}</td>
                        <td class="value text-right text-black">{{trans('request-summary.requested-amount')}}</td>
                    </tr>
                    </thead>
                    <tbody>

                    @foreach($summary['elements'] as $element)
                        @if($element['requestedConvertedAmount']['amount'] != '0.00')
                            <tr>
                                <td class="desc text-black">{{trans($element['name']['name'], $element['name']['params'])}}</td>
                                <td class="value text-right text-black">@amount_format($element['requestedConvertedAmount']['amount']) {{$element['requestedConvertedAmount']['currency']}}</td>
                            </tr>
                        @endif
                    @endforeach
                    </tbody>
                    <tfoot>
                    <tr>
                        <td class="desc text-black">{{trans('request-summary.in-total')}}</td>
                        <td class="value text-right text-black">@amount_format($summary['requestedConvertedAmount']['amount']) {{$summary['requestedConvertedAmount']['currency']}}</td>
                    </tr>
                    <tr>
                        @if(array_key_exists('installments', $summary) && empty($summary['installments']) === false)
                            <td class="desc text-black vertical-align-top">{{ trans('request-summary.with-installments') }}</td>
                            <td class="value text-right text-black">
                                @foreach($summary['installments'] as $currency => $installment)
                                    <div>@amount_format($installment['amount']) {{$installment['currency']}}</div>
                                @endforeach
                            </td>
                        @endif
                    </tr>
                    </tfoot>
                </table>
            </td>
        </tr>
        <tr>
            <td class="content-cell">
                @if(array_key_exists('rules', $summary) && empty($summary['rules']) === false)
                    <div>
                        <div class="request-summary-warnings">
                            <p class='request-summary-warnings__warning-header'>{{trans('request.compliance-message')}}:</p>
                            <ul>
                                @foreach($summary['rules'] as $rule)
                                    <div>- {{trans($rule['message']['name'], $rule['message']['params'])}}</div>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </td>
        </tr>
        <tr>
            <td class="content-cell">
                <table class="button-table">
                    <tbody>
                    <tr>
                        @if($acceptanceByTokenIsEnabled)
                            <td align="center"><a href="{{ $acceptanceUrl }}">{{trans('notification-email.request-accept-btn')}}</a></td>
                            <td align="center"><a href="{{ $rejectionUrl }}">{{trans('notification-email.request-decline-btn')}}</a></td>
                        @else
                            <td align="center" @if(!$acceptanceByTokenIsEnabled) colspan="2" @endif ><a href="{{ $request->getFrontendUrl() }}">{{trans('global.go-to-app')}}</a></td>
                        @endif
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td class="content-cell">
                @component('mail::footer')
                    {{
                        trans('notification-email.do-more', [
                            'url' => $request->getFrontendUrl()
                        ])
                    }}
                @endcomponent
            </td>
        </tr>
    </table>
@endsection

