@extends('mail.message')
@section('style')
    <style>
        table {
            margin: 30px 0;
        }

        .user-email a{
            color: black !important;
        }

    </style>
@endsection

@section('body')
    <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0">
        <!-- Body content -->
        <tr>

            <td class="content-cell">
                <p class="text-black">Błąd wysłania {{ $item }}</p>
                <br>
                <p class="text-black">Nie udało się poprawnie przeprowadzić synchronizacji w systemie ERP.</p>
                <p class="text-black"><strong>Kolejne próby mogą zostać podjęte przez użytkownika systemu.</strong></p>
                <br>
                <p class="text-black">Podczas wysyłki wystąpiły nastepujące błędy</p>
                @foreach($errors as $error)
                    <p class="text-black"><strong>{{ $error['name'] }}</strong></p>
                    <ul>
                    @foreach($error['messages'] as $message)
                        <li class="text-black">{{ $message }}</li>
                    @endforeach
                    </ul>
                @endforeach
                <ul>

                </ul>
            </td>
        </tr>
    </table>

@endsection
