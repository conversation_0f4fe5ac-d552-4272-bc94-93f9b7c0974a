@extends('mail.message')
@section('style')
    <style>
        table {
            margin: 30px 0;
        }

        .text-black {
            color: black !important;
        }

        .warning-icon {
            max-width: 25px;
            width: 25px;
            height: 25px;
        }

        font {
            display: none;
        }

        .not-bold {
            font-weight: 400;
        }

        .avatar {
            border-radius: 50%;
            display: block;
            border: none;
            width: 80px;
            height: 80px;
            background-color: #e1e1e1;
            object-fit: cover;
            flex-shrink: 0;
            margin-right: 15px;
        }

        .user-email a{
            color: black !important;
            text-decoration: none;
        }

        .desc {
            width: 50% !important;
        }

        .nest-level-0 {
            padding-left: 10px !important;
        }

        .nest-level-1 {
            padding-left: 25px !important;
        }

        .nest-level-2 {
            padding-left: 50px !important;
        }

        .text-right {
            text-align: right;
        }

        .value {
            width: 22% !important;
        }

        .text-weight-normal {
            font-weight: normal;
        }
    </style>
@endsection

@section('body')
    <table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0">
        <!-- Body content -->
        <tr>
            <td class="content-cell">

                <table class="header half-table table-details">
                    <thead>
                    <tr>
                        <td>&nbsp;</td>
                        <td class="text-right header__td header__td-second text-black" style="width: 85% !important;">
                            <div class="text-black">
                                {{$user->first_name}} {{$user->last_name}}
                            </div>
                            <div>
                                <span class="not-bold user-email"><font style="display: none">@</font>{{strtolower($user->email)}}</span>
                            </div>

                            @if(!empty($user->phone))
                                <div>
                                    <span class="not-bold text-black">{{$user->phone}}</span>
                                </div>
                            @endif
                        </td>
                    </tr>
                    </thead>
                </table>

                <div class="header-padding">
                    <h2 class="table-title-black">{{trans('request.state_settlement')}} {{$request->number}}</h2>
                    <h3 class="table-title-black">{{strtoupper(trans($request->getNameAttribute($user->locale)))}}</h3>
                </div>

                <table class="three-piece-table">
                    <thead>
                    <tr>
                        <td class="desc text-black">{{trans('global.name')}}</td>
                        <td class="value text-right text-black">{!! trans('notification-email.accounted-amount') !!}</td>
                        <td class="value text-right text-black">{!! trans('notification-email.requested-amount') !!}</td>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($summary['elements'] as $element)
                        @if($element['requestedConvertedAmount']['amount'] != '0.00' || $element['settledConvertedAmount']['amount'] != '0.00')
                            <tr>
                                <td class="desc text-black">{{trans($element['name']['name'], $element['name']['params'])}}</td>
                                <td class="value text-right text-black">@amount_format($element['settledConvertedAmount']['amount']) {{$element['settledConvertedAmount']['currency']}}</td>
                                <td class="value text-right text-black">@amount_format($element['requestedConvertedAmount']['amount']) {{$element['requestedConvertedAmount']['currency']}}</td>
                            </tr>
                        @endif
                    @endforeach
                    </tbody>
                    <tfoot>
                    <tr class="text-black">
                        <td class="desc text-black">{{trans('request-summary.in-total')}}</td>
                        <td class="value text-right text-black">@amount_format($summary['settledConvertedAmount']['amount']) {{$summary['settledConvertedAmount']['currency']}}</td>
                        <td class="value text-right text-black">@amount_format($summary['requestedConvertedAmount']['amount']) {{$summary['requestedConvertedAmount']['currency']}}</td>
                    </tr>
                    <tr>
                        @if(array_key_exists('installments', $summary) && empty($summary['installments']) === false)
                            <td class="desc text-black vertical-align-top">{{ trans('request-summary.with-installments') }}</td>
                            <td class="value text-right text-black">
                                @foreach($summary['installments'] as $currency => $installment)
                                    <div>@amount_format($installment['amount']) {{$installment['currency']}}</div>
                                @endforeach
                            </td>
                        @endif
                    </tr>
                    </tfoot>
                </table>
            </td>
        </tr>
        <tr>
            <td class="content-cell">
                <table class="button-table">
                    <tbody>
                    <tr>
                        @if($acceptanceByTokenIsEnabled)
                            <td align="center"><a href="{{ $acceptanceUrl }}">{{trans('notification-email.request-accept-btn')}}</a></td>
                            <td align="center"><a href="{{ $returnToImprovementUrl }}">{{trans('notification-email.request-return-to-improvement-btn')}}</a></td>
                        @else
                            <td align="center" @if(!$acceptanceByTokenIsEnabled) colspan="3" @endif><a href="{{ $request->getFrontendUrl() }}">{{trans('global.go-to-app')}}</a></td>
                        @endif
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td class="content-cell">
                @component('mail::footer')
                    {{
                        trans('notification-email.do-more', [
                            'url' => $request->getFrontendUrl()
                        ])
                    }}
                @endcomponent
            </td>
        </tr>
    </table>
@endsection
