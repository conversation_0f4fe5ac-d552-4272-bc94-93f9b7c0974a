body {
    color: #212529;
    font-family: '<PERSON>o', sans-serif;
}

.bg-gray {
    background-color: #f5f5f5;
}


.bg-white {
    background-color: white;
}

.header__number {
    display: block;
    text-align: right;
}

.header__title {
    font-size: 24px;
    text-align: center;
    text-transform: uppercase;
    padding-bottom: 25px;
    margin-bottom: 25px;
    border-bottom: 1px solid #000;
}


.main__trip-details {
    display: block;
    margin-bottom: 40px;
}

.main__trip-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 15px;
}
.main__trip-wrapper:last-child {
    margin-bottom: 0;
}

.main__trip-heading {
    font-weight: bold;
    display: block;
    width: 32%;
}

.main__trip-list {
    display: block;
    width: 68%;
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.main__trip-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 5px;
}

.main__trip-part {
    width: 50%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-right: 10px;
}
.main__trip-part:last-child {
    padding-right: 0;
}

.main__trip-part-heading {
    font-weight: bold;
    display: block;
    width: 35%;
}

.main__trip-part-date {
    display: block;
    width: 65%;
}

.main__section-wrapper {
    margin-bottom: 40px;
}
.main__section-wrapper:last-child {
    margin-bottom: 60px;
}

.main__section-wrapper-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-right: 5px;
}

.main__section-wrapper-heading {
    display: block;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 18px;
}
.main__section-wrapper-heading--center {
    text-align: center !important;
    display: block;
    width: 100%;
    margin: 0 auto;
}

.main__documents-table {
    border-collapse: collapse;
}

.main__documents-table thead {
    vertical-align: bottom;
}

.main__documents-table thead tr {
    border-bottom: 1px solid #000;
}

.main__documents-table thead th {
    padding: 5px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.main__documents-table thead th span {
    white-space: nowrap;
}

.main__documents-table tbody td {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 15px 5px;
    text-align: center;
}

.main__documents-table tfoot tr:first-child {
    border-top: 1px solid #000;
}

.main__documents-table tfoot td {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 5px;
    text-align: center;
    font-weight: bold;
}

.header__detail {
    display: block;
    font-size: 18px;
}


.main__documents-table tfoot td:last-child {
    text-align: right;
}

.main__diet-header {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 25px;
}

.main__diet-heading {
    font-weight: bold;
}

.main__diet-currency {
    margin-bottom: 30px;
}

.main__diet-currency-heading {
    font-weight: bold;
}

.main__diet-list {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.main__diet-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin-bottom: 10px;
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
}
.main__diet-item:last-child {
    margin-bottom: 0;
}

.main__diet-item-heading {
    font-weight: bold;
    display: block;
    width: 35%;
}

.main__diet-item-table {
    border-collapse: collapse;
    width: 65%;
}

.main__diet-item-table thead tr {
    border-bottom: 1px solid #000;
}

.main__diet-item-table thead th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 5px;
    white-space: nowrap;
    text-align: center;
}
.main__diet-item-table thead th:nth-child(2) {
    min-width: 150px;
}

.main__diet-item-table td:first-child,
.main__diet-item-table th:first-child {
    width: 100%;
    text-align: left;
}

.main__diet-item-table td {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 15px 5px;
    text-align: right;
}
.main__diet-item-table td:nth-child(2) {
    text-align: center;
}


.main__bilance-table {
    border-collapse: collapse;
}

.main__bilance-table thead tr {
    border-bottom: 1px solid #000;
}

.main__bilance-table thead th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 5px;
}

.main__bilance-table thead th:first-child {
    width: 100%;
}

.main__bilance-table thead th:nth-child(2) {
    text-align: right;
}

.main__bilance-table tbody td:nth-child(2) {
    text-align: right;
}

.main__bilance-table tbody tr {
    border-bottom: 1px solid #000;
    vertical-align: top;
    page-break-inside: avoid;
}

.main__bilance-table tbody td {
    padding: 15px 5px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.main__bilance-table tbody strong {
    display: block;
    text-align: right;
    white-space: nowrap;
    margin-bottom: 5px;
}

.main__bilance-table tbody strong:last-child {
    margin-bottom: 0;
}

.main__bilance-table tbody tr:last-child {
    border-bottom: none;
}

.main__accounting-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
}

.main__accounting-table thead tr:not(.without-border) {
    border-bottom: 1px solid #000;
}

.main__accounting-table thead th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 5px;
    white-space: nowrap;
}
.main__accounting-table .main__accounting-table-header th:not(:first-child):nth-child(-n +3) {
    text-align: left;
}

.main__accounting-table .main__accounting-table-header th:nth-child(n + 4) {
    text-align: center;
    padding-left: 10px;
    padding-right: 10px;
}

.main__accounting-table .main__accounting-table-header th:nth-child(2) {
    width: 50%;
}

.main__accounting-table .main__accounting-table-header th:nth-child(3) {
    width: 50%;
}

.main__accounting-table .main__accounting-table-header tr {
    vertical-align: top;
    page-break-inside: avoid;
}

.main__accounting-table tbody td {
    padding: 15px 5px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
}

.main__accounting-table tbody td:first-child {
    width:5px;
    text-align: center;
}

.main__accounting-table tbody td:nth-child(4) {
    text-align: right;
}

.main__accounting-table tbody td:nth-child(5) {
    text-align: center;
}

.main__accounting-table tbody td:nth-child(6) {
    text-align: center;
}

.main__accounting-table tbody td:last-child {
    text-align: right;
}

.main__accounting-table tbody strong {
    display: block;
    text-align: right;
    white-space: nowrap;
    margin-bottom: 5px;
}

.main__accounting-table tbody strong:last-child {
    margin-bottom: 0;
}
.main__accounting-table-document-row td{
    padding-bottom: 0!important;
    margin-bottom: 0!important;
}

.main__accounting-table .row-accounting_details-table td{
    padding-top: 0;
    padding-bottom: 30px;
    margin:0;
}

.accounting_details-table {
    border-collapse: collapse;
    width:100%;
}

.accounting_details-table tbody td {
    padding-top: 2px !important;
    padding-bottom: 2px !important;
}

.accounting_details-table tbody td:not(:last-child):not(:nth-last-of-type(2)) {
    text-align: left;
    word-spacing: -3px;
}

.accounting_details-table thead th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 5px;
    white-space: nowrap;
}
.accounting_details-table thead th:last-child {
    text-align: right;
    margin-left: 20px;
    width: 20px;
}

.accounting_details-table thead th:nth-last-child(2) {
    text-align: right;
    padding:0 20px;
    margin-right: 20px;
    width: 20px;
}

.accounting_details-table tbody tr {
    vertical-align: top;
    page-break-inside: avoid;
}

.accounting_details-table tbody td {
    padding: 15px 5px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.accounting_details-table tbody td:first-child {
    padding-right:15px;
}

.accounting_details-table tbody td:last-child {
    text-align: right;
    padding-left: 20px;
}

.accounting_details-table tbody td:nth-last-child(2) {
    text-align: right;
    padding-right: 20px;
}

.accounting_details-table tbody strong {
    display: block;
    text-align: right;
    white-space: nowrap;
    margin-bottom: 5px;
}

.accounting_details-table tbody strong:last-child {
    margin-bottom: 0;
}

.accounting_details-table tbody tr:last-child {
    border-bottom: none;
}

.main__documents {
    margin-bottom: 50px;
}

.main__documents-heading {
    font-size: 18px;
    font-weight: bold;
    display: block;
    margin-bottom: 15px;
    text-transform: uppercase;
    text-align: center;
}

.main__documents-table {
    border-spacing: 0;
    border-collapse: collapse;
}

.main__documents-table thead th {
    text-align: center;
    white-space: nowrap;
    padding: 5px;
    border-bottom: 1px solid #000;
}

.main__documents-table tbody td {
    text-align: right;
    padding: 20px 5px;
}
.main__documents-table tbody tr {
    page-break-inside: avoid;
    border-bottom: 1px solid #000;
}

.main__documents-table tbody tr:last-child {
    border-bottom: 0;
}

body {
    color: #212529;
    font-family: 'Roboto', sans-serif;
}

.header__number {
    display: block;
    text-align: right;
}

.header__title {
    font-size: 24px;
    text-align: center;
    text-transform: uppercase;
    padding-bottom: 25px;
    margin-bottom: 25px;
    border-bottom: 1px solid #000;
}

.main__note {
    margin-bottom: 60px;
}

.main__note-heading {
    font-size: 18px;
    display: block;
    font-weight: bold;
    text-align: center;
}

.main__note-space {
    display: block;
    width: 100%;
    height: auto;
    min-height: 150px;
    padding: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #000;
    margin: 10px 0 15px 0;
}

.main__note-author {
    display: block;
}

.main__details {
    margin-bottom: 60px;
}

.main__details-heading {
    display: block;
    font-size: 18px;
    font-weight: bold;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: 15px;
}

.main__details-table {
    border-spacing: 0;
    border-collapse: collapse;
}

.main__details-table thead th {
    text-align: center;
    white-space: nowrap;
    padding: 5px;
    border-bottom: 1px solid #000;
    vertical-align: top;
}

.main__details-table tbody td {
    text-align: right;
    padding: 20px 5px;
}

.main__details-table tbody tr:last-child {
    border-bottom: 1px solid #000;
}

.main__details-table tfoot th {
    text-align: right;
    padding: 10px 5px;
}
.main__comapny-details {
    margin-bottom: 40px;
}

.main__comapny-detail {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.main__user-details {
    margin-bottom: 40px;
}

.main__user-detail {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 5px;
}

.main__user-detail strong {
    display: block;
    width: 15%;
}

.main__user-detail span {
    display: block;
    width: 85%;
}

/* Deductions */
.main__deductions-table {
    width: 100%;
    border-collapse: collapse;
}

.main__deductions-table thead th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 5px;
}

.main__deductions-table tbody tr {
    page-break-inside: avoid;
}

.main__deductions-table tbody td {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px 5px;
}

.main__deductions-trip-heading {
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
}

.main__deductions-trip-heading td {
    line-height: 1;
    text-indent: 70px;
    max-height: 16px;
}

.main__deductions-date {
    white-space: nowrap;
}

.main__deductions-check-icon {
    display: block;
    margin: 0 auto;
    height: 16px;
    max-height: 16px;
}

.main__expense-commute-table {
    width: 100%;
    border-collapse: collapse;
}

.main__expense-commute-table thead th {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-transform: uppercase;
    padding: 5px;
}

.main__expense-commute-table tbody tr {
    page-break-inside: avoid;
}

.main__expense-commute-table tbody td{
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 15px 5px;
}

.main__expense-commute-heading {
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    text-align: center;
    font-weight: bold;
}

.main__expense-commute-heading td {
    padding: 10px 5px !important;
}

.footer__signatures {
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    width: 100%;
}

.footer__signature {
    text-align: left;
}

.footer__signature:last-child {
    text-align: right;
}

.footer__signature-title {
    font-weight: bold;
}
.footer__signature-signature {
    margin: 5px 0;
}

.breakable {
    -ms-word-break: break-all;
    word-break: break-all;

    /* Non standard for webkit */
    word-break: break-word;

    -webkit-hyphens: auto;
    -moz-hyphens: auto;
    -ms-hyphens: auto;
    hyphens: auto;
}
