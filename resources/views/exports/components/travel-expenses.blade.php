@php
    $lastDay = null;
    $white = 1;
@endphp
@foreach($travelExpenses as $index=>$element)
    @php
        if(!$lastDay || (new \Carbon\Carbon($element['date']))->diffInDays(new \Carbon\Carbon($lastDay['date'])) == 1) {
            $lastDay = $element;
            $white = !$white;
        }
    @endphp

    @if($element['type'] == \App\Services\RequestMealDeductionsAndLumpSumsWidget\ElementDTO::TYPE_TRIP_START)
        <tr class="main__deductions-trip-heading @if($white == 1) bg-white @else bg-gray @endif">
            <td colspan="4" class="main__deductions-date">
                <span>{{$element['date']}} {{$element['localTime']}} ({{$element['timeZoneAbbreviation']}})</span>
                <span>{{trans('print-settlement.expense.table.start')}}: {{trans($element['countryName'])}} @if($element['values']['target']) - {{trans('print-settlement.expense.table.start')}} @endif</span>
            </td>
        </tr>
    @elseif($element['type'] == \App\Services\RequestMealDeductionsAndLumpSumsWidget\ElementDTO::TYPE_EXPENSE)
        <tr class="main__deductions-trip-heading @if($white == 1) bg-white @else bg-gray @endif">
            <td class="main__deductions-date">{{$element['date']}}</td>
            @foreach($values as $name)
                <td>@if($element['values'][$name])<img class="main__deductions-check-icon" src="{{ resource_path('assets/img/checked.png') }}"> @endif</td>
            @endforeach
        </tr>
    @elseif($element['type'] == \App\Services\RequestMealDeductionsAndLumpSumsWidget\ElementDTO::TYPE_BORDER_CROSSING)
        <tr class="main__deductions-trip-heading @if($white == 1) bg-white @else bg-gray @endif">
            <td colspan="4" class="main__deductions-date">
                <span>{{$element['date']}} {{$element['localTime']}} ({{$element['timeZoneAbbreviation']}})</span>
                <span>{{trans('print-settlement.expense.table.cross-border')}}: {{trans($element['countryName'])}} @if($element['values']['target']) - {{trans('print-settlement.expense.table.target-country')}} @endif</span>
            </td>
        </tr>
    @elseif($element['type'] == \App\Services\RequestMealDeductionsAndLumpSumsWidget\ElementDTO::TYPE_TRAVEL_EXPENSE_PERIOD_CHANGE)
        <tr class="main__deductions-trip-heading @if($white == 1) bg-white @else bg-gray @endif">
            <td colspan="4" class="main__deductions-date">{{$element['date']}} {{$element['localTime']}} ({{$element['timeZoneAbbreviation']}})</td>
        </tr>
    @elseif($element['type'] == \App\Services\RequestMealDeductionsAndLumpSumsWidget\ElementDTO::TYPE_TRIP_END)
        <tr class="main__deductions-trip-heading @if($white == 1) bg-white @else bg-gray @endif">
            <td colspan="4" class="main__deductions-date">
                <span>{{$element['date']}} {{$element['localTime']}} ({{$element['timeZoneAbbreviation']}})</span>
                <span>{{trans('print-settlement.expense.table.end')}}: {{trans($element['countryName'])}}</span>
            </td>
        </tr>
    @endif
@endforeach
