<script>
    (function(){
        setTimeout(function() {
            window.opener = window.opener || {
                postMessage: function (payload, origin) {
                    location.href = location.protocol + '//' + location.host + '?token=' + payload.token;
                },
                location: {
                    origin: ''
                }
            };
            window.opener.postMessage(
                {
                    'action': 'auth',
                    'token': '{{$token}}',
                    'error': '{{$error}}'
                },
                window.opener.location.origin
            );
            window.close();
        }, 100);
    })();
</script>
