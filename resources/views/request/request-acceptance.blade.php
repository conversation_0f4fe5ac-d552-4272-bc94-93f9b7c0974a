@extends('layout')

@section('tab_title')
    {{ trans('request.request-accept-by-token-title') }}
@endsection

@section('body')
    @if($invalidToken)
        <div class="app__main-page-content column-padding">
        <p class="is-gradient-success" style="width: 320px;height: 60px;text-align: center;position: absolute;top: calc(35% - 30px); left: calc(50% - 150px); font-weight: 400; font-size: 18px;">{{trans('notification-email.link-was-used')}}</p>
    @else
        <body>
            <div class="app__main-page-content column-padding" style="width:100%;max-width:1024px;margin:0 auto;">

            <section class="section section--no-margin">
                <h1 class="h2 section__header-title">{{$request->name}}</h1>
                <br>

                @if($success)
                    @if($action == 'accept')
                    <strong style="color:#64e0ba; font-size: 20px;">{{trans('success.request-has-been-accepted')}}</strong>
                    @else
                    <strong style="color:#ff2243; font-size: 20px;">{{trans('success.request-has-been-rejected')}}</strong>
                    @endif
                @endif
            </section>

            <section class="section section--no-border section--no-padding">
                <header class="section__header">
                    <h2 class="h2 section__header-title">{{trans('request.applicant')}}</h2>
                </header>

                <div class="row">
                    <div class="xs-7 md-6 lg-7">
                        <div class="user-profile-card">
                            <div class="user-profile-card__avatar" style="background-image: url(data:image/png;base64,{{ $base64Avatar }}"></div>
                            <div class="user-profile-card__content">
                                <h4 class="user-profile-card__header">{{$request->user->first_name}} {{$request->user->last_name}}</h4>
                                <span>
                                    <a href="mailto:{{$request->user->email}}">{{$request->user->email}}</a>
                                    <br>
                                    <a href="tel:{{$request->user->phone}}">{{$request->user->phone}}</a>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="section section--no-border section--no-padding table-nested-padding">
                <header class="section__header">
                    <h2 class="h2 section__header-title">{{trans('request-summary.request-summary')}} {{$request->number}}</h2>
                </header>

                <div class="table-accordion-container table-accordion-container--no-padding-bottom">
                    <div class="table-accordion">
                        <div class="table-accordion__head">
                            <div class="table-accordion__row row">
                                <div class="table-accordion__col xs-7 lg-10">
                                    <div class="table-accordion__col__content">{{trans('global.name')}}</div>
                                </div>
                                <div class="table-accordion__col xs-5 lg-2" style="padding-right: 0;">
                                    <div class="table-accordion__col__content is-allign-end">{{trans('request-summary.requested-amount')}}</div>
                                </div>
                            </div>
                        </div>

                        <div class="table-accordion__body">
                            @foreach($summary['elements'] as $element)
                            <div class="accordion table-accordion__accordion">
                                <div class="accordion__bar" style="padding-right: 0;">
                                    <div class="table-accordion__row row">
                                        <div class="table-accordion__col xs-6 lg-10">
                                            <div class="table-accordion__col__content">{{trans($element['name']['name'], $element['name']['params'])}}</div>
                                        </div>
                                        <div class="table-accordion__col xs-6 lg-2">
                                            <div class="table-accordion__col__content is-allign-end"><strong>@amount_format($element['requestedConvertedAmount']['amount']) {{$element['requestedConvertedAmount']['currency']}}</strong></div>
                                        </div>
                                    </div>
                                </div><!-- bar -->
                                <div class="accordion__content">
                                    @foreach($element['elements'] as $requestElement)
                                        <div class="table-accordion__row row basic-summary__element-row">
                                            <div class="table-accordion__col xs-7 lg-10">
                                                <div class="table-accordion__col__content">
                                                    <span class="basic-summary__element-text">{{trans($requestElement['name']['name'], $requestElement['name']['params'])}}</span>
                                                </div>
                                            </div>
                                            <div class="table-accordion__col xs-5 lg-2 is-allign-end">
                                                <div class="table-accordion__col__content">
                                                    @amount_format($requestElement['requestedConvertedAmount']['amount']) {{$requestElement['requestedConvertedAmount']['currency']}}
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        </div>

                        <div class="table-accordion__footer">
                            <div class="table-accordion__row row table-accordion__row--is-thin">
                                <div class="table-accordion__col xs-7 lg-10">
                                    <div class="table-accordion__col__content">
                                        <strong>{{trans('request-summary.in-total')}}</strong>
                                    </div>
                                </div>
                                <div class="table-accordion__col xs-5 lg-2 is-allign-end" style="padding-right: 0;">
                                    <div class="table-accordion__col__content">
                                        <strong>@amount_format($summary['requestedConvertedAmount']['amount']) {{$summary['requestedConvertedAmount']['currency']}}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if(array_key_exists('installments', $summary))
                        <div class="table-accordion__body">

                                <div class="accordion table-accordion__accordion">
                                    <div class="accordion__bar" style="padding-right: 0;">
                                        <div class="table-accordion__row row">
                                            <div class="table-accordion__col xs-6 lg-10">
                                                <div class="table-accordion__col__content">{{ trans('request-summary.with-installments') }}</div>
                                            </div>
                                            <div class="table-accordion__col xs-6 lg-2">
                                                @foreach($summary['installments'] as $currency => $installment)
                                                    <div class="table-accordion__col__content is-allign-end">@amount_format($installment['amount']) {{$installment['currency']}}</div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                        @endif
                    </div>
                </div>

                @if(array_key_exists('rules', $summary) && empty($summary['rules']) === false)
                    <div>
                        <div class="request-summary-warnings">
                            <p class='request-summary-warnings__warning-header'>
                                <i class="icon icon-warning request-summary-warnings__warning-icon is-gradient-warning"></i> {{trans('request.compliance-message')}}:
                            </p>
                            <ul>
                                @foreach($summary['rules'] as $rule)
                                    <div >- {{trans($rule['message']['name'], $rule['message']['params'])}}</div>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif
            </section>
            @if(!$success)
                {{ Form::open(['route' => [($action == 'accept' ? 'accept-by-token.save' : 'reject-by-token.save'), 'slug' => $request->slug, 'token' => $token]]) }}
                <section class="section section--no-border section--no-padding section-accept-by-email">
                    <div class="container">
                        <div class="row form-group">
                            <div class="col-2 col-md-2 col-sm-2 align-items-center">
                                {{ Form::password('pin_code', ['class' => 'form-control',  'placeholder' => trans('request.enter-pin-code')]) }}
                            </div>
                        </div>
                        @error(['key' => "pin_code"]) @enderror
                        <div class="row form-group">
                            <div class="col-2 col-md-2 col-sm-2 align-items-center">
                                @if($action == 'accept')
                                {{Form::submit(trans('request.accept'), ['class' => 'btn btn--primary text-center', 'title' => trans('request.accept')])}}
                                @else
                                {{Form::submit(trans('request.reject'), ['class' => 'btn btn--danger btn--danger--nogradient text-center', 'title' => trans('request.reject')])}}
                                @endif
                            </div>
                        </div>
                    </div>
                </section>
                {{ Form::close() }}
            @endif
        </div>
    @endif
@endsection

@include('request.scripts')