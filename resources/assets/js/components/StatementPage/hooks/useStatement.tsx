import { useMemo } from 'react';

import { StatementSummary } from '../../../types/statements';
import useStatements from '../../StatementsPage/hooks/useStatements';

export default function useStatement(id: string | undefined) {
  const { data: statements, loading } = useStatements();

  const summaries = useMemo(() => {
    if (!id || !statements || statements.length === 0) {
      return [];
    }

    const statement = statements.find((stmt) => stmt.id === id);

    if (!statement) {
      return [];
    }

    const summaryData: StatementSummary[] = [
      {
        name: 'my-card.statement-summary-label-balance',
        value: statement.balance,
      },
      {
        name: 'my-card.statement-summary-label-settled',
        value: statement.amount_settled,
      },
      {
        name: 'my-card.statement-summary-label-remind-settled',
        value: statement.amount_to_settle,
      },
    ];

    return summaryData;
  }, [id, statements]);

  return {
    summaries,
    loading,
    statement: statements?.find((stmt) => stmt.id === id),
  };
}
