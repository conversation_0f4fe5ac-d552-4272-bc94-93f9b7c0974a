import React from 'react';

import { Link, useParams } from 'react-router-dom';

import { BreadCrumbs } from '../../containers';
import { getRouteByName } from '../../routes';
import trans from '../../trans';
import useTransactions from '../PaymentsPage/hooks/useTransactions';
import PaymentsPageFilters from '../PaymentsPageFilters';
import PaymentsPageTable from '../PaymentsPageTable';
import { Loader } from '../ui/LoadingOverlay/Loader';
import { Section } from '../ui/Section';
import { SectionHeader } from '../ui/SectionHeader';

import useStatement from './hooks/useStatement';
import StatementSummary from './StatementSummary';

function StatementPage() {
  const params = useParams<{ id: string; from: string; to: string }>();
  const id = params.id;

  const {
    data: transactions,
    paginator,
    loading: transactionsLoading,
    setPage,
    load,
    setFilter,
    filters,
  } = useTransactions();
  const { summaries, loading: statementLoading } = useStatement(id);

  return (
    <div>
      <BreadCrumbs>
        <Link to={getRouteByName('main', 'dashboard')}>{trans('global.dashboard')}</Link>
        <Link to={getRouteByName('main', 'my-card')}>{trans('main-menu.my-card')}</Link>
        <Link to={getRouteByName('main', 'my-card-statements')}>
          {trans('main-menu.my-card-statements')}
        </Link>
        <Link to={getRouteByName('main', 'my-card-settlement', { id })}>
          {trans('my-card.single_statement_header', { from: params.from, to: params.to })}
        </Link>
      </BreadCrumbs>

      <Section className={'my-cards'} noBorder>
        <SectionHeader
          className={'my-cards__header'}
          caption={trans('my-card.single_statement_header', {
            from: params.from,
            to: params.to,
          })}
        />

        <PaymentsPageFilters filters={filters} setFilter={setFilter} />

        {transactionsLoading || statementLoading ? (
          <Loader />
        ) : (
          <>
            <PaymentsPageTable
              data={transactions}
              paginator={paginator}
              setPage={setPage}
              refresh={load}
              loading={transactionsLoading}
            />

            <StatementSummary data={summaries} />
          </>
        )}
      </Section>
    </div>
  );
}

export default StatementPage;
