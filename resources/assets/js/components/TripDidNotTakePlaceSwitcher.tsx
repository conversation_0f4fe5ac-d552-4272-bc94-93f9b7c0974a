import React, { Fragment } from 'react'
import { connect } from '../containers/TripDidNotHavePlace'
import { Toggle } from '../ui'
import { Tooltip } from './Tooltip'
import Icon from './ui/IconComponent'
import { trans } from '../trans'
import classnames from 'classnames'

const TripDidNotHavePlaceSwitcher = connect((props) => {
  const {
    tripDidNotHavePlace: {
      actions: { setTripDidNotHavePlace },
      selectors: { isTripDidNotHavePlace },
    },
  } = props
  const {
    delegation: {
      selectors: { isDelegation },
    },
  } = props

  const {
    context: {
      request: { abilities },
    },
  } = props

  if (!(abilities.edit || abilities.settle) && isTripDidNotHavePlace) {
    return null
  }

  const classes = classnames(
    {
      'delegation-switcher': true,
    },
    props.className,
  )

  return (
    <div className={classes}>
      <div className='delegation-switcher__header'>
        {trans('request.trip-did-not-have-place-header')}

        <div className='delegation-switcher__tooltip'>
          <Tooltip
            className='hotel-dialog__offer-detail-text'
            html={trans('request.trip-did-not-have-place')}
          >
            <Icon greyLight lg type='question_fill' />
          </Tooltip>
        </div>
      </div>

      <div className='delegation-switcher__toggle'>
        {abilities.edit || abilities.settle ? (
          <Toggle
            onChange={(event) => {
              setTripDidNotHavePlace(event.target.checked)
            }}
            checked={isTripDidNotHavePlace}
            disabled={!isDelegation}
          />
        ) : isTripDidNotHavePlace ? (
          <div className='delegation-switcher__toggle-read-only'>
            {trans('request.is-delegation-trip')}
          </div>
        ) : (
          ''
        )}
      </div>
    </div>
  )
})

export default TripDidNotHavePlaceSwitcher
export { TripDidNotHavePlaceSwitcher }
