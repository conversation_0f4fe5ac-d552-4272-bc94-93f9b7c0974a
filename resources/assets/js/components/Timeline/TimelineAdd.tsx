import PropTypes from 'prop-types'
import React from 'react'

import Icon from '../../components/ui/IconComponent'
import trans from '../../trans'

const TimelineAdd = (props) => {
  const { onClick } = props

  return <div>Old timeline todo</div>

  return (
    <div className='timeline timeline--add'>
      <div className='timeline__header' onClick={onClick}>
        <span className='timeline__header-icon'>
          <Icon type='plus' />
        </span>
        <span className='timeline__header-title sm-3'>{trans('global.add')}</span>
      </div>
    </div>
  )
}

TimelineAdd.propTypes = {
  onClick: PropTypes.func.isRequired,
}

export default TimelineAdd
