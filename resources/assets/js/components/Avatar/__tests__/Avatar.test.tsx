import { render, act, cleanup } from '../../../../../../internals/testing/test-utils'
import Avatar from '../Avatar'
import { server } from '../../../../../../internals/testing/server/server'
import { rest } from 'msw'

const setup = async (props = {}, config) => {
  let result

  await act(() => {
    result = render(<Avatar {...props} />, config)
  })

  return result
}

describe('components/Avatar/Avatar.tsx', () => {
  afterEach(cleanup)

  it('should render', async () => {
    const { container } = await setup()

    expect(container).toMatchSnapshot()
  })

  it('should fetch avatar on mount and render as div with background image', async () => {
    const user = { avatar: 'some-avatar', slug: 'user-slug' }
    const { container } = await setup({ user })
    const avatar = container.querySelector('.user-avatar')

    expect(avatar.tagName).toEqual('DIV')
    expect(avatar.getAttribute('style')).toContain('blob:http')
  })

  it('should render empty element if no user provided', async () => {
    const { container } = await setup()
    const avatar = container.querySelector('.user-avatar')

    expect(avatar.tagName).toEqual('DIV')
    expect(avatar.hasAttribute('style')).toBe(false)
  })

  it('should render empty element if no avatar found', async () => {
    server.use(
      rest.get(
        '/api/storage/avatars/:slug/:filename/:filetype/:width/:height',
        async (req, res, ctx) => {
          return res(ctx.status(400))
        },
      ),
    )

    const user = { avatar: 'some-avatar', slug: 'user-slug' }
    const { container } = await setup({ user })
    const avatar = container.querySelector('.user-avatar')

    expect(avatar.tagName).toEqual('DIV')
    expect(avatar.hasAttribute('style')).toBe(false)
  })

  it('should fetch avatar on mount and render as image with src', async () => {
    const user = { avatar: 'some-avatar', slug: 'user-slug' }
    const { container } = await setup({ user, asImage: true })
    const avatar = container.querySelector('.user-avatar')

    expect(avatar.tagName).toEqual('IMG')
    expect(avatar.getAttribute('src')).toContain('blob:http')
  })

  it('should update avatar when user property has been changed', async () => {
    const user1 = { avatar: 'some-avatar', slug: 'user-slug' }
    const user2 = { avatar: 'another user', slug: 'another-slug' }
    const { container, rerender } = await setup({ user: user1 })
    const avatar1 = container.querySelector('.user-avatar')
    const url1 = avatar1.getAttribute('style')

    await act(() => rerender(<Avatar user={user2} />))

    const avatar2 = container.querySelector('.user-avatar')
    const url2 = avatar2.getAttribute('style')

    expect(url1).toContain('blob:http')
    expect(url2).toContain('blob:http')
    expect(url2).not.toEqual(url1)
  })
})
