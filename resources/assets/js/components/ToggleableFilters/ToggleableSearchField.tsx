import React, { useRef, useState } from 'react';

import { Timeout } from 'react-number-format/types/types';

import Icon from '../../components/ui/IconComponent';
import trans from '../../trans';

export const TOGGLEABLE_SEARCH_TEST_ID = 'toggleable-search-field';

export interface SearchFilterFieldProps {
  value: string;
  onChange: (value: string) => void;
  debounce?: number;
}

export function ToggleableSearchField({ value, onChange, debounce }: SearchFilterFieldProps) {
  const inputRef = useRef<HTMLInputElement>();
  const [timer, setTimer] = useState<Timeout>();
  const [currentValue, setCurrentValue] = useState<string>(value);

  const handleClear = () => {
    if (inputRef.current) {
      inputRef.current.value = '';
      setCurrentValue('');
      onChange('');
    }
  };

  return (
    <div className='input-group'>
      <Icon type='search' lg />
      <input
        type='text'
        placeholder={trans('ui.search')}
        className='input-group__input input-group__search'
        data-testid={TOGGLEABLE_SEARCH_TEST_ID}
        ref={inputRef}
        defaultValue={value}
        onChange={(e) => {
          e.preventDefault();

          const newValue = e.target.value;
          setCurrentValue(newValue);

          clearTimeout(timer);

          setTimer(
            setTimeout(() => {
              onChange(inputRef.current.value);
            }, debounce || 250),
          );
        }}
      />
      {currentValue && (
        <button
          type='button'
          className='input-group__close-icon'
          aria-label='Close'
          tabIndex={-1}
          onClick={handleClear}
        />
      )}
    </div>
  );
}

export default ToggleableSearchField;
