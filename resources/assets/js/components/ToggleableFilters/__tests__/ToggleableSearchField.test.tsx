import React from 'react'
import { render } from '../../../../../../internals/testing/test-utils'
import ToggleableSearchField, {
  SearchFilterFieldProps,
  TOGGLEABLE_SEARCH_TEST_ID,
} from '../ToggleableSearchField'
import { cleanup, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

const setup = (
  props: SearchFilterFieldProps = {
    value: '',
    debounce: undefined,
    onChange: jest.fn(),
  },
) => {
  const { container } = render(<ToggleableSearchField {...props} />)

  return {
    container,
  }
}

describe('components/ToggleableFilters/ToggleableSearchField.tsx', () => {
  afterEach(cleanup)

  it('should render component', () => {
    const { container } = setup()

    expect(container).toMatchSnapshot()
  })

  it('should have default value', () => {
    const onChangeFn = jest.fn()
    const value = 'lorem ipsum'

    setup({ onChange: onChangeFn, value })

    const inputField = screen.getByTestId(TOGGLEABLE_SEARCH_TEST_ID)

    expect(inputField).toHaveValue(value)
  })

  it('should value change when user type in', async () => {
    const onChangeFn = jest.fn()
    const value = 'lorem ipsum'
    const newValue = 'foo bar'
    const user = userEvent.setup()

    setup({ onChange: onChangeFn, value })

    const inputField = screen.getByTestId(TOGGLEABLE_SEARCH_TEST_ID)

    await user.clear(inputField)
    await user.type(inputField, newValue)

    expect(inputField).toHaveValue(newValue)
  })
})
