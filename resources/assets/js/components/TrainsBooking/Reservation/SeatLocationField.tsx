import React, { Component, Fragment } from 'react'
import PropTypes from 'prop-types'
import { Field } from 'redux-form'
import { SelectField } from '../../ui/Form'
import { trans } from '../../../trans'
import { get } from 'lodash'

class SeatLocationField extends Component<any, any> {
  getOptions = () => {
    const { offer } = this.props
    const option = offer.option

    return get(option, 'availableOptions.seat_location', []).map((option) => {
      return {
        label: trans('trains-booking.seat-location-' + option),
        value: option,
      }
    })
  }

  renderValue = () => {
    const { input } = this.props
    return trans('trains-booking.seat-location-' + input.value)
  }

  render() {
    const { input, meta, disabled } = this.props

    const options = this.getOptions()
    if (!options.length) {
      return null
    }

    return (
      <Fragment>
        <span className='train-trip__selected-ticket-travel-detail-title'>
          {trans('trains-booking.preferred-location')}
        </span>
        {disabled && this.renderValue()}

        {!disabled && (
          <div className='train-trip__selected-ticket-place-prefered-select train-trip__selected-ticket-place-prefered-select--short'>
            <SelectField
              options={options}
              value={input.value}
              placeholder={trans('trains-booking.select-seat-type')}
              onChange={(value) => input.onChange(value)}
              disabled={meta.warning}
            />
          </div>
        )}
      </Fragment>
    )
  }
}

SeatLocationField.propTypes = {
  offer: PropTypes.object.isRequired,
}

SeatLocationField.defaultProps = {
  disabled: false,
}

export default SeatLocationField
export { SeatLocationField }
