import React from 'react'
import { getFormSubmitErrors, reduxForm, SubmissionError } from 'redux-form/immutable'
import { connect } from 'react-redux'
import { fromJS } from 'immutable'
import { PrivateCar as Form } from '../Forms'
import { bindActionCreators } from 'redux'
import { processAPIerrorResponseToFormErrors } from '../../../../services/APIClient'
import { prepareRequestDates } from '../../../../utils/prepareRequestDates'
import { Factory as ElementFactory } from '../../../../models/timeline/index'
import { getFormValues } from '../../../../utils/forms'
import { DateSuggester } from '../../../../store/app/trip-timeline/services/date-suggester'
import { LocationSuggester } from '../../../../store/app/trip-timeline/services/location-suggester'
import { getCurrency } from '../../../../store/app/instance'
import { setEndOfDay } from '../../../../utils/setEndOfDay'

class PrivateCar extends React.Component<any, any> {
  render() {
    const { ...props } = this.props

    if (!this.props.initialized) {
      return null
    }

    return <Form {...props} />
  }
}
export const submit = (values, dispatch, props) => {
  const { onSave, request, change } = props

  values = prepareRequestDates(values)
  values.return_at = setEndOfDay(values.return_at, true)

  return onSave(request, values, props.element).then(
    () => {
      dispatch(change('isOpen', false))
    },
    (alerts) => {
      throw new SubmissionError(processAPIerrorResponseToFormErrors(alerts))
    },
  )
}

export const change = (values, dispatch, props) => {
  const { change } = props
  if (values.get('return_at') < values.get('departure_at')) {
    dispatch(change('return_at', values.get('departure_at')))
  }
}

const withForm = reduxForm({
  enableReinitialize: true,
  keepDirtyOnReinitialize: true,
  onSubmit: submit,
  onChange: change,
  destroyOnUnmount: false,
})(PrivateCar)

const mapStateToProps = (state, props) => {
  const { request, element, currencies } = props
  const car = ElementFactory.create(element)
  const formErrors = getFormSubmitErrors(car.key)

  const dateSuggester = new DateSuggester(state, car)
  const locationSuggester = new LocationSuggester(state, car)

  const instanceCurrency = getCurrency(state)

  return {
    initialValues: fromJS({
      uuid: car.uuid,
      departure_at: car.draft ? dateSuggester.suggestStartDate() : car.getStartDate(),
      return_at: car.draft ? dateSuggester.suggestEndDate() : car.getEndDate(),
      departure_location: car.draft
        ? locationSuggester.suggestStartLocation()
        : car.getStartLocation(),
      destination_location: car.draft
        ? locationSuggester.suggestEndLocation()
        : car.getEndLocation(),
      round_trip: car.round_trip,
      distance: car.distance,
      other_costs_amount: car.other_costs_amount,
      other_costs_currency: !car.draft ? car.other_costs_currency : instanceCurrency,
      type: car.type,
      google_distance: car.google_distance,
      id: car.id,
      converted_amount: car.converted_amount,
      calculated_amount_currency: car.calculated_amount_currency
        ? car.calculated_amount_currency
        : instanceCurrency,
      vehicle_type: car.vehicle_type,
      isOpen: car.isOpen,
      draft: car.draft,
      virtual: car.virtual,
      license_plate: car.license_plate ? car.license_plate : request.user.license_plate,
    }),
    formErrors: formErrors(state).toJS(),
    form: car.key,
    request,
    car: ElementFactory.create(getFormValues(car.key, state)),
    currencies,
    minDate: dateSuggester.suggestMinDate(),
    maxDate: dateSuggester.suggestMaxDate(),
    maxStartDate: dateSuggester.suggestMaxStartDate(),
  }
}

const mapDispatchToProps = (dispatch) => {
  return bindActionCreators({}, dispatch)
}

const connected = connect(mapStateToProps, mapDispatchToProps)(withForm)

PrivateCar = connected

export { PrivateCar }
export default { PrivateCar }
