import { Cell, Row } from '../../../ui/AccordionTable'
import Button from '../../../ui/ButtonComponent'
import trans from '../../../../trans'
import React from 'react'
import { reduxForm, Field, SubmissionError } from 'redux-form/immutable'
import { connect } from 'react-redux'
import { bindActionCreators, compose } from 'redux'
import { getFormValues } from '../../../../utils/forms'
import { fromJS } from 'immutable'
import { FormField } from '../../../ui/Form'
import { getValidationErrors } from '../../../../utils'
import { updateExpenseType } from '../../../../store/app/expense-type'
import TruncateMarkup from 'react-truncate-markup'
import Ellipsis from '../../../ui/Ellipsis/Ellipsis'
import { getActiveVatCodeSelectOptions } from '../../../../store/app/vat'
import { getActiveAccountingAccountSelectOptions } from '../../../../store/app/accounting-account'
import { getActiveExpenseGroupSelectOptions } from '../../../../store/app/expense-group'
import {
  generateAccountDimensionFormValues,
  getAccountDimensionIdFromKey,
  isAccountDimensionField,
} from '../../../../store/app/account-dimensions/form'
import { transformDimensions } from '../../../../store/app/account-dimensions/form'

class ExpenseTypeListFormItem extends React.Component<any, any> {
  getExchangeRateOptions() {
    return [
      {
        label: trans('accounting.document-element-exchange_rate_from-document'),
        value: 'document',
      },
      {
        label: trans('accounting.document-element-exchange_rate_from-settlement'),
        value: 'settlement',
      },
    ]
  }

  render() {
    const {
      handleSubmit,
      cancel,
      item,
      vatNumbers,
      accountingAccounts,
      expenseGroups,
      accountDimensions,
    } = this.props

    return (
      <Row>
        <form onSubmit={handleSubmit}>
          <Cell fixedWidth='auto'>
            <TruncateMarkup lines={1} ellipsis={<Ellipsis text={`${trans(item.short_name)}`} />}>
              <span>{trans(item.short_name)}</span>
            </TruncateMarkup>
          </Cell>
          <Cell fixedWidth={150}>
            <Field
              name='document_element_group_id'
              type='select'
              options={expenseGroups}
              component={FormField}
              inputOnly
              withErrorTooltip
            />
          </Cell>
          <Cell fixedWidth={150}>
            <Field
              name='accounting_account_id'
              type='select'
              options={accountingAccounts}
              component={FormField}
              emptyValue={{
                label: trans('global.empty-option'),
                value: null,
              }}
              inputOnly
              withErrorTooltip
            />
          </Cell>
          {accountDimensions.map((dimension) => (
            <Cell fixedWidth={150}>
              <Field
                name={dimension.fieldName}
                type='account-dimensions'
                component={FormField}
                accountDimension={dimension}
                selectedDimensions={item.accountDimensionItems}
                placeholder={trans('account-dimensions.placeholder')}
                asField
                key={dimension.id}
              />
            </Cell>
          ))}
          <Cell fixedWidth={100}>
            <Field
              name='vat_number_id'
              type='select'
              options={vatNumbers}
              component={FormField}
              emptyValue={{
                label: trans('global.empty-option'),
                value: null,
              }}
              inputOnly
              withErrorTooltip
            />
          </Cell>
          <Cell fixedWidth={165}>
            <Field
              name='exchange_rate_from'
              type='select'
              options={this.getExchangeRateOptions()}
              component={FormField}
              inputOnly
              withErrorTooltip
            />
          </Cell>
          <Cell fixedWidth={85}>
            <Field
              name='is_visible_in_trip'
              type='toggle'
              component={FormField}
              inputOnly
              withErrorTooltip
            />
          </Cell>
          <Cell fixedWidth={85}>
            <Field
              name='is_visible_in_expense'
              type='toggle'
              component={FormField}
              inputOnly
              withErrorTooltip
            />
          </Cell>
          <Cell fixedWidth={180} alignRight>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Button primary xs className={'btn--tiny'} onClick={handleSubmit}>
                {trans('ui.save-send')}
              </Button>
              <Button outline xs className={'btn--tiny'} type='button' onClick={() => cancel(item)}>
                {trans('ui.save-cancel')}
              </Button>
            </div>
          </Cell>
        </form>
      </Row>
    )
  }
}

const submit = (values, dispatch, props) => {
  const data = values.toJS()
  const { cancel, updateExpenseType } = props

  const dimensions = Object.keys(data)
    .filter((key) => isAccountDimensionField(key))
    .map((key) => ({
      [getAccountDimensionIdFromKey(key)]: data[key] ? data[key].value : data[key],
    }))
    .reduce((a, b) => ({ ...a, ...b }), {})

  const dataWithoutDimensions = Object.keys(data)
    .filter((key) => !isAccountDimensionField(key))
    .map((key) => ({ [key]: data[key] }))
    .reduce((a, b) => ({ ...a, ...b }), {})

  const payload = {
    ...dataWithoutDimensions,
    'account-dimensions': dimensions,
  }

  return updateExpenseType(payload)
    .then((item) => cancel(item))
    .catch((res) => {
      const correctedAlerts = res.alerts.map((alert) => {
        const errors = Object.keys(alert.errors)
          .map((key) => {
            const newKey = key.startsWith('account-dimensions') ? key.replace('.', '_') : key

            return { [newKey]: alert.errors[key] }
          })
          .reduce((a, b) => ({ ...a, ...b }), {})

        return {
          ...alert,
          errors,
        }
      })

      throw new SubmissionError(getValidationErrors(correctedAlerts))
    })
}

const withForm = reduxForm({
  onSubmit: submit,
  enableReinitialize: true,
})

const mapDispatchToProps = (dispatch, props) => {
  return bindActionCreators(
    {
      updateExpenseType,
    },
    dispatch,
  )
}

const mapStateToProps = (state, props) => {
  const form = `expense-type-form-${props.item.id}`
  const currentValues = getFormValues(form, state)
  const {
    item: {
      vat_number_id,
      slug,
      is_active,
      visible_in_trip,
      visible_in_expense,
      visible_in_invoice,
      exchange_rate_from,
      document_element_group_id,
      accounting_account_id,
      accountDimensionItems,
    },
    accountDimensions,
  } = props

  return {
    initialValues: fromJS({
      is_visible_in_trip: Boolean(visible_in_trip),
      is_visible_in_expense: Boolean(visible_in_expense),
      is_visible_in_invoice: Boolean(visible_in_invoice),
      is_active: Boolean(is_active),
      accounting_account_id,
      document_element_group_id,
      exchange_rate_from,
      vat_number_id,
      slug,
      ...generateAccountDimensionFormValues(accountDimensions, accountDimensionItems),
    }),
    data: currentValues,
    form,
    accountDimensions: accountDimensions.map(transformDimensions),
    accountingAccounts: getActiveAccountingAccountSelectOptions(state, [accounting_account_id]),
    vatNumbers: getActiveVatCodeSelectOptions(state, [vat_number_id]),
    expenseGroups: getActiveExpenseGroupSelectOptions(state, [document_element_group_id]),
  }
}

const withConnect = connect(mapStateToProps, mapDispatchToProps)

export default compose(withConnect, withForm)(ExpenseTypeListFormItem)
