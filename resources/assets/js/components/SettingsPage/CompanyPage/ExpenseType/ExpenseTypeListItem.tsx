import { Cell, Row } from '../../../ui/AccordionTable'
import Button from '../../../ui/ButtonComponent'
import trans from '../../../../trans'
import React from 'react'
import TruncateMarkup from 'react-truncate-markup'
import Ellipsis from '../../../ui/Ellipsis/Ellipsis'
import { TruncateBreakable } from '../../../TruncateBreakable'

const getDimensionName = (accountDimension, selectedAccountDimensions) => {
  const selectedDimension = selectedAccountDimensions.find(
    (item) => item.account_dimension_id === accountDimension.id,
  )

  if (!selectedDimension) {
    return '-'
  }

  const option = selectedDimension.accountDimensionItem

  if (!option) {
    return '-'
  }

  return option.code + ' - ' + option.name
}

export default function ExpenseTypeListItem({
  short_name,
  visible_in_expense,
  is_active,
  visible_in_trip,
  exchange_rate_from,
  edit,
  accountingAccount,
  vatNumber,
  group,
  accountDimensionItems,
  accountDimensions,
  ...rest
}) {
  return (
    <Row>
      <Cell fixedWidth='auto'>
        <TruncateMarkup lines={1} ellipsis={<Ellipsis text={`${trans(short_name)}`} />}>
          <span>{trans(short_name)}</span>
        </TruncateMarkup>
      </Cell>
      <Cell fixedWidth={150}>
        {group && (
          <TruncateMarkup lines={1} ellipsis={<Ellipsis text={`${group.name}`} />}>
            <span>{group.name}</span>
          </TruncateMarkup>
        )}
      </Cell>
      <Cell fixedWidth={150}>{accountingAccount && accountingAccount.account_number}</Cell>
      {accountDimensions.map((item) => (
        <Cell fixedWidth={150} className='expense-type-list-item--cell'>
          <TruncateBreakable lines={1} text={getDimensionName(item, accountDimensionItems)} />
        </Cell>
      ))}
      <Cell fixedWidth={100}>{vatNumber && vatNumber.code}</Cell>
      <Cell fixedWidth={165}>
        {trans(`accounting.document-element-exchange_rate_from-${exchange_rate_from}`)}
      </Cell>
      <Cell fixedWidth={85}>
        {trans(visible_in_trip ? 'accounting.is_active_yes' : 'accounting.is_active_no')}
      </Cell>
      <Cell fixedWidth={85}>
        {trans(visible_in_expense ? 'accounting.is_active_yes' : 'accounting.is_active_no')}
      </Cell>
      <Cell fixedWidth={180} alignRight>
        <Button primary xs className={'btn--tiny'} onClick={edit}>
          {trans('instance-users.edit')}
        </Button>
      </Cell>
    </Row>
  )
}
