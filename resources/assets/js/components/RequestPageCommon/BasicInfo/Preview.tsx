import React from 'react'
import trans from '../../../trans'

class Preview extends React.Component<any, any> {
  render() {
    const { mpk, project, target, isModuleActive } = this.props
    return (
      <div>
        <header className='section__header'>
          <h1 className='h2 section__header-title'>{trans('trip-request.basic-info-header')}</h1>
        </header>

        <div className='row'>
          <label className='form-group form-group--label-top xs-12'>
            <span className='form-group__label form-group__label'>{trans('global.mpk')}</span>

            <div className='form-group__input-wrapper form-group__input-wrapper--no-margin'>
              <p>
                {mpk.name} ({mpk.code})
              </p>
            </div>
          </label>
        </div>

        {isModuleActive('projects') ? (
          <div className='row'>
            <label className='form-group form-group--label-top xs-12'>
              <span className='form-group__label form-group__label'>{trans('global.project')}</span>

              <div className='form-group__input-wrapper form-group__input-wrapper--no-margin'>
                <p>
                  {project.name} ({project.code})
                </p>
              </div>
            </label>
          </div>
        ) : null}

        <div className='row'>
          <label className='form-group form-group--label-top xs-12'>
            <span className='form-group__label form-group__label'>
              {trans('request.description')}
            </span>

            <div className='form-group__input-wrapper form-group__input-wrapper--no-margin'>
              <p>{target}</p>
            </div>
          </label>
        </div>
      </div>
    )
  }
}

export { Preview }
export default { Preview }
