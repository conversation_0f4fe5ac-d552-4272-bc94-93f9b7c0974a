import React from 'react'
import { cleanup, render, screen } from '@testing-library/react'
import ProviderSelectSingleValue, {
  ProviderSelectSingleValueProps,
} from '../ProviderSelectSingleValue'

const BASIC_PROPS: ProviderSelectSingleValueProps = {
  children: undefined,
  clearValue: jest.fn(),
  cx: jest.fn(),
  data: {
    data: { address: '', city: '', erp_id: '', name: '', postcode: '', registry_number: '' },
  },
  getClassNames: jest.fn(),
  getStyles: jest.fn(),
  getValue: jest.fn(),
  hasValue: false,
  innerProps: undefined,
  isDisabled: false,
  isMulti: false,
  isRtl: false,
  options: [],
  selectOption: jest.fn(),
  selectProps: undefined,
  setValue: jest.fn(),
  theme: undefined,
}

const setup = (data, children) => {
  const { container } = render(
    <ProviderSelectSingleValue {...BASIC_PROPS} data={{ data }}>
      {children}
    </ProviderSelectSingleValue>,
  )

  return {
    container,
  }
}

describe('components/AccountDocumentForm/ProviderSelectSingleValue.tsx', () => {
  afterEach(cleanup)

  it('should render children if no data provided', () => {
    const testId = 'lorem'
    const data = null
    const children = <span data-testid={testId}>My Children</span>

    setup(data, children)

    expect(screen.getByTestId(testId)).toBeInTheDocument()
  })

  it('should render provider if data provided', () => {
    const testId = 'lorem'
    const data = { name: 'My Name' }
    const children = <span data-testid={testId}>My Children</span>

    setup(data, children)

    expect(screen.getByText(data.name)).toBeInTheDocument()
    expect(screen.queryByTestId(testId)).toEqual(null)
  })
})
