import React from 'react'
import { cleanup, render, screen } from '@testing-library/react'
import RenderProvider from '../RenderProvider'
import trans from '../../../trans'

const setup = (name: string, nip?: string, erp?: string, address?: string, city?: string) => {
  const { container } = render(
    <RenderProvider name={name} erpId={erp} registryNumber={nip} address={address} city={city} />,
  )

  const name$ = screen.queryByText(name)
  const erpLabel$ = screen.queryByText(trans('document.erp-id') + ':')
  const registryNumberLabel$ = screen.queryByText(trans('document.provider-nip') + ':')
  const address$ = screen.queryByText(trans('document.address') + ':')

  return {
    name$,
    erpLabel$,
    registryNumberLabel$,
    address$,
    container,
  }
}

describe('components/AccountDocumentForm/RenderProvider.tsx', () => {
  afterEach(cleanup)

  it('should not have erp id, registry number and address', () => {
    const name = 'My Name'
    const { name$, erpLabel$, registryNumberLabel$, address$ } = setup(name)

    expect(name$).toBeInTheDocument()
    expect(erpLabel$).toBeInTheDocument()
    expect(erpLabel$.parentNode.lastChild.textContent).toEqual('-')
    expect(registryNumberLabel$).toBeInTheDocument()
    expect(registryNumberLabel$.parentNode.lastChild.textContent).toEqual('-')
    expect(address$).toBeInTheDocument()
    expect(address$.parentNode.lastChild.textContent).toEqual('-')
  })

  it('should have erp id, registry number and address', () => {
    const name = 'My Name'
    const erpId = 'My ERP ID'
    const registryNumber = 'My Registry Number'
    const address = 'My Address'
    const city = 'My City'

    const { name$, erpLabel$, registryNumberLabel$ } = setup(
      name,
      registryNumber,
      erpId,
      address,
      city,
    )

    const erp$ = screen.getByText(erpId)
    const registryNumber$ = screen.getByText(registryNumber)
    const address$ = screen.getByText(`${address}, ${city}`)

    expect(name$).toBeInTheDocument()
    expect(erp$).toBeInTheDocument()
    expect(registryNumber$).toBeInTheDocument()
    expect(address$).toBeInTheDocument()
  })
})
