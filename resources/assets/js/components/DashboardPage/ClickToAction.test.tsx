import { cleanup, screen } from '@testing-library/react'
import React from 'react'
import { ClickToAction } from './ClickToAction'
import { render } from '../../../../../internals/testing/test-utils'

const setup = (abilities) => {
  const { user, container } = render(<ClickToAction abilities={abilities} />)

  return {
    user,
    container,
  }
}

describe('components/DashboardPage/ClickToAction.tsx', () => {
  afterEach(cleanup)

  it('should not display anything with required abilities', () => {
    setup({})

    expect(screen.queryByText('dashboard-page.there-is-no-tasks')).toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-trip-request-action')).not.toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-expense-request-action')).not.toBeInTheDocument()
    expect(
      screen.queryByText('dashboard-page.new-expense-periodic-request-action'),
    ).not.toBeInTheDocument()
  })

  it('should display all boxes', () => {
    const { container } = setup({
      showRegularTrip: true,
      showRegularExpense: true,
      showPeriodicExpense: true,
    })

    expect(screen.queryByText('dashboard-page.there-is-no-tasks')).toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-trip-request-action')).toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-expense-request-action')).toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-expense-periodic-request-action')).toBeInTheDocument()
    expect(container.querySelector('.click-to-action__box')).toBeDefined()
  })

  it('should have one box only', () => {
    const { container } = setup({
      showRegularTrip: true,
      showRegularExpense: true,
      showPeriodicExpense: false,
    })

    expect(screen.queryByText('dashboard-page.there-is-no-tasks')).toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-trip-request-action')).toBeInTheDocument()
    expect(screen.queryByText('dashboard-page.new-expense-request-action')).toBeInTheDocument()
    expect(
      screen.queryByText('dashboard-page.new-expense-periodic-request-action'),
    ).not.toBeInTheDocument()
    expect(container.querySelector('.click-to-action__box')).toBeDefined()
  })
})
