import React from 'react';
import ToggleableInputWrapper from '../ToggleableFilters/ToggleableInputWrapper';
import trans from '../../trans';
import { DatePickerField } from '../ui/Form';
import ToggleableFilters from '../ToggleableFilters/ToggleableFilters';
import ToggleableQuickFilters from '../ToggleableFilters/ToggleableQuickFilters';
import ToggleableSearchField from '../ToggleableFilters/ToggleableSearchField';
import ToggleableQuickFiltersContainer from '../ToggleableFilters/ToggleableQuickFiltersContainer';
import { StateTextButton } from '../ui/StateTextButton';
import ToggleableBottomFilters from '../ToggleableFilters/ToggleableBottomFilters';

const QUICK_FILTER_ACCOUNTED = 1;
const QUICK_FILTER_TO_ACCOUNT = 0;

interface Props {
  filters: Record<string, unknown>;
  setFilter: (key: string, value: unknown) => void;
}

function AccountStatementsPageFilters({ filters, setFilter }: Props) {
  const filtersCount = Object.keys(filters).length;

  const bottomFilters = (
    <>
      <ToggleableInputWrapper label={trans('global.filter-period')}>
        <div className='filters__period-container'>
          <DatePickerField
            placeholder={trans('global.datepicker-placeholder')}
            onChange={(value) => setFilter('created_at_from', value)}
            isClearable={true}
            value={filters['created_at_from']}
          />
          <span className='filters__separator'>-</span>
          <DatePickerField
            placeholder={trans('global.datepicker-placeholder')}
            onChange={(value) => setFilter('created_at_to', value)}
            isClearable={true}
            minDate={filters['created_at_from']}
            value={filters['created_at_to']}
          />
        </div>
      </ToggleableInputWrapper>
    </>
  );

  return (
    <form>
      <ToggleableFilters filtersCount={filtersCount}>
        {({ isOpen }) => (
          <>
            <ToggleableQuickFilters>
              <ToggleableSearchField
                value={filters['search']}
                onChange={(value) => {
                  setFilter('search', value);
                }}
              />

              <ToggleableQuickFiltersContainer>
                <StateTextButton
                  onClick={(activate) => {
                    setFilter('closed', activate ? QUICK_FILTER_ACCOUNTED : null);
                  }}
                  active={filters['closed'] === QUICK_FILTER_ACCOUNTED}
                >
                  {trans('my-card.account_quick_filter_accounted')}
                </StateTextButton>

                <StateTextButton
                  onClick={(activate) => {
                    setFilter('closed', activate ? QUICK_FILTER_TO_ACCOUNT : null);
                  }}
                  active={filters['closed'] === QUICK_FILTER_TO_ACCOUNT}
                >
                  {trans('my-card.account_quick_filter_to_account')}
                </StateTextButton>
              </ToggleableQuickFiltersContainer>
            </ToggleableQuickFilters>

            <ToggleableBottomFilters isOpen={isOpen}>{bottomFilters}</ToggleableBottomFilters>
          </>
        )}
      </ToggleableFilters>
    </form>
  );
}

export default AccountStatementsPageFilters;
