import Icon from '../../ui/IconComponent'
import { trans } from '../../../trans'
import React from 'react'

export const TripTypeToggleField = ({ input, disabled }) => {
  const { value, ...rest } = input

  return (
    <div
      className={`border-crossings__toggler ${
        disabled ? 'border-crossings__toggler--disabled' : ''
      }`}
    >
      <label
        htmlFor='trip-national'
        className={`border-crossings__toggler-label border-crossings__toggler-label--first
                ${value ? 'border-crossings__toggler-label--active' : ''}`}
      >
        <Icon type='home' />
        <span>{trans('deductions-widget.national-trip')}</span>
      </label>
      <input
        id='trip-national'
        className='border-crossings__toggler-radio'
        type='radio'
        {...rest}
        value='1'
        disabled={disabled ? 'disabled' : ''}
      />
      <label
        htmlFor='trip-abroad'
        className={`border-crossings__toggler-label border-crossings__toggler-label--last
                ${!value ? 'border-crossings__toggler-label--active' : ''}`}
      >
        <Icon type='globe' />
        <span>{trans('deductions-widget.abroad-trip')}</span>
      </label>
      <input
        id='trip-abroad'
        className='border-crossings__toggler-radio'
        type='radio'
        {...rest}
        value='0'
        disabled={disabled ? 'disabled' : ''}
      />
    </div>
  )
}
