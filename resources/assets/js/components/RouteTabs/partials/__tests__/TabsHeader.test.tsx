import { screen } from '@testing-library/react'
import React from 'react'
import { TabsHeader } from '../TabsHeader'
import { render } from '../../../../../../../internals/testing/test-utils'

const setup = (props) => {
  const { container } = render(<TabsHeader {...props} />)

  return {
    container,
  }
}

describe('components/RouteTabs/partials/TabsHeader.tsx', () => {
  it('should not render any tabs if items are empty', () => {
    const { container } = setup()
    const list = container.querySelectorAll('.tabs__titles li')

    expect(list.length).toBe(0)
  })

  it('should render items that are not disabled', () => {
    const items = [
      {
        id: 1,
        title: 'Lorem',
        enabled: true,
      },
      {
        id: 2,
        title: 'Ipsum',
        enabled: false,
      },
      {
        id: 3,
        title: 'Dolor',
      },
    ]
    const match = { url: `/foo` }

    const { container } = setup({ items, match })
    const list = container.querySelectorAll('.tabs__titles li')

    const firstElement = screen.queryByText(items[0].title)

    expect(list.length).toBe(2)
    expect(screen.queryByText('Lorem')).toBeInTheDocument()
    expect(screen.queryByText('Ipsum')).toBeNull()
    expect(screen.queryByText('Dolor')).toBeInTheDocument()
    expect(firstElement.closest('a').getAttribute('href')).toEqual(`${match.url}/${items[0].id}`)
    expect(screen.queryByText(items[1].title)).toBeNull()
    expect(container.innerHTML).toMatchSnapshot()
  })

  it('should header item have link', () => {
    const items = [
      {
        id: 1,
        title: 'Lorem',
        enabled: true,
      },
    ]
    const match = { url: `/foo` }
    const { container } = setup({ items, match })

    expect(screen.queryByText('Lorem').closest('a').getAttribute('href')).toEqual(
      `${match.url}/${items[0].id}`,
    )
    expect(container.innerHTML).toMatchSnapshot()
  })

  it('should mark active tab', () => {
    const items = [
      {
        id: 1,
        title: 'Lorem',
        enabled: true,
      },
      {
        id: 2,
        title: 'Ipsum',
        enabled: true,
      },
    ]
    const match = { url: `/foo` }
    const { container } = setup({ items, match, activeTab: 2 })
    const list = container.querySelectorAll('.tabs__titles li')
    const activeElement = screen.queryByText(items[1].title)

    expect(list.length).toBe(2)
    expect(screen.queryByText(items[0].title)).toBeInTheDocument()
    expect(activeElement).toBeInTheDocument()
    expect(activeElement.closest('a').getAttribute('href')).toEqual(`${match.url}/${items[1].id}`)
    expect(container.innerHTML).toMatchSnapshot()
  })
})
