import { render } from '../../../../../../internals/testing/test-utils'
import React from 'react'
import RouteTabs from '../RouteTabs'
import { cleanup, screen } from '@testing-library/react'

const setup = (props, config) => {
  const { container } = render(<RouteTabs {...props} />, config)

  return {
    container,
  }
}

describe('components/RouteTabs/RouteTabs.tsx', () => {
  afterEach(cleanup)

  it('should render default tab', () => {
    const items = [
      { id: '1', title: 'Lorem', path: '/lorem', enabled: true, component: <>First component</> },
      { id: '2', title: 'Ipsum', enabled: true, path: '/ipsum', component: <>Second component</> },
    ]
    const defaultTab = '2'

    setup({ items, defaultTab }, { route: '/lorem/1', path: '/lorem' })

    expect(screen.queryByText('Lorem')).toBeInTheDocument()
    expect(screen.queryByText('Ipsum')).toBeInTheDocument()
    expect(screen.queryByText('Second component')).toBeNull()
    expect(screen.queryByText('First component')).toBeInTheDocument()
  })

  it('should render active tab', () => {
    const items = [
      { id: '1', title: 'Lorem', path: '/lorem', enabled: true, component: <>First component</> },
      { id: '2', title: 'Ipsum', enabled: true, path: '/ipsum', component: <>Second component</> },
    ]
    const defaultTab = '2'

    setup({ items, defaultTab }, { route: '/lorem/2', path: '/lorem/2' })

    expect(screen.queryByText('Lorem')).toBeInTheDocument()
    expect(screen.queryByText('Ipsum')).toBeInTheDocument()
    expect(screen.queryByText('Ipsum').closest('.tabs__title--active')).toBeInTheDocument()
    expect(screen.queryByText('Second component')).toBeInTheDocument()
    expect(screen.queryByText('First component')).toBeNull()
  })
})
