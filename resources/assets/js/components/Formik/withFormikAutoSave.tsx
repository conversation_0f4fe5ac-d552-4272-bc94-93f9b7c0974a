import React from 'react'
import { withFormik } from 'formik'
import { omitBy, throttle, isFunction, values } from 'lodash'

const withFormikAutoSave = (formikArgs, handleAutoSave) => (Component) => {
  class FormikAutoSave extends React.Component<any, any> {
    constructor(props) {
      super(props)
      this.throttled = throttle(this.save, 500)
    }

    save(changedFiles) {
      if (isFunction(handleAutoSave) && values(changedFiles).length) {
        handleAutoSave(changedFiles, this.props)
      }
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
      const changedFields = omitBy(
        this.props.values,
        (value, key) => prevProps.values[key] === value,
      )

      this.throttled(changedFields)
    }

    render() {
      return <Component<any, any> {...this.props} />
    }
  }

  return withFormik({ ...formikArgs })(FormikAutoSave)
}

export default withFormikAutoSave
export { withFormikAutoSave }
