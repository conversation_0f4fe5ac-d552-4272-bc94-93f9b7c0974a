import React from 'react';

import { ICellRendererParams } from 'ag-grid-community';

import { TruncateBreakable } from '../../TruncateBreakable';

export interface TextRendererProps extends ICellRendererParams {
  lines?: number;
}

export default function TextRenderer(props: TextRendererProps) {
  const lines = props.lines ?? 1;
  const value = props.valueFormatted || props.value;

  return <TruncateBreakable tooltipAs={'text'} lines={lines} text={value} />;
}
