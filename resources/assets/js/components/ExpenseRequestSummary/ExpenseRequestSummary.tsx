import React from 'react'
import { TableFlatArrayRenderer } from '../ui/AccordionTable'
import {
  TYPE_BACKGROUND_GRADIENT_ROW,
  TYPE_BACKGROUND_PRIMARY_ROW,
  TYPE_DATA_ROW,
} from '../ui/AccordionTable/Row'
import { TYPE_SUMMARY2_CELL, TYPE_SUMMARY_CELL } from '../ui/AccordionTable/Cell'
import { AmountFormatter } from '../AmountFormatter'
import trans from '../../trans'

class ExpenseRequestSummary extends React.Component<any, any> {
  translateData(tableData) {
    const ret = [
      {
        type: TYPE_BACKGROUND_GRADIENT_ROW,
        columns: [
          {
            content: trans('request-summary.accounting-documents'),
            smallMargin: true,
          },
          {
            content: trans('request-summary.expenses'),
          },
          {
            connectLeft: true,
          },
          {
            connectLeft: true,
          },
          {
            content: trans('request-summary.lump-sums'),
          },
          {
            content: trans('request-summary.in-total'),
          },
        ],
      },
      {
        type: TYPE_BACKGROUND_PRIMARY_ROW,
        columns: [
          {
            content: trans('request-summary.payment-method'),
          },
          {
            content: trans('request-summary.cash-and-service-card'),
            tooltip: trans('request-summary.cash-and-service-card-tooltip'),
          },
          {
            content: trans('request-summary.corporate-card-and-transfer'),
          },
          {
            content: trans('request-summary.in-total'),
          },
          {
            content: trans('global.not-applicable'),
          },
          {
            connectUpper: true,
          },
        ],
      },
    ]

    if (!_.isEmpty(tableData)) {
      if (!_.isEmpty(tableData['elements'])) {
        Object.keys(tableData['elements']).forEach((key, i) => {
          const element = tableData['elements'][key]

          ret.push({
            type: TYPE_DATA_ROW,
            columns: [
              {
                content: element['payment_method'],
                type: i === 0 ? TYPE_SUMMARY_CELL : TYPE_SUMMARY2_CELL,
              },
              {
                content: <AmountFormatter amount={element['cash_and_service_card']} />,
                type: i === 0 ? TYPE_SUMMARY_CELL : TYPE_SUMMARY2_CELL,
              },
              {
                content: <AmountFormatter amount={element['corporate_card_and_transfer']} />,
                type: i === 0 ? TYPE_SUMMARY_CELL : TYPE_SUMMARY2_CELL,
              },
              {
                content: <AmountFormatter amount={element['subsum']} />,
                type: i === 0 ? TYPE_SUMMARY_CELL : TYPE_SUMMARY2_CELL,
              },
              {
                content: <AmountFormatter amount={element['credit']} />,
                type: i === 0 ? TYPE_SUMMARY_CELL : TYPE_SUMMARY2_CELL,
              },
              {
                content: <AmountFormatter amount={element['sum']} />,
                type: i === 0 ? TYPE_SUMMARY_CELL : TYPE_SUMMARY2_CELL,
              },
            ],
          })
        })
      }
    }

    return ret
  }

  render() {
    const { tableData } = this.props
    const data = this.translateData(tableData)

    return <TableFlatArrayRenderer data={data} />
  }
}

ExpenseRequestSummary.propTypes = {}

export { ExpenseRequestSummary }
