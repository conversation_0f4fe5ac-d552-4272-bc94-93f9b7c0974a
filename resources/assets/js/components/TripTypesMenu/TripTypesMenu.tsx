import React from 'react'
import Button from '../ui/ButtonComponent'
import Icon from '../ui/IconComponent'
import trans from '../../trans'

const trip_types = [
  {
    name: 'Plane',
    icon: 'plane2',
    timelineIcon: 'plane2_fill',
    type: ['plane_trip'],
    render: true,
  },
  {
    name: 'Car',
    icon: 'car_flat',
    timelineIcon: 'car_fill',
    type: ['private_car_trip', 'company_car_trip', 'rented_car_trip'],
    render: true,
  },
  {
    name: 'Bus',
    icon: 'train_flat',
    timelineIcon: 'train_fill',
    type: ['bus_trip'],
    render: true,
  },

  {
    name: 'Train',
    icon: 'train_flat',
    timelineIcon: 'train_fill',
    type: ['train_trip'],
    render: true,
  },
  {
    name: 'Hotel',
    icon: 'hotel',
    timelineIcon: 'hotel_fill',
    type: ['accomodation'],
    render: true,
  },
  {
    name: 'Private Hotel',
    icon: 'accomodation',
    timelineIcon: 'accomodation_fill',
    type: ['private_accomodation'],
    render: false,
  },
]

export const TripTypesMenu = ({ selected, onChange, onSelect }) => {
  const types = trip_types
    .filter((icon) => {
      return icon.type.indexOf(selected) !== -1 || (selected === null && icon.render)
    })
    .map((trip, index) => {
      return (
        <span key={index} className='timeline-icon' onClick={() => onSelect(trip.type[0])}>
          <Icon type={trip.icon} gradient='true' />
        </span>
      )
    })

  return (
    <div className='icon-select'>
      {types}
      {selected !== null && (
        <Button outline xxs onClick={onChange}>
          {trans('global.change')}
        </Button>
      )}
    </div>
  )
}
