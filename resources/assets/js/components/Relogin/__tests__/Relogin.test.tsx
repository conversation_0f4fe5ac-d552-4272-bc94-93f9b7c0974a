import React from 'react'
import { cleanup, screen } from '@testing-library/react'
import { render } from '../../../../../../internals/testing/test-utils'
import Relogin from '../Relogin'
import { configureStore } from '../../../store'
import { createMemoryHistory } from 'history'
import { relogin } from '../../../store/app/auth'

jest.mock('../../../store/app/auth', () => ({
  ...jest.requireActual('../../../store/app/auth'),
  relogin: jest.fn(),
}))

const setup = (props = {}, restConfig = {}) => {
  return render(<Relogin {...props} />, restConfig)
}

describe('components/Relogin/Relogin.tsx', () => {
  afterEach(cleanup)

  it('should render component', () => {
    const ref = () => Promise.resolve()
    relogin.mockReturnValue(ref)

    const { container } = setup()

    expect(container).toMatchSnapshot()
  })

  it('should call API', () => {
    const hash = 'some-hash'
    const store = configureStore({}, createMemoryHistory())
    const ref = () => Promise.resolve()

    relogin.mockReturnValue(ref)

    jest.spyOn(store, 'dispatch').mockImplementation()

    setup(
      {},
      {
        route: `/relogin/${hash}`,
        path: '/relogin/:slug',
        store,
      },
    )

    expect(relogin).toHaveBeenCalledWith(hash)
    // expect(store.dispatch).toHaveBeenCalledWith(ref);
  })
})
