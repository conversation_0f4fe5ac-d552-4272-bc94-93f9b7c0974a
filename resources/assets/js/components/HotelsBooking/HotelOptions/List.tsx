import React, { Component } from 'react'
import { Scrollbars } from 'react-custom-scrollbars-2'
import Item from './Item'
import { get } from 'lodash'
import Maintenance from '../Maintenance'
import Stars from '../Stars'
import Icon from '../../ui/IconComponent'
import Slider from 'react-slick'
import 'lightbox-react/style.css'
import { trans } from '../../../trans'
import Description from '../Description'
import Gallery from './Gallery'
import TruncateMarkup from 'react-truncate-markup'

const PrevArrow = (props) => {
  return (
    <button
      className='accommodation__hotel-gallery-button accommodation__hotel-gallery-button--prev'
      type='button'
    >
      <Icon type='arrow' onClick={props.onClick} />
    </button>
  )
}

const NextArrow = (props) => {
  return (
    <button
      className='accommodation__hotel-gallery-button accommodation__hotel-gallery-button--next'
      type='button'
    >
      <Icon type='arrow' onClick={props.onClick} />
    </button>
  )
}

const settings = {
  dots: false,
  infinite: false,
  speed: 500,
  slidesToShow: 5,
  nextArrow: <NextArrow />,
  prevArrow: <PrevArrow />,
}

class List extends Component<any, any> {
  constructor(props) {
    super(props)
    this.detailsRef = React.createRef()

    this.state = {
      galleryWidth: 500,
      photoIndex: 0,
      isGalleryOpen: false,
      isDescriptionOpen: false,
    }
  }

  componentDidMount() {
    if (get(this.detailsRef, 'current.offsetWidth', null)) {
      this.setState({ galleryWidth: this.detailsRef.current.offsetWidth })
      window.addEventListener('resize', this.updateGallery)
    }
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.updateGallery)
  }

  updateGallery = () => {
    this.setState({ galleryWidth: this.detailsRef.current.offsetWidth })
  }

  openGallery = (index) => {
    this.setState({ photoIndex: index, isGalleryOpen: true })
  }

  closeGallery = () => {
    this.setState({ isGalleryOpen: false })
  }

  openDescription = (e) => {
    e.preventDefault()
    this.setState({ isDescriptionOpen: true })
  }

  closeDescription = () => {
    this.setState({ isDescriptionOpen: false })
  }

  render() {
    const { offer, selectOfferOption } = this.props

    if (!offer) {
      return null
    }

    const items = offer.options.map((option) => {
      return <Item key={option.uuid} option={option} select={(o) => selectOfferOption(offer, o)} />
    })

    return (
      <div className='accommodation__hotel-details-container'>
        <aside className='accommodation__hotel-details-bar'>
          <div className='accommodation__hotel-stars'>
            <Stars stars={offer.attributes.stars} />
          </div>

          <div className='accommodation__hotel-accessories'>
            <Maintenance mealTypes={offer.mealTypes} />
          </div>
        </aside>

        <div className='accommodation__hotel-details-main' ref={this.detailsRef}>
          <div
            className='accommodation__hotel-details-gallery'
            style={{ width: this.state.galleryWidth + 'px' }}
          >
            <Slider {...settings}>
              {offer.attributes.photos.map((photo, key) => {
                return (
                  <div key={key}>
                    <img
                      src={photo}
                      className='accommodation__hotel-gallery-image'
                      onClick={() => this.openGallery(key)}
                    />
                  </div>
                )
              })}
            </Slider>
          </div>

          <div className='accommodation__hotel-heading'>
            <span className='accommodation__hotel-details-name'>{offer.attributes.name}</span>

            <a
              target='_blank'
              className='accommodation__hotel-details-address'
              href={`https://www.google.com/maps/search/?api=1&query=${offer.attributes.address}`}
            >
              <Icon className='accommodation__hotel-details-address-icon' type='pin_1' />
              <span>{offer.attributes.address}</span>
            </a>
          </div>

          <div className='accommodation__hotel-text'>
            <TruncateMarkup
              lines={2}
              ellipsis={
                <span>
                  ...{' '}
                  <a href='#' onClick={this.openDescription}>
                    {trans('hotels-booking.read-more')}
                  </a>
                </span>
              }
            >
              <div dangerouslySetInnerHTML={{ __html: offer.attributes.description }} />
            </TruncateMarkup>
          </div>

          <Scrollbars style={{ height: '249px' }}>
            <div className='accommodation__hotel-details-packets'>{items}</div>
          </Scrollbars>
        </div>

        <Gallery
          index={this.state.photoIndex}
          isOpen={this.state.isGalleryOpen}
          photos={get(offer, 'attributes.photos', [])}
          onClose={this.closeGallery}
        />

        <Description
          title={offer.attributes.name}
          content={get(offer, 'attributes.description', null)}
          onClose={this.closeDescription}
          isOpen={this.state.isDescriptionOpen}
        />
      </div>
    )
  }
}

export { List }
export default List
