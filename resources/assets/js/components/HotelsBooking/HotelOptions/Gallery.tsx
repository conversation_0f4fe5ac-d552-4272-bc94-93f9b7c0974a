import React, { Component } from 'react'
import { isFunction } from 'lodash'
import PropTypes from 'prop-types'
import Lightbox from 'lightbox-react'

class Gallery extends Component<any, any> {
  constructor(props) {
    super(props)
    this.state = {
      index: 0,
    }
  }

  onClose = () => {
    const { onClose } = this.props
    if (isFunction(onClose)) {
      onClose()
    }
  }

  render() {
    const { photos, isOpen } = this.props
    const { index } = this.state

    if (!isOpen) {
      return null
    }

    return (
      <Lightbox
        mainSrc={photos[index]}
        nextSrc={photos[(index + 1) % photos.length]}
        prevSrc={photos[(index + photos.length - 1) % photos.length]}
        onCloseRequest={this.onClose}
        onMovePrevRequest={() =>
          this.setState({
            index: (index + photos.length - 1) % photos.length,
          })
        }
        onMoveNextRequest={() =>
          this.setState({
            index: (index + 1) % photos.length,
          })
        }
        onAfterOpen={() => {
          this.setState({ index: this.props.index })
        }}
      />
    )
  }
}

Gallery.propTypes = {
  photos: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired,
  onClose: PropTypes.func.isRequired,
}

export default Gallery
export { Gallery }
