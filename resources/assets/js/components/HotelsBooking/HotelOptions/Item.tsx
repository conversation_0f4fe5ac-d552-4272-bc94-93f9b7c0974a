import React, { Component, Fragment } from 'react'
import Icon from '../../ui/IconComponent'
import Button from '../../ui/ButtonComponent'
import { trans } from '../../../trans'
import { MEAL_TYPES, ROOM_TYPES } from '../index'
import classNames from 'classnames'
import { get } from 'lodash'

class Item extends Component<any, any> {
  clickHandler = (e) => {
    const { option, select } = this.props
    e.stopPropagation()
    select(option)
  }

  renderMeal = () => {
    const { option } = this.props

    const mealType = get(MEAL_TYPES, option.attributes.mealType[0], null)

    return (
      <div className='accommodation__hotel-packet-item'>
        {mealType !== 'hotels-booking.meal-RO' && (
          <Fragment>
            <Icon type='restaurant' />
            <span>{trans(!mealType ? 'meal-question' : mealType)}</span>
          </Fragment>
        )}
      </div>
    )
  }

  render() {
    const { option } = this.props

    const className = classNames({
      'accommodation__hotel-packet': true,
      'accommodation__hotel-packet--selected': option.chosen,
    })

    return (
      <div className={className}>
        <div className='accommodation__hotel-packet-items-wrapper'>
          <div className='accommodation__hotel-packet-item'>
            <Icon type='accomodation' />
            <span>{trans(ROOM_TYPES[option.attributes.roomType[0]])}</span>
          </div>

          {this.renderMeal()}
        </div>

        <div className='accommodation__hotel-packet-prices'>
          <span className='accommodation__hotel-price'>{option.amount.formatted}</span>
          <span className='accommodation__hotel-price-details'>
            {trans('hotels-booking.amount-per-night', {
              amount: get(option, 'amountPerNight.formatted', '0,00 zł'),
            })}
          </span>
        </div>
        <Button
          type='button'
          primary
          className='accommodation__hotel-packet-button'
          onClick={this.clickHandler}
        >
          Wybierz
        </Button>
      </div>
    )
  }
}

export { Item }
export default Item
