// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`components/AccountAllowanceForm/AccountAllowanceForm.tsx should render component 1`] = `
<div>
  <div
    class="panel"
  >
    <div
      class="panel__header"
    >
      <h1>
        document.basic-info
      </h1>
    </div>
    <div
      class="panel__content"
    >
      <form>
        <div>
          <div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <p>
                  request.lump-sum-statement
                   
                </p>
              </div>
            </div>
          </div>
          <div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    document.document-number
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <input
                      class="input input--text input--no-border"
                      disabled=""
                      name="uid"
                      placeholder="document.document-number"
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
              <div
                class="xs-2"
              />
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    document.document-date
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <div
                      class="date-picker-field"
                      data-portal-id="portal-1"
                    >
                      <div
                        class="react-datepicker-wrapper"
                      >
                        <div
                          class="react-datepicker__input-container"
                        >
                          <input
                            autocomplete="off"
                            class="input--no-border"
                            disabled=""
                            name="status_settlement_at"
                            placeholder="-"
                            type="text"
                            value=""
                          />
                        </div>
                      </div>
                      <i
                        class="icon icon-calendar date-picker-field__icon"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    means-of-transports.field-label
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <div>
                      <div>
                        <div
                          class="react-select__container--outer"
                        >
                          <div
                            class="react-select__select-container react-select__select-container__is-disabled css-3iigni-container"
                          >
                            <span
                              class="css-1f43avz-a11yText-A11yText"
                              id="react-select-2-live-region"
                            />
                            <span
                              aria-atomic="false"
                              aria-live="polite"
                              aria-relevant="additions text"
                              class="css-1f43avz-a11yText-A11yText"
                            />
                            <div
                              class="react-select__control css-16xfy0z-control"
                            >
                              <div
                                class="react-select__value-container css-1fdsijx-ValueContainer"
                              >
                                <div
                                  class="react-select__placeholder css-1jqq78o-placeholder"
                                  id="react-select-2-placeholder"
                                >
                                  means-of-transports.field-label
                                </div>
                                <div
                                  class="react-select__input css-1h01tm3-Input"
                                  data-value=""
                                >
                                  <input
                                    aria-autocomplete="list"
                                    aria-describedby="react-select-2-placeholder"
                                    aria-expanded="false"
                                    aria-haspopup="true"
                                    autocapitalize="none"
                                    autocomplete="off"
                                    autocorrect="off"
                                    class=""
                                    disabled=""
                                    id="react-select-2-input"
                                    role="combobox"
                                    spellcheck="false"
                                    style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                    tabindex="0"
                                    type="text"
                                    value=""
                                  />
                                </div>
                              </div>
                              <div
                                class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                              >
                                <span
                                  class="react-select__indicator-separator css-894a34-indicatorSeparator"
                                />
                                <div
                                  aria-hidden="true"
                                  class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="css-tj5bde-Svg"
                                    focusable="false"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    width="20"
                                  >
                                    <path
                                      d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                    />
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="xs-2"
              />
              <div
                class="xs-6"
              >
                <div
                  class="form-group--no-label"
                />
              </div>
            </div>
          </div>
          <div
            class="row"
          />
        </div>
      </form>
    </div>
  </div>
</div>
`;

exports[`components/AccountAllowanceForm/AccountAllowanceForm.tsx should render multiple account dimensions 1`] = `
<div>
  <div
    class="panel"
  >
    <div
      class="panel__header"
    >
      <h1>
        document.basic-info
      </h1>
    </div>
    <div
      class="panel__content"
    >
      <form>
        <div>
          <div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <p>
                  request.lump-sum-statement
                   
                </p>
              </div>
            </div>
          </div>
          <div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    document.document-number
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <input
                      class="input input--text input--no-border"
                      disabled=""
                      name="uid"
                      placeholder="document.document-number"
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
              <div
                class="xs-2"
              />
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    document.document-date
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <div
                      class="date-picker-field"
                      data-portal-id="portal-4"
                    >
                      <div
                        class="react-datepicker-wrapper"
                      >
                        <div
                          class="react-datepicker__input-container"
                        >
                          <input
                            autocomplete="off"
                            class="input--no-border"
                            disabled=""
                            name="status_settlement_at"
                            placeholder="-"
                            type="text"
                            value=""
                          />
                        </div>
                      </div>
                      <i
                        class="icon icon-calendar date-picker-field__icon"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    means-of-transports.field-label
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <div>
                      <div>
                        <div
                          class="react-select__container--outer"
                        >
                          <div
                            class="react-select__select-container react-select__select-container__is-disabled css-3iigni-container"
                          >
                            <span
                              class="css-1f43avz-a11yText-A11yText"
                              id="react-select-5-live-region"
                            />
                            <span
                              aria-atomic="false"
                              aria-live="polite"
                              aria-relevant="additions text"
                              class="css-1f43avz-a11yText-A11yText"
                            />
                            <div
                              class="react-select__control css-16xfy0z-control"
                            >
                              <div
                                class="react-select__value-container css-1fdsijx-ValueContainer"
                              >
                                <div
                                  class="react-select__placeholder css-1jqq78o-placeholder"
                                  id="react-select-5-placeholder"
                                >
                                  means-of-transports.field-label
                                </div>
                                <div
                                  class="react-select__input css-1h01tm3-Input"
                                  data-value=""
                                >
                                  <input
                                    aria-autocomplete="list"
                                    aria-describedby="react-select-5-placeholder"
                                    aria-expanded="false"
                                    aria-haspopup="true"
                                    autocapitalize="none"
                                    autocomplete="off"
                                    autocorrect="off"
                                    class=""
                                    disabled=""
                                    id="react-select-5-input"
                                    role="combobox"
                                    spellcheck="false"
                                    style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                    tabindex="0"
                                    type="text"
                                    value=""
                                  />
                                </div>
                              </div>
                              <div
                                class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                              >
                                <span
                                  class="react-select__indicator-separator css-894a34-indicatorSeparator"
                                />
                                <div
                                  aria-hidden="true"
                                  class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="css-tj5bde-Svg"
                                    focusable="false"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    width="20"
                                  >
                                    <path
                                      d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                    />
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="xs-2"
              />
              <div
                class="xs-6"
              >
                <div
                  class="form-group--no-label"
                />
              </div>
            </div>
          </div>
          <div
            class="row"
          >
            <div
              class="xs-6"
            >
              <div
                class="form-group form-group--label-top"
              >
                <span
                  class="form-group__label"
                >
                  foo
                </span>
                <div
                  class="form-group__input-wrapper"
                >
                  <div
                    class=""
                  >
                    <div
                      class="react-select__container--outer"
                    >
                      <div
                        class="react-select__select-container css-b62m3t-container"
                      >
                        <span
                          class="css-1f43avz-a11yText-A11yText"
                          id="react-select-6-live-region"
                        />
                        <span
                          aria-atomic="false"
                          aria-live="polite"
                          aria-relevant="additions text"
                          class="css-1f43avz-a11yText-A11yText"
                        />
                        <div
                          class="react-select__control css-13cymwt-control"
                        >
                          <div
                            class="react-select__value-container css-1fdsijx-ValueContainer"
                          >
                            <div
                              class="react-select__placeholder css-1jqq78o-placeholder"
                              id="react-select-6-placeholder"
                            >
                              account-dimensions.placeholder
                            </div>
                            <div
                              class="react-select__input css-qbdosj-Input"
                              data-value=""
                            >
                              <input
                                aria-autocomplete="list"
                                aria-describedby="react-select-6-placeholder"
                                aria-expanded="false"
                                aria-haspopup="true"
                                autocapitalize="none"
                                autocomplete="off"
                                autocorrect="off"
                                class=""
                                id="react-select-6-input"
                                role="combobox"
                                spellcheck="false"
                                style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                tabindex="0"
                                type="text"
                                value=""
                              />
                            </div>
                          </div>
                          <div
                            class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                          >
                            <span
                              class="react-select__indicator-separator css-1u9des2-indicatorSeparator"
                            />
                            <div
                              aria-hidden="true"
                              class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                            >
                              <svg
                                aria-hidden="true"
                                class="css-tj5bde-Svg"
                                focusable="false"
                                height="20"
                                viewBox="0 0 20 20"
                                width="20"
                              >
                                <path
                                  d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="xs-6"
            >
              <div
                class="form-group form-group--label-top"
              >
                <span
                  class="form-group__label"
                >
                  bar
                </span>
                <div
                  class="form-group__input-wrapper"
                >
                  <div
                    class=""
                  >
                    <div
                      class="react-select__container--outer"
                    >
                      <div
                        class="react-select__select-container css-b62m3t-container"
                      >
                        <span
                          class="css-1f43avz-a11yText-A11yText"
                          id="react-select-7-live-region"
                        />
                        <span
                          aria-atomic="false"
                          aria-live="polite"
                          aria-relevant="additions text"
                          class="css-1f43avz-a11yText-A11yText"
                        />
                        <div
                          class="react-select__control css-13cymwt-control"
                        >
                          <div
                            class="react-select__value-container css-1fdsijx-ValueContainer"
                          >
                            <div
                              class="react-select__placeholder css-1jqq78o-placeholder"
                              id="react-select-7-placeholder"
                            >
                              account-dimensions.placeholder
                            </div>
                            <div
                              class="react-select__input css-qbdosj-Input"
                              data-value=""
                            >
                              <input
                                aria-autocomplete="list"
                                aria-describedby="react-select-7-placeholder"
                                aria-expanded="false"
                                aria-haspopup="true"
                                autocapitalize="none"
                                autocomplete="off"
                                autocorrect="off"
                                class=""
                                id="react-select-7-input"
                                role="combobox"
                                spellcheck="false"
                                style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                tabindex="0"
                                type="text"
                                value=""
                              />
                            </div>
                          </div>
                          <div
                            class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                          >
                            <span
                              class="react-select__indicator-separator css-1u9des2-indicatorSeparator"
                            />
                            <div
                              aria-hidden="true"
                              class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                            >
                              <svg
                                aria-hidden="true"
                                class="css-tj5bde-Svg"
                                focusable="false"
                                height="20"
                                viewBox="0 0 20 20"
                                width="20"
                              >
                                <path
                                  d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
`;

exports[`components/AccountAllowanceForm/AccountAllowanceForm.tsx should render multiple account dimensions 2`] = `
<div>
  <div
    class="panel"
  >
    <div
      class="panel__header"
    >
      <h1>
        document.basic-info
      </h1>
    </div>
    <div
      class="panel__content"
    >
      <form>
        <div>
          <div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <p>
                  request.lump-sum-statement
                   
                </p>
              </div>
            </div>
          </div>
          <div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    document.document-number
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <input
                      class="input input--text input--no-border"
                      disabled=""
                      name="uid"
                      placeholder="document.document-number"
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
              <div
                class="xs-2"
              />
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    document.document-date
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <div
                      class="date-picker-field"
                      data-portal-id="portal-4"
                    >
                      <div
                        class="react-datepicker-wrapper"
                      >
                        <div
                          class="react-datepicker__input-container"
                        >
                          <input
                            autocomplete="off"
                            class="input--no-border"
                            disabled=""
                            name="status_settlement_at"
                            placeholder="-"
                            type="text"
                            value=""
                          />
                        </div>
                      </div>
                      <i
                        class="icon icon-calendar date-picker-field__icon"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="row"
            >
              <div
                class="read-only xs-4"
              >
                <div
                  class="form-group form-group--label-top"
                >
                  <span
                    class="form-group__label"
                  >
                    means-of-transports.field-label
                  </span>
                  <div
                    class="form-group__input-wrapper"
                  >
                    <div>
                      <div>
                        <div
                          class="react-select__container--outer"
                        >
                          <div
                            class="react-select__select-container react-select__select-container__is-disabled css-3iigni-container"
                          >
                            <span
                              class="css-1f43avz-a11yText-A11yText"
                              id="react-select-5-live-region"
                            />
                            <span
                              aria-atomic="false"
                              aria-live="polite"
                              aria-relevant="additions text"
                              class="css-1f43avz-a11yText-A11yText"
                            />
                            <div
                              class="react-select__control css-16xfy0z-control"
                            >
                              <div
                                class="react-select__value-container css-1fdsijx-ValueContainer"
                              >
                                <div
                                  class="react-select__placeholder css-1jqq78o-placeholder"
                                  id="react-select-5-placeholder"
                                >
                                  means-of-transports.field-label
                                </div>
                                <div
                                  class="react-select__input css-1h01tm3-Input"
                                  data-value=""
                                >
                                  <input
                                    aria-autocomplete="list"
                                    aria-describedby="react-select-5-placeholder"
                                    aria-expanded="false"
                                    aria-haspopup="true"
                                    autocapitalize="none"
                                    autocomplete="off"
                                    autocorrect="off"
                                    class=""
                                    disabled=""
                                    id="react-select-5-input"
                                    role="combobox"
                                    spellcheck="false"
                                    style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                    tabindex="0"
                                    type="text"
                                    value=""
                                  />
                                </div>
                              </div>
                              <div
                                class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                              >
                                <span
                                  class="react-select__indicator-separator css-894a34-indicatorSeparator"
                                />
                                <div
                                  aria-hidden="true"
                                  class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="css-tj5bde-Svg"
                                    focusable="false"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    width="20"
                                  >
                                    <path
                                      d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                    />
                                  </svg>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="xs-2"
              />
              <div
                class="xs-6"
              >
                <div
                  class="form-group--no-label"
                />
              </div>
            </div>
          </div>
          <div
            class="row"
          >
            <div
              class="xs-6"
            >
              <div
                class="form-group form-group--label-top"
              >
                <span
                  class="form-group__label"
                >
                  foo
                </span>
                <div
                  class="form-group__input-wrapper"
                >
                  <div
                    class=""
                  >
                    <div
                      class="react-select__container--outer"
                    >
                      <div
                        class="react-select__select-container css-b62m3t-container"
                      >
                        <span
                          class="css-1f43avz-a11yText-A11yText"
                          id="react-select-6-live-region"
                        />
                        <span
                          aria-atomic="false"
                          aria-live="polite"
                          aria-relevant="additions text"
                          class="css-1f43avz-a11yText-A11yText"
                        />
                        <div
                          class="react-select__control css-13cymwt-control"
                        >
                          <div
                            class="react-select__value-container css-1fdsijx-ValueContainer"
                          >
                            <div
                              class="react-select__placeholder css-1jqq78o-placeholder"
                              id="react-select-6-placeholder"
                            >
                              account-dimensions.placeholder
                            </div>
                            <div
                              class="react-select__input css-qbdosj-Input"
                              data-value=""
                            >
                              <input
                                aria-autocomplete="list"
                                aria-describedby="react-select-6-placeholder"
                                aria-expanded="false"
                                aria-haspopup="true"
                                autocapitalize="none"
                                autocomplete="off"
                                autocorrect="off"
                                class=""
                                id="react-select-6-input"
                                role="combobox"
                                spellcheck="false"
                                style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                tabindex="0"
                                type="text"
                                value=""
                              />
                            </div>
                          </div>
                          <div
                            class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                          >
                            <span
                              class="react-select__indicator-separator css-1u9des2-indicatorSeparator"
                            />
                            <div
                              aria-hidden="true"
                              class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                            >
                              <svg
                                aria-hidden="true"
                                class="css-tj5bde-Svg"
                                focusable="false"
                                height="20"
                                viewBox="0 0 20 20"
                                width="20"
                              >
                                <path
                                  d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="xs-6"
            >
              <div
                class="form-group form-group--label-top"
              >
                <span
                  class="form-group__label"
                >
                  bar
                </span>
                <div
                  class="form-group__input-wrapper"
                >
                  <div
                    class=""
                  >
                    <div
                      class="react-select__container--outer"
                    >
                      <div
                        class="react-select__select-container css-b62m3t-container"
                      >
                        <span
                          class="css-1f43avz-a11yText-A11yText"
                          id="react-select-7-live-region"
                        />
                        <span
                          aria-atomic="false"
                          aria-live="polite"
                          aria-relevant="additions text"
                          class="css-1f43avz-a11yText-A11yText"
                        />
                        <div
                          class="react-select__control css-13cymwt-control"
                        >
                          <div
                            class="react-select__value-container css-1fdsijx-ValueContainer"
                          >
                            <div
                              class="react-select__placeholder css-1jqq78o-placeholder"
                              id="react-select-7-placeholder"
                            >
                              account-dimensions.placeholder
                            </div>
                            <div
                              class="react-select__input css-qbdosj-Input"
                              data-value=""
                            >
                              <input
                                aria-autocomplete="list"
                                aria-describedby="react-select-7-placeholder"
                                aria-expanded="false"
                                aria-haspopup="true"
                                autocapitalize="none"
                                autocomplete="off"
                                autocorrect="off"
                                class=""
                                id="react-select-7-input"
                                role="combobox"
                                spellcheck="false"
                                style="opacity: 1; width: 100%; grid-area: 1 / 2; min-width: 2px; border: 0px; margin: 0px; outline: 0; padding: 0px;"
                                tabindex="0"
                                type="text"
                                value=""
                              />
                            </div>
                          </div>
                          <div
                            class="react-select__indicators-container css-1hb7zxy-IndicatorsContainer"
                          >
                            <span
                              class="react-select__indicator-separator css-1u9des2-indicatorSeparator"
                            />
                            <div
                              aria-hidden="true"
                              class="react-select__dropdown-indicator css-1xc3v61-indicatorContainer"
                            >
                              <svg
                                aria-hidden="true"
                                class="css-tj5bde-Svg"
                                focusable="false"
                                height="20"
                                viewBox="0 0 20 20"
                                width="20"
                              >
                                <path
                                  d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"
                                />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
`;
