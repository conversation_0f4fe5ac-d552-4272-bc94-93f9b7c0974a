import PropTypes from 'prop-types'
import React from 'react'
import classNames from 'classnames'

import Icon from '../../components/ui/IconComponent'

class SummaryRow extends React.Component<any, any> {
  constructor(props) {
    super(props)

    this.state = {
      status: 'is-close',
    }
  }

  toggle = () => {
    this.setState((state) => (state.status === 'close' ? { status: 'open' } : { status: 'close' }))
  }

  render() {
    const TimelineClasses = classNames({
      'is-open': this.state.status === 'open',
      'is-close': this.state.status === 'close',
      'accordion table-accordion__accordion': true,
    })

    return (
      <div className={TimelineClasses}>
        <div className='accordion__bar table-accordion__bar'>
          <div className='table-accordion__row row'>
            <div className='table-accordion__col xs-5'>{this.props.title}</div>
            <div className='table-accordion__col xs-3 is-allign-end'>
              {this.props.warning && <Icon className='is-color-warning' type='warning' />}
              {this.props.summaryPrice} {this.props.currency}
            </div>
            <div className='table-accordion__col xs-3'>
              {this.props.unitPrice && (
                <span>
                  {this.props.unitPrice} {this.props.currency}/{this.props.unit}
                </span>
              )}
            </div>
            <div className='xs-1 table-accordion__row-icon'>
              <Icon onClick={this.toggle} type='arrow' />
            </div>
          </div>
        </div>

        <div className='accordion__content'>{this.props.children}</div>
      </div>
    )
  }
}

SummaryRow.propTypes = {
  title: PropTypes.string.isRequired,
  summaryPrice: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  unitPrice: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  unit: PropTypes.string,
  currency: PropTypes.string,
  warning: PropTypes.bool,
}

SummaryRow.defaultProps = {
  currency: 'PLN',
  warning: false,
  unit: 'noc',
}

export default SummaryRow
