import { useSelector } from 'react-redux';

import useMyCardDataSource from '../../../hooks/useMyCardDataSource';
import { getUserAssistantOrCurrentUserLink } from '../../../store/app/user-profile';
import { Statement } from '../../../types/statements';

export default function useStatements() {
  const link = useSelector(getUserAssistantOrCurrentUserLink('statements'));

  return useMyCardDataSource<Statement>(link?.href);
}
