import React from 'react';

import moment from 'moment/moment';

import trans from '../../trans';
import ToggleableBottomFilters from '../ToggleableFilters/ToggleableBottomFilters';
import ToggleableFilters from '../ToggleableFilters/ToggleableFilters';
import ToggleableInputWrapper from '../ToggleableFilters/ToggleableInputWrapper';
import ToggleableQuickFilters from '../ToggleableFilters/ToggleableQuickFilters';
import ToggleableQuickFiltersContainer from '../ToggleableFilters/ToggleableQuickFiltersContainer';
import ToggleableSearchField from '../ToggleableFilters/ToggleableSearchField';
import { DatePickerField } from '../ui/Form';
import { StateTextButton } from '../ui/StateTextButton';

interface Props {
  filters: Record<string, unknown>;
  setFilter: (key: string, value: unknown) => void;
}

function StatementsPageFilters({ filters, setFilter }: Props) {
  const filtersCount = Object.keys(filters).length;

  const bottomFilters = (
    <>
      <ToggleableInputWrapper label={trans('global.filter-period')}>
        <div className='filters__period-container'>
          <DatePickerField
            placeholder={trans('global.datepicker-placeholder')}
            onChange={(value) => setFilter('range_from', moment(value))}
            isClearable={true}
            value={filters['range_from']}
          />
          <span className='filters__separator'>-</span>
          <DatePickerField
            placeholder={trans('global.datepicker-placeholder')}
            onChange={(value) => setFilter('range_to', moment(value))}
            isClearable={true}
            minDate={filters['range_from']}
            value={filters['range_to']}
          />
        </div>
      </ToggleableInputWrapper>
    </>
  );

  return (
    <form>
      <ToggleableFilters filtersCount={filtersCount}>
        {({ isOpen }) => (
          <>
            <ToggleableQuickFilters>
              <ToggleableSearchField
                value={filters['search']}
                onChange={(value) => {
                  setFilter('search', value);
                }}
              />

              <ToggleableQuickFiltersContainer>
                <StateTextButton
                  onClick={(activate) => {
                    setFilter('closed', activate ? '0' : null);
                  }}
                  active={filters['closed'] === '0'}
                >
                  {trans('my-card.statement_quick_filter_opened')}
                </StateTextButton>

                <StateTextButton
                  onClick={(activate) => {
                    setFilter('closed', activate ? '1' : null);
                  }}
                  active={filters['closed'] === '1'}
                >
                  {trans('my-card.statement_quick_filter_closed')}
                </StateTextButton>
              </ToggleableQuickFiltersContainer>
            </ToggleableQuickFilters>

            <ToggleableBottomFilters isOpen={isOpen}>{bottomFilters}</ToggleableBottomFilters>
          </>
        )}
      </ToggleableFilters>
    </form>
  );
}

export default StatementsPageFilters;
