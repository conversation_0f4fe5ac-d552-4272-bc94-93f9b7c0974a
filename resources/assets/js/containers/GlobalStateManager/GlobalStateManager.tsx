import PropTypes from 'prop-types'
import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'

class GlobalStateManagerBase extends React.Component<any, any> {
  render() {
    const { children, currentUser } = this.props

    const renderProps = {
      currentUser,
    }

    return children(renderProps)
  }
}

GlobalStateManagerBase.propTypes = {
  children: PropTypes.func.isRequired,
}

const mapDispatchToProps = (dispatch) => {
  return bindActionCreators({}, dispatch)
}

const mapStateToProps = (state) => ({
  currentUser: state.get('global').get('currentUser'),
})

const GlobalStateManager = connect(mapStateToProps, mapDispatchToProps)(GlobalStateManagerBase)

export { GlobalStateManager }
export default GlobalStateManager
