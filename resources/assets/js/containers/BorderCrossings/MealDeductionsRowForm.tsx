import React from 'react'
import { connect } from 'react-redux'
import { fromJS } from 'immutable'
import { ExpenseRowForm as Form } from '../../components/BorderCrossings/MealDeductions/ExpenseRowForm'
import { compose } from 'redux'
import { createAutoSaveForm, getFormValues } from '../../utils/forms'
import { save as saveExpense } from '../../store/app/deductions/meal-deductions'
import { get } from 'lodash'

export const save = (name, value, dispatch, props) => {
  const { expense, request } = props
  return dispatch(saveExpense(request)(expense, { [name]: value }))
}

const AutoSaveForm = createAutoSaveForm(Form, {
  save,
  timeout: 500,
})

const mapStateToProps = (state, props) => {
  const { form, expense } = props
  return {
    initialValues: fromJS({
      breakfast: get(expense, 'values.breakfast', false),
      breakfast_locked: get(expense, 'values.breakfast_locked', false),
      dinner: get(expense, 'values.dinner', false),
      lunch: get(expense, 'values.lunch', false),
    }),
    form,
    data: getFormValues(form, state),
  }
}

const withConnect = connect(mapStateToProps)

const MealDeductionsRowForm = compose(withConnect)(AutoSaveForm)

export { MealDeductionsRowForm }
export default MealDeductionsRowForm
