import PropTypes from 'prop-types'
import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import {
  reset,
  fetchDocumentElementGroups,
  getDocumentElementGroups,
  changeElementOfType,
  getIsLoading,
  getIsLoaded,
  addElementOfType,
  deleteElementOfType,
  changeAmountOfElementSuggested,
  getGroupSums,
  calculateSumsWithType,
  getRemainingSum,
  setDocumentElementGroups,
} from '../../store/app/document-element-groups'

class DocumentElementGroupsManagerBase extends React.Component<any, any> {
  componentDidMount() {
    const {
      reset,
      setDocumentElementGroups,
      groups,
      socketSubscribe,
      document: { id: documentId },
    } = this.props

    reset()

    setDocumentElementGroups(groups[0]['documentElements'])
    socketSubscribe(
      'private',
      `App.Document.${documentId}`,
      'listen',
    )('.App\\Events\\DocumentElementChanged', (groups) => {
      setDocumentElementGroups(groups[0]['documentElements'])
    })
  }

  componentWillUnmount() {
    const { socketLeave } = this.props
    const {
      document: { id: documentId },
    } = this.props

    socketLeave(`App.Document.${documentId}`)
  }

  handleSave(travelElement, expenseGroup, expense) {
    // const {addElementOfType, changeElementOfType, deleteElementOfType, document} = this.props;
    //
    // const {gross} = data;
    // const floatGross = parseFloat(gross);
    // console.log(element, type, data);
    //
    // data = {
    //     type_id: type['id'],
    //     request_element_id: null,
    //     ...data
    // }
    //
    // if (element.id === null) {
    //     return addElementOfType(document, element, type, {
    //         ...data,
    //         type_id: type['id'],
    //     });
    // } else if (floatGross === 0 || isNaN(floatGross)) {
    //     return deleteElementOfType(document, element);
    // } else if (element.id !== null) {
    //     return changeElementOfType(document, element, type, data);
    // }

    return new Promise((resolve) => resolve())
  }

  handleChangeGross(amount) {
    const { document, changeAmountOfElementSuggested } = this.props
    const { suggested_document_element_type } = document

    if (suggested_document_element_type) {
      clearTimeout(this.handleChangeGrossTimeout)

      this.handleChangeGrossTimeout = setTimeout(() => {
        changeAmountOfElementSuggested(amount, suggested_document_element_type, document)
      }, 1500)
    }
  }

  render() {
    const {
      children,
      items,
      isLoading,
      isLoaded,
      changeAmountOfElementSuggested,
      groupSums,
      calculateSumsWithType,
      remainingSum,
      deleteElementOfType,
    } = this.props
    const renderProps = {
      documentElementGroups: items,
      handleExpenseSave: this.handleSave.bind(this),
      handleChangeGross: this.handleChangeGross.bind(this),
      areDocumentElementGroupsLoading: isLoading,
      areDocumentElementGroupsLoaded: isLoaded,
      changeAmountOfElementSuggested,
      groupSums,
      calculateSumsWithType,
      remainingSum,
      deleteElementOfType,
    }

    return children(renderProps)
  }
}

DocumentElementGroupsManagerBase.propTypes = {
  children: PropTypes.func.isRequired,
  document: PropTypes.object.isRequired,
}

const mapDispatchToProps = (dispatch) => {
  return bindActionCreators(
    {
      reset,
      fetchDocumentElementGroups,
      changeElementOfType,
      addElementOfType,
      deleteElementOfType,
      changeAmountOfElementSuggested,
      calculateSumsWithType,
      setDocumentElementGroups,
    },
    dispatch,
  )
}

const mapStateToProps = (state) => ({
  items: getDocumentElementGroups(state),
  isLoading: getIsLoading(state),
  isLoaded: getIsLoaded(state),
  groupSums: getGroupSums(state),
  remainingSum: getRemainingSum(state),
})

const DocumentElementGroupsManager = connect(
  mapStateToProps,
  mapDispatchToProps,
)(DocumentElementGroupsManagerBase)

export { DocumentElementGroupsManager }
export default DocumentElementGroupsManager
