import React from 'react'
import classNames from 'classnames'
import PropTypes from 'prop-types'

export const DocumentPreview = ({ className, sticky, img, ...props }) => {
  const DocumentClasses = classNames(className, {
    'document-preview': true,
    'document-preview--sticky': sticky,
  })

  return (
    <div className={DocumentClasses}>
      <div className='document-preview__thumbnail'>
        <img src={img} {...props} />
      </div>

      <div className='document-preview__blank'>
        <img src={img} />
      </div>
    </div>
  )
}

DocumentPreview.propTypes = {
  className: PropTypes.string,
  img: PropTypes.any.isRequired,
  sticky: PropTypes.bool,
}
