// initial state
import APIClient from '../../../services/APIClient'

const getInitialState = () => {
  return {
    travels: {
      data: [],
    },
  }
}

// constants
export const RESET = 'expenses-commute::reset'
export const SET_TRAVELS = 'expenses-commute::set-travels'
export const CHANGE_ACCESS = 'expenses-commute::change-access'
export const MOUNT_POINT = 'expenses-commute'

// reducer
export const reducer = (state = getInitialState(), action) => {
  switch (action.type) {
    case RESET:
      return getInitialState()
    case SET_TRAVELS:
      return {
        ...state,
        travels: {
          ...state.travels,
          data: action.payload,
        },
      }
    case CHANGE_ACCESS:
      const { key, field, value } = action.payload
      const travels = state.travels.data.map((item, index) => {
        if (index !== key) {
          return item
        }

        return {
          ...item,
          [field]: value,
        }
      })

      return {
        ...state,
        travels: {
          data: travels,
        },
      }
    default:
      return state
  }
}

// actions
export const reset = () => (dispatch) => {
  dispatch({
    type: RESET,
  })
}

export const setTravels = (data) => (dispatch) => {
  dispatch({
    type: SET_TRAVELS,
    payload: data,
  })
}

export const changeAccess = (request, travel, key, field, value) => (dispatch) => {
  dispatch({
    type: CHANGE_ACCESS,
    payload: { key, field, value },
  })

  const values = { ...travel, [field]: value }

  if (travel.type === 'plane_trip') {
    APIClient.updatePlaneTrip(request.slug, travel.id, values)
  } else {
    APIClient.updateTrainTrip(request.slug, travel.id, values)
  }
}

// selectors
export const getState = (state) => {
  return state.get(MOUNT_POINT)
}

export const getTravelsData = (state) => {
  return getState(state).travels
}

export const getTravels = (state) => {
  return getTravelsData(state).data
}
