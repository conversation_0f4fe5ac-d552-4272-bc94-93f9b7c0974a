import React from 'react'
import { connect } from 'react-redux'
import { bindActionCreators } from 'redux'
import { get } from 'lodash'
import APIClient from '../../../services/APIClient'
import uuid from '../../../utils/uuid'
import { BorderCrossing as BorderCrossingModel } from '../../../models/BorderCrossing'
import arrayMove from 'array-move'
import moment from 'moment'
import { config } from '../../../config'
import { removeDrafts } from '../target-points'
import { changeRequestValue } from '../trip-request'
import {
  fetchRequest,
  reset as resetRequestMileageAllowance,
  updateBorderCrossingsState,
} from '../request-mileage-allowance'

export const MODULE_MOUNT_POINT = 'border-crossings'

const RESET = MODULE_MOUNT_POINT + '::reset'
const SET = MODULE_MOUNT_POINT + '::set'
const REMOVE = MODULE_MOUNT_POINT + '::remove-crossing'
const INSERT = MODULE_MOUNT_POINT + '::insert-crossing'
const UPDATE = MODULE_MOUNT_POINT + '::update-crossing'
const UPDATE_CROSSING = MODULE_MOUNT_POINT + '::update-target'
const DESTROY = MODULE_MOUNT_POINT + '::destroy'
const SET_LOADING = MODULE_MOUNT_POINT + '::set-loading'
const SET_STEP = MODULE_MOUNT_POINT + '::set-step'
const SET_STEP_READ = MODULE_MOUNT_POINT + '::set-step-read'
const SET_NATIONAL_TRIP = MODULE_MOUNT_POINT + '::set-national-trip'
const REORDER = MODULE_MOUNT_POINT + '::reorder'
const SET_CROSSINGS = MODULE_MOUNT_POINT + '::set-crossings'

const getInitialState = () => {
  return {
    crossings: [],
    step: false,
    countries: [],
    nationalTrip: null,
    loading: true,
    initialized: false,
  }
}

// actions Creators
const reset = () => (dispatch) => {
  dispatch({
    type: RESET,
  })
}

const destroy = () => (dispatch) => {
  dispatch({
    type: DESTROY,
  })
}

const set = (data) => (dispatch) => {
  dispatch({
    type: SET,
    payload: {
      crossings: data.crossings,
      countries: data.countries,
      step: data.step,
      nationalTrip: data.nationalTrip,
    },
  })
}

const remove = (request) => (crossing) => (dispatch, getState) => {
  dispatch(setLoading(true))

  dispatch({
    type: REMOVE,
    payload: {
      crossing,
    },
  })

  if (crossing.draft) {
    dispatch(setLoading(false))
    return
  }

  return APIClient.removeBorderCrossing(request.slug, crossing.id).then((response) => {
    dispatch(setLoading(false))
    dispatch(updateWeight(request, getCrossings(getState())))
  })
}

const insert = (index) => (dispatch) => {
  dispatch({
    type: INSERT,
    payload: {
      index: index + 1, //always after trip-start
    },
  })
}

const update = (cid, crossing) => (dispatch) => {
  dispatch({
    type: UPDATE,
    payload: {
      cid,
      crossing,
    },
  })
}

const updateCrossing = (cid, date, target) => (dispatch) => {
  dispatch({
    type: UPDATE_CROSSING,
    payload: {
      cid,
      date,
      target,
    },
  })
}

const setLoading = (state) => (dispatch) => {
  dispatch({
    type: SET_LOADING,
    payload: { state },
  })
}

export const save = (request) => (crossing, fields) => (dispatch, getState) => {
  let promise

  if (crossing.draft) {
    promise = APIClient.createBorderCrossing(request.slug, fields)
  } else {
    promise = APIClient.updateBorderCrossing(request.slug, crossing.id, fields)
  }

  return new Promise((resolve, reject) => {
    promise
      .then((response) => {
        if (crossing.draft) {
          dispatch(update(crossing.cid, response.data))
        } else {
          const { date, target } = response.data[0]
          dispatch(updateCrossing(crossing.cid, date, target))
        }

        dispatch(updateWeight(request, getCrossings(getState())))
        resolve(response)
      })
      .catch((response) => {
        reject(response)
      })
  })
}

const fetch = (request) => () => (dispatch) => {
  APIClient.getBorderCrossings(request.slug).then((response) => {
    dispatch(
      set({
        crossings: response.data[0].crossings,
        step: response.data[0].border_crossing_state,
        nationalTrip: response.data[0].national_trip,
        countries: response.data,
      }),
    )
    dispatch(changeRequestValue(['border_crossing_state'], response.data[0].border_crossing_state))
  })
}

const setStep = (request) => (step, crossingsChanged) => (dispatch) => {
  dispatch(setLoading(true))

  if (step) {
    dispatch(removeDrafts())
  }

  APIClient.changeDeductionsWidgetState(request.slug, {
    crossingsChanged: crossingsChanged ? crossingsChanged : false,
    state: step,
  })
    .then((response) => {
      dispatch({
        type: SET_STEP,
        payload: { step },
      })

      dispatch(updateBorderCrossingsState(response.data[0].state))
      if (!step) {
        dispatch(reset())
        dispatch(fetch(request)())
      } else {
        dispatch(setLoading(false))
      }
    })
    .catch(() => {
      dispatch(setLoading(false))
    })
}

const setStepReadOnly = (request) => (step) => (dispatch) => {
  dispatch({
    type: SET_STEP_READ,
    payload: { step },
  })
}

const setNationalTrip = (request) => (value) => (dispatch) => {
  dispatch(setLoading(true))

  return APIClient.updateRequest(request.slug, {
    national_trip: value,
    national_trip_value_confirmed: true,
  })
    .then((response) => {
      dispatch({
        type: SET_NATIONAL_TRIP,
        payload: { value },
      })

      dispatch(setLoading(false))

      dispatch(resetRequestMileageAllowance())
      dispatch(fetchRequest(request.slug))
    })
    .catch(() => {
      dispatch(setLoading(false))
    })
}

const updateWeight = (request, crossings) => (dispatch) => {
  const dateOnly = (item) => item.date
  const toUnix = (item) => moment(item.date, config.apiDateTimeFormat).unix()
  const sortByDate = (a, b) => toUnix(a) - toUnix(b)
  const sorted = crossings.filter(dateOnly).sort(sortByDate)

  dispatch({
    type: SET_CROSSINGS,
    payload: {
      crossings: sorted,
    },
  })

  const order = sorted.map((crossing, index) => {
    return { id: crossing.id, weight: index }
  })

  APIClient.updateBorderCrossingsWeights(request, order)
}

const reorder = (request) => (source, destination) => (dispatch, getState) => {
  dispatch({
    type: REORDER,
    payload: {
      source,
      destination,
    },
  })

  const crossings = getCrossings(getState())
  const order = crossings.map((crossing, index) => {
    return { id: crossing.id, weight: index }
  })

  APIClient.updateBorderCrossingsWeights(request, order)
}

//actions
const resetAction = () => {
  return getInitialState()
}

const destroyAction = (state) => {
  delete state[MODULE_MOUNT_POINT]
  return state
}

const setAction = (state, payload) => {
  return {
    ...state,
    crossings: payload.crossings,
    countries: payload.countries,
    loading: false,
    step: payload.step,
    nationalTrip: payload.nationalTrip,
    initialized: true,
  }
}

const removeAction = (state, payload) => {
  const crossings = state.crossings.filter((c) => c.id !== payload.crossing.id)
  return {
    ...state,
    crossings,
  }
}

const insertAction = (state, payload) => {
  let crossings = [...state.crossings]
  const crossing = new BorderCrossingModel({ id: null, draft: true, cid: uuid() })
  crossings.splice(payload.index, 0, crossing)

  return {
    ...state,
    crossings,
  }
}

const updateAction = (state, payload) => {
  let crossings = [...state.crossings]
  const index = crossings.findIndex((c) => c.cid === payload.cid)
  crossings[index] = {
    cid: payload.cid,
    ...payload.crossing,
  }

  return {
    ...state,
    crossings,
  }
}

const updateCrossingAction = (state, payload) => {
  return {
    ...state,
    crossings: state.crossings.map((crossing) => {
      if (crossing.cid === payload.cid) {
        return {
          ...crossing,
          target: payload.target !== undefined ? payload.target : crossing.target,
          date: payload.date !== undefined ? payload.date : crossing.date,
        }
      }

      return crossing
    }),
  }
}

const setLoadingAction = (state, payload) => {
  return {
    ...state,
    loading: payload.state,
  }
}

const setStepAction = (state, payload) => {
  return {
    ...state,
    step: payload.step,
  }
}

const setNationalTripAction = (state, payload) => {
  return {
    ...state,
    nationalTrip: payload.value,
  }
}

const setCrossingsAction = (state, payload) => {
  return {
    ...state,
    crossings: payload.crossings,
  }
}

const reorderAction = (state, payload) => {
  let crossings = [...state.crossings]

  //take out trip start and trip end
  const tripStart = crossings[0]
  const tripEnd = crossings[crossings.length - 1]

  crossings = crossings.filter((c) => c.id !== 'trip-start' && c.id !== 'trip-end')
  crossings = arrayMove(crossings, payload.source, payload.destination)

  //put back trip start and trip end
  crossings.splice(0, 0, tripStart)
  crossings.push(tripEnd)

  return {
    ...state,
    crossings,
  }
}

const actions = {
  [RESET]: resetAction,
  [SET]: setAction,
  [REMOVE]: removeAction,
  [INSERT]: insertAction,
  [UPDATE]: updateAction,
  [UPDATE_CROSSING]: updateCrossingAction,
  [DESTROY]: destroyAction,
  [SET_LOADING]: setLoadingAction,
  [SET_STEP]: setStepAction,
  [SET_STEP_READ]: setStepAction,
  [SET_NATIONAL_TRIP]: setNationalTripAction,
  [REORDER]: reorderAction,
  [SET_CROSSINGS]: setCrossingsAction,
}

//selectors
const getState = (state) => {
  return state.get(MODULE_MOUNT_POINT)
}

const getCrossings = (state) => {
  return get(getState(state), 'crossings', []).map((crossing) => {
    if (!get(crossing, 'cid', null)) {
      crossing.cid = crossing.id
    }
    return new BorderCrossingModel(crossing)
  })
}

const isLoading = (state) => {
  return get(getState(state), 'loading', true)
}

const isInitialized = (state) => {
  return get(getState(state), 'initialized', true)
}

const getStep = (state) => {
  return get(getState(state), 'step', false)
}

const getNationalTrip = (state) => {
  return get(getState(state), 'nationalTrip', true)
}

export const reducer = (state = getInitialState(), action) => {
  if (actions.hasOwnProperty(action.type)) {
    return actions[action.type](state, action.payload)
  }
  return state
}

const borderCrossings =
  (
    resetOnMount = false,
    fetchOnMount = false,
    destroyOnUnmount = false,
    listenNationalTrip = false,
  ) =>
  (Component) => {
    class BorderCrossingsHOC extends React.PureComponent<any, any> {
      componentDidMount() {
        const { crossings } = this.props
        if (resetOnMount) {
          crossings.actions.reset()
        }

        if (fetchOnMount) {
          crossings.actions.fetch()
        }
      }

      componentDidUpdate(prevProps, prevState, prevContext) {
        const { crossings } = this.props

        if (
          fetchOnMount &&
          listenNationalTrip &&
          crossings.selectors.nationalTrip !== prevProps.crossings.selectors.nationalTrip &&
          prevProps.crossings.selectors.nationalTrip !== null
        ) {
          crossings.actions.fetch()
        }
      }

      componentWillUnmount() {
        const { crossings } = this.props
        if (destroyOnUnmount) {
          crossings.actions.destroy()
        }
      }

      render() {
        return <Component<any, any> {...this.props} />
      }
    }

    const mapStateToProps = (state) => ({
      crossings: getCrossings(state),
      isLoading: isLoading(state),
      step: getStep(state),
      nationalTrip: getNationalTrip(state),
      isInitialized: isInitialized(state),
    })

    const mapDispatchToProps = (dispatch, props) => {
      return bindActionCreators(
        {
          reset,
          set,
          insert,
          destroy,
          remove: remove(props.request),
          save: save(props.request),
          fetch: fetch(props.request),
          setStep: setStep(props.request),
          setStepReadOnly: setStepReadOnly(props.request),
          setNationalTrip: setNationalTrip(props.request),
          reorder: reorder(props.request),
        },
        dispatch,
      )
    }

    const mergedProps = (selectors, actions, own) => {
      return {
        ...own,
        crossings: {
          selectors,
          actions,
        },
      }
    }

    return connect(mapStateToProps, mapDispatchToProps, mergedProps)(BorderCrossingsHOC)
  }

export { borderCrossings }
export default borderCrossings
