import { SocketEvent } from '../../../utils/SocketProvider'

export interface HotelsBookingSearchOffersResultsChanged extends SocketEvent {
  // due to bug MIN-3588 it can be array or object while the key is index of array
  offers: SearchResultOffer[] | Record<number, SearchResultOffer>
  roomAllocations: SearchResultRoomAllocation[]
  searchQueryChunkUuid: string
  totalPaxes: string
  status: string
}

export interface SearchResultRoomAllocation {
  capacity: number
}

export interface SearchResultOffer {
  attributes: any
  booking?: any
  created_at: string
  errorMessageSlut?: string
  hasBreakfast: boolean
  hasBrokenRules: boolean
  lowestPrice: string
  lowestPricePerNight: string
  lowestPriceWithBreakfast: string
  lowestPriceWithBreakfastPerNight: string
  mealTypes: string[]
  nights: number
  options: SearchResultOfferOption[]
  reservationMessage: string
  rules: any[]
  uuid: string
}

export type StarType = 1 | 2 | 3 | 4 | 5

export interface SearchResultOfferAttributes {
  address: string
  name: string
  description: string
  distance: number
  photos: string[]
  stars: StarType
  facilities: {
    facilities: string[]
    hotel: string
    room: string
  }
  location: {
    countryCode: string
    latitude: number
    longitude: number
  }
}

interface Amount {
  amount: string
  currency: string
  formatted: string
  symbol: string
}

export interface SearchResultOfferOption {
  amount: Amount
  amountPerNight: Amount
  amountPerPax: Amount
  apiId: string
  attributes: {
    cancelDate: string
    description: string
    id: string
    mealType: string[]
    refundable: boolean
    roomType: string[]
  }
  booking: any
  chosen: boolean
  hasBreakfast: boolean
  reservation_fee: Amount & {
    amount_rounded: string
  }
  rules: any[]
  uuid: string
}
