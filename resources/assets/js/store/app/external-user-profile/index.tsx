import React from 'react'
import { get } from 'lodash'
import { reset, set, change } from './reducers'

export const MODULE_MOUNT_POINT = 'external-user-profile'

export const RESET = MODULE_MOUNT_POINT + '::reset'
export const SET = MODULE_MOUNT_POINT + '::set'
export const CHANGE = MODULE_MOUNT_POINT + '::change'

const actions = {
  [RESET]: reset,
  [SET]: set,
  [CHANGE]: change,
}

export const getInitialState = () => {
  return {
    data: {},
    countries: [],
    isFetching: false,
    isInitialized: false,
  }
}

export const reducer = (state = {}, action) => {
  if (actions.hasOwnProperty(action.type)) {
    return actions[action.type](state, action.payload, get(action, 'meta', {}))
  }
  return state
}
