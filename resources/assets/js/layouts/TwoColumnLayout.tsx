import React from 'react'
import { routes } from '../routes'

import MainNav from '../containers/MainNav/index'
import { Header } from './partials/Header'
import { Route } from 'react-router-dom'
import classNames from 'classnames'

export const TwoColumnLayout = ({ component: Component, type, name, ...rest }) => {
  const classes = classNames(['app', name])

  return (
    <div className={classes}>
      <MainNav items={routes.main} />
      <Route
        {...rest}
        children={(matchProps) => (
          <div className='app__main'>
            <Header />
            <Component<any, any> {...matchProps} />
          </div>
        )}
      />
    </div>
  )
}
