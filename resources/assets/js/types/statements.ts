import { HttpLink } from './http-link';
import { Paginator } from './response';

export enum StatementStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
}

export interface Statement {
  id: string;
  amount_settled: string;
  amount_to_settle: string;
  balance: string;
  card_number: string;
  closed: boolean;
  from: string;
  to: string;
  owner: string;
  _links: {
    transactions: HttpLink<[]>;
  };
}

export interface StatementResponse {
  data: Statement[];
  summary?: StatementSummary[];
  paginator: Paginator;
}

export interface StatementSummary {
  name: string;
  value: string;
}
