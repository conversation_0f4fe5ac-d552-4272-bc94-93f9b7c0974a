export enum SearchOfferStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  ERROR = 'error',
}

export interface TrainOffersWebsocketMessage {
  status: SearchOfferStatus;
  uuid: string;
  totalPaxes: number;
  offers: TrainOffer[];
  subscribe: {
    channel: string;
    event: string;
  };
  socket: any;
}

export interface TrainOffer {
  uuid: string;
  created_at: string;
  attributes: TrainOfferAttributes;
  options: TrainOfferOption[];
  hasBrokenRules: boolean;
  rules: boolean | any[];
  chosen: boolean;
  booking: any;
  reservationMessage: string;
  errorMessageSlug: string | null;
}

export interface TrainOfferAttributes {
  departureDate: string;
  arrivalDate: string;
  originStation: string;
  destinationStation: string;
  originCity: string;
  destinationCity: string;
  travelTime: string;
  equipmentCode: string;
  options: any[][];
  apiVars: {
    train_0: string;
  };
  trainNumber: string;
}

export interface TrainOfferOption {
  uuid: string;
  apiId: string;
  attributes: TrainOfferOptionAttributes;
  amount: PriceAmount;
  reservation_fee: PriceAmount;
  supplierCode: string | null;
  parking: boolean;
  corporateRate: boolean;
  corporateRateImage: string;
  rules: Rule[];
  chosen: boolean;
  booking: any;
  availableOptions: any[];
  reservationAttributes: any[];
  requestedAttributes: any[];
  amountPerPax: PriceAmount;
}

export interface TrainOfferOptionAttributes {
  salable: string;
  optionID: string;
  service_class: string;
  availability: string;
  immediateTicketing: any;
  refundable: boolean;
}

export interface PriceAmount {
  amount: string;
  amount_rounded?: string;
  currency: string;
  formatted: string;
  symbol: string;
}

export interface Rule {
  level: string;
  valid: boolean;
  message: {
    name: string;
    params: {
      service_class: number;
    };
  };
  ruleName: string;
  validationValues: {
    config_value: number;
    request_value: string;
    compare_values: string;
  };
}
