import { HttpResponse } from './response'
import { SocketSubscribe } from './websocket'

export interface HotelOfferEntryRaw {
  uuid: string
  created_at: string
  attributes: HotelOfferEntryAttributes
  options: HotelOfferEntryOption[]
  hasBrokenRules: boolean
  rules: string[]
  hasBreakfast: boolean
  reservationMessage: string
  lowestPrice: string
  lowestPriceWithBreakfast: string
  lowestPricePerNight: string
  lowestPriceWithBreakfastPerNight: string
  errorMessageSlug: string
  mealTypes: string[]
}

interface OptionAmount {
  amount: string
  currency: string
  symbol: string
  formatted: string
}

export interface HotelOfferEntryOption {
  uuid: string
  apiId: string
  attributes: {
    id: string
    mealType: string[]
    description: string
    cancelDate: string
    refundable: boolean
    roomType: string[]
  }
  cancelDate: {
    refundable: boolean
    date: string
    hour: string
  }
  rules: string[]
  amount: OptionAmount
  reservation_fee: OptionAmount & { amount_rounded: string }
  amountPerNight: OptionAmount
  amountPerPax: OptionAmount
  chosen: boolean
  booking: null
  hasBreakfast: boolean
  corporateRate: string
  corporateRateImage: string
}

interface HotelOfferEntryAttributes {
  name: string
  address: string
  description: string
  distance: number
  location: {
    latitude: number
    longitude: number
    countryCode: string
  }
  photos: string[]
  stars: number
  facilities: {
    hotel: string
    room: string
    facilities: string[]
  }
  apiVars: {
    from: string
    to: string
    hotelFacilities: string
    roomFacilities: string
  }
}

export interface HotelOfferDataResponse {
  status: 'complete'
  uuid: string
  totalPaxes: number
  searchQueryChunkUuid: string
  roomAllocations: RoomAllocation[]
  offers: HotelOfferEntryRaw[]
  subscribe: SocketSubscribe
}

interface RoomAllocation {
  capacity: number
}

export type ChooseHotelOfferResponse = HttpResponse<HotelOfferDataResponse>
