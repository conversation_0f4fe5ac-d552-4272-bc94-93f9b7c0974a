import { Pagin<PERSON> } from './response'
import { HttpLink } from './http-link'

export interface Transaction {
  id: string
  card_id: string
  description: string
  billing_value: string
  billing_currency: string
  transaction_value: string
  transaction_currency: string
  status: string
  created_at: string
  confirmed_at: string
  document_number: string
  request_number: string
}

export interface TransactionResponse {
  data: Transaction[]
  paginator: Paginator
}
