import {
  STATUS_ACCOUNTING,
  STATUS_SETTLEMENT,
  STATUS_TRIP,
  STATUS_UPCOMING_TRIP,
  STATUS_WAITING_FOR_ACCEPTANCE,
} from '../constants/request'

import { includes } from 'lodash'

class Request {
  constructor(init) {
    const keys = Object.keys(init)
    keys.forEach((key) => {
      Object.defineProperty(this, key, {
        value: init[key],
      })
    })
  }
}

export { Request }
export default Request
