.company-hierarchy__list {
  list-style: none;
  overflow: auto;
  display: flex;

  ul {
    list-style: none;
    position: relative;
    white-space: nowrap;
    margin: 0;
    padding: 0;
  }

  li {
    display: inline-block;
    white-space: nowrap;
    vertical-align: top;
    margin: 0;
    text-align: center;
    position: relative;
    padding: 0 5px;
    box-sizing: border-box;

    div {
      display: flex;
      flex-flow: wrap column;
      align-items: center;
      padding: 0 10px;
      text-decoration: none;
      color: #666;
      font-size: 11px;

      img{
        cursor: pointer;
        object-fit: cover;
      }
    }
  }

  // employee
  li ul li {
    div {
      &:before {
        content: '';
        display: block;
        border-left: 1px solid #ccc;
        height: 45px;
        margin: 5px 0;
      }

    }

    &:before, &:after {
      content: '';
      position: absolute;
      top: 0;
      border-top: 1px solid #ccc;
      width: 50%;
    }

    &:before {
      left: 0;
    }

    &:after {
      right: 0;
    }
  }

  ul li {
    &, div {
      margin-top: -5px;
    }

    &:only-child {
      &, & > div {
        margin-top: 0 !important;
      }

      &:before, &:after, & > div:before {
        display: none !important;
      }
    }

    &:first-child {

      &::before {
        display: none !important;
      }
    }

    &:last-child:after {
      display: none !important;
    }
  }
}


li.has-child-node {
  & > div {
    &:after {
      content: '';
      display: block;
      border-left: 1px solid #ccc;
      height: 45px;
      margin: 5px 0;
    }
  }
}
