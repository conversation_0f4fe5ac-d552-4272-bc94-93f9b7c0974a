.input-group {
  display: flex;
  align-items: center;
  position: relative;
  border: 2px solid transparent;
  border-radius: 15px;
  background: $gradient-default-01;
  -moz-background-origin: border;
  background-origin: border-box;
  box-shadow: inset -999px 0 0 #fff; /* The background color */

  @include rwd(small) {
    max-width: 100%;
    min-width: 165px;
  }
}

.input-group .icon {
  margin: 0 10px;
}

.input-group__input {
  border: 0;
  outline: none;
  max-width: 100%;
  width: 120px;
  flex-grow: 1;
  @include rwd(small) {
    min-width: 180px;
    width: auto;
  }
}

.input-group__btn {
  border: 0;
  background: transparent;
  outline: none;
  color: $color-default-02;
  border-left: 2px solid $color-default-02;
  padding: 8px 15px;
  font-weight: $font-weight-semibold;
  cursor: pointer;
}

.input-group__close-icon {
  @extend .icon;
  @extend .icon-close_fill;
  border: 0;
  background: transparent;
  outline: none;
  color: $color-danger;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  width: 20px;
  height: 20px;
}

.input-group__search {
  padding-right: 8px;
}

.input-group__close-icon:before {
  background-color: transparent;
}

.input-group__close-icon:after {
  content: none;
}

