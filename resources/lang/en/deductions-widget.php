<?php
return array(
  'header__top-tooltip-content-1' => 'Select if provided',
  'header__top-tooltip-content-2-1' => 'Accommodation allowance: Select if accommodation cost is at your expense',
  'header__top-tooltip-content-2-2' => 'Public transport allowance: Select when you use public transport at your destination',
  'header__top-cell-1' => 'Deductions',
  'header__top-cell-2' => 'Allowances',
  'header__bottom-cell-1' => 'Local time',
  'header__bottom-cell-2' => 'Breakfast',
  'header__bottom-cell-3' => 'Lunch',
  'header__bottom-cell-4' => 'Dinner',
  'header__bottom-cell-5' => 'Accommodation',
  'header__bottom-cell-6' => 'Public transportation allowance',
  'header__bottom-cell-7' => 'Accommodation Public transportation',
  'header__top-tooltip-content-3' => 'Accommodation allowance: Select if the accomodation cost is at your expense',
  'header__top-tooltip-content-4' => 'Local transfers allowance: Select if you use public transportation at your destination.',
  'day-element-border-crossing-container__border-crossing' => 'Crossing a border:',
  'day-element-border-crossing-container__target-country' => ' - Destination country',
  'form__country' => 'Country',
  'form__target' => 'Destination country',
  'form__date' => 'Date',
  'form__save-button' => 'Save',
  'expense-container__add-button' => 'Add border crossing',
  'national-trip' => 'Domestic trip',
  'abroad-trip' => 'Trip abroad',
  'trip-type' => 'Type of the trip and details',
  'declarations' => 'Per diems',
  'back-to-border-crossings' => 'Back to the trip details',
  'go-to-deductions' => 'Save and go to calculation',
  'go-to-deductions-read' => 'Go to calculation',
  'border-crossing' => 'Crossing a border',
  'destination-country' => 'Destination country',
  'current-deductions-status' => 'Actual status of per diems calculation',
  'trip' => 'Trip type',
  'trip-start' => 'Trip start',
  'trip-end' => 'Trip end',
  'trip-process' => 'Trip details',
  'add-crossing' => 'Add crossing a border',
  'add-target' => 'Add target point',
  'tooltip-strong-1' => 'The location where the business trip starts and ends ',
  'tooltip-content-1' => 'is where the company\'s seat or permanent place of work is located. After agreeing with the employer, this place may also be the place of residence of the employee.',
  'tooltip-strong-2' => 'Start of the business trip',
  'tooltip-content-2' => ' is the time to leave the locality where the trip starts and it is assumed that it is eg the time of departure of the train, departure of the plane.',
  'tooltip-strong-3' => 'End of the business trip',
  'tooltip-content-3' => 'is the time of reaching the destination indicated as the place where the trip ends, e.g. the time of arrival of the train, arrival of the plane.',
  'header__top-tooltip-content-2' => 'Allowances',
  'target-points' => 'Destinations',
  'time' => 'Time',
  'local-time' => 'Local time',
  'breakfast' => 'Breakfast',
  'lunch' => 'Lunch',
  'dinner' => 'Dinner',
  'accommodation' => 'Accommodation',
  'local-trips' => 'Local trips',
  'target-point' => 'Destination',
  'date-auto-suggested-tooltip-content' => 'Time of crossing a border is autosuggested. Check if correct',
  'accommodation-confirmation-message' => 'You are not entitled to accommodation allowance.<br> Trip is too short or out of 9PM-7AM hours',
  'confirm-accommodation' => 'Confirm',
  'accept-accommodation' => 'Yes, I got it',
  'accommodation-cant-be-obtained' => 'You are not entitled to accommodation allowance. Trip is too short or out of 9PM-7AM hours',
  'price-included-breakfast' => 'Are you sure? Price for accommodation included breakfest',
  'confirm-widget-title' => 'Are you sure?',
  'confirm-cancel-trip' => 'Yes',
  'go-to-deductions-read-only' => 'Go to calculation'
);
