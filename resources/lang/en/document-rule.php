<?php

return array(
    'message' => 'Document cannot be settled:',
    'account-message' => 'Invoice cannot be posted:',
    'document-has-not-got-issue-date' => 'Fill in document date',
    'document-has-not-got-gross' => 'Fill in gross amount',
    'document-has-not-got-currency' => 'Fill in a currency',
    'document-has-not-got-payment' => 'Select payment method',
    'document-has-not-got-elements' => 'Select expense type',
    'document-has-not-got-document-number' => 'Fill in document number',
    'document-has-not-been-settled' => 'Document was not settled',
    'document-has-not-got-provider' => 'Select supplier',
    'document-has-not-got-received-date' => 'Pick a receive date',
    'document-elements-are-not-accounted' => 'Items are not settled',
    'accounting-dimensions-are-required' => 'Select all mandatory dimensions',
    'document-has-not-account-accounting' => 'Select an accounting account',
    'document-has-not-mpk' => 'Select MPK',
    'document-has-not-exchange-rate' => 'Complete the exchange rate',
    'document-has-not-got-accounting-type' => 'Select accounting type',
    'document-has-not-actual-exchange-rate' => 'The exchange rate required to settle this document is not yet available',
    'document-has-chosen-currency-exchange-rate' => 'Fill in or accept fx rate',
    'provider-has-no-erp-id' => 'Fill in supplier ERP id',
    'corrected-number-is-required' => 'Corrected document number is required',
    'document-is-not-unique' => 'An invoice with given number has been already entered more than once',
    'document-is-not-unique-for-supplier' => 'An invoice with given number and supplier has been already entered more than once ',
    'account-dimension-dependency-failed' => ':account_dimension is required',
);
