<?php
return array (
  'accounting-travel-expenses-has-been-saved' => 'info.accounting-travel-expenses-has-been-saved "Posting saved"',
  'accounting-travel-expenses-has-been-deleted' => 'info.accounting-travel-expenses-has-been-deleted "Posting deleted"',
  'request-has-been-saved' => '',
  'document-has-been-saved' => '',
  'request-has-been-deleted' => '',
  'acceptor-has-been-attached' => 'Acceptator assigned',
  'acceptor-has-been-deleted' => 'Acceptator deleted',
  'settlement-acceptor-has-been-attached' => 'Approver assigned',
  'settlement-acceptor-has-been-deleted' => 'Approver deleted',
  'element-has-been-saved' => 'Item saved',
  'element-has-been-deleted' => 'Item deleted',
  'element-has-been-updated' => 'Item changed',
  'group-has-been-saved' => 'Group saved',
  'group-has-been-deleted' => 'Group deleted',
  'ocr-hint-has-been-saved' => 'Recognized data have been saved',
  'travel-expense-has-been-saved' => 'Per diem item saved',
  'travel-expense-has-been-deleted' => 'Per diem item deleted',
  'request-mileage-has-been-saved' => 'Mileage saved',
  'request-mileage-has-been-deleted' => 'Mileage deleted',
  'comment-has-been-saved' => 'Comment saved',
  'document-has-been-deleted' => 'Comment deleted',
  'you-are-logged-in' => 'You are logged in',
  'you-are-logged-in-as' => 'You are logged in as 
info.installment-has-been-saved,Pozycja zaliczki zostaĹa zapisana"',
  'installment-has-been-deleted' => 'Cash advance item deleted',
  'instance-has-been-saved' => 'Instance data saved',
  'instance-has-been-deleted' => 'Instance data deleted',
  'border-crossing-has-been-saved' => 'Item crossing a border saved',
  'border-crossing-has-been-deleted' => 'Item crossing a border deleted',
  'user-has-been-saved' => 'User saved',
  'user-blocking-has-been-saved' => 'User blocking saved',
  'user-welcome-email-sent' => 'Welcome email has been sent',
  'project-has-been-saved' => 'Project saved',
  'project-has-been-deleted' => 'Project deleted',
  'company-has-been-saved' => 'Company saved',
  'user-assigned-accounting-of-this-request' => 'User: user assigned this claim himself already',
  'accountant-has-been-assigned' => 'Finance user has been assigned to this claim',
  'target-point-has-been-deleted' => 'Destination deleted',
  'target-point-has-been-saved' => 'Destination saved',
  'private-accomodation-has-been-deleted' => 'Accommodation cost paid by employee deleted',
  'private-accomodation-has-been-saved' => 'Accommodation cost paid by employee saved',
  'provided-accommodation-has-been-saved' => 'Provided accommodation has been saved',
  'provided-accommodation-has-been-deleted' => 'Provided accommodation has been removed',
  'document-has-been-removed-from-request-element' => 'Document has been deleted ',
  'document-has-been-added-to-request-element' => 'Document has been added ',

  'user-deputy-has-been-added-to-user' => 'Deputy successfully added',
  'user-deputy-has-been-deleted' => 'User deputy deleted',
  'user-deputy-given-user-does-not-exists' => 'User does not exists',
  'user-deputy-cannot-add-in-the-past' => 'You can not add a user deputy in the past',
  'user-deputy-cannot-add-yourself' => 'You can not add yourself',
  'user-deputy-cannot-add-in-given-time-period' => 'You can not add a deputy in a given time range',

  'user-assistant-has-been-deleted' => 'The user\'s assistant has been removed',
  'user-assistant-has-been-added-to-user' => 'The user\'s assistant successfully added',
  'user-assistant-given-user-does-not-exists' => 'User does not exists',
  'user-assistant-cannot-add-in-the-past' => 'You can not add a user assistant in the past',
  'user-assistant-cannot-add-yourself' => 'You can not add yourself',
  'user-assistant-cannot-add-in-given-time-period' => 'You can not add a assistant in a given time range',


  'provider-has-been-saved' => 'Provider has been saved',
  'provider-has-been-updated' => 'Provider has been updated',
  'provider-has-been-deleted' => 'Provider has been removed',
  'provider-accountant-notified' => 'Request for adding supplier has been sent ',
);
