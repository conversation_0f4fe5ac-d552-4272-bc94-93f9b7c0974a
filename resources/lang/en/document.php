<?php
return array (
  'accounting-travel-list' => 'Invoices',
  'ocr-hint-automatic-recognition' => 'Recognized data',
  'request-item' => 'Item',
  'basic-info' => 'Basic information',
  'document-type' => 'Document type',
  'is_margin' => 'Invoice type',
  'is_margin_type_vat' => 'VAT Invoice',
  'is_margin_type_margin_scheme' => 'Margin scheme',
  'accounting-document' => 'Invoice',
  'other-documents' => 'Other documents',
  'other-document' => 'Other document',
  'document-number' => 'Invoice number',
  'document-corrected-number' => 'Credit invoice number',
  'document-date' => 'Invoice date',
  'annotation' => 'Description',
  'exchange-rate' => 'Exchange rate',
  'exchanged-amount' => 'Amount',
  'payment' => 'Payment method',
  'service-card' => 'Company card',
  'own' => 'Own funds',
  'transfer' => 'Transfer',
  'finish-document-edition' => 'Complete',
  'finish-document-edition-settlement-acceptor' => 'Close',
  'accounting-settlements-credit-account' => 'CR account',
  'accounting-settlements-debit-account' => 'DR account',
  'accounting-settlements-amount' => 'Amount',
  'mileage-allowance-finish-button' => 'Save draft',
  'waiting-for-acceptance' => 'Waiting for approval',
  'to-settlement' => 'To settlement',
  'accounting-document-to-decretation' => ' Documents for posting',
  'accounting-documents' => 'Accounting documents',
  'accounting-date' => 'Posting date',
  'document-accounting-number' => 'Voucher ID',
  'not-accounted' => 'Not posted',
  'to-decretation' => 'For posting',
  'to-accept' => 'For acceptance',
  'type-travel' => 'Trip',
  'type-accounting' => 'Posting',
  'invoice' => 'Invoice',
  'correcting-invoice' => 'Credit invoice',
  'receipt' => 'Receipt Other',
  'provider' => 'Supplier',
  'provider-search' => 'Search supplier',
  'issue-date' => 'Invoice date',
  'received-date' => 'Invoice receipt date',
  'vat-date' => 'VAT date',
  'currency' => 'Currency',
  'pln-amount' => 'Amount PLN',
  'vat' => 'VAT amount',
  'account-dimension-summary' => 'Line summary',
  'accounting-settlements-header' => 'Settlements',
  'accounting-finish-accounting-button' => 'Complete document entries',
  'vat-summary-table-sum-header' => 'Amount',
  'vat-summary-table-accounting-account-header' => 'VAT rate',
  'no-belongs' => 'Not assigned',
  'accounting-page-title' => 'Document posting',
  'gross' => 'Gross amount',
  'net' => 'Net amount',
  'list-header-gross' => 'Amount',
  'travel-document-title' => 'Trip document',
  'mileage-allowance-finish-and-mark-as-completed' => 'Save as completed',
  'there-are-no-documents' => 'No documents',
  'vat-account' => 'GL account',
  'vat-amount' => 'Amount',
  'mileage-allowance-add-button' => 'Add entry line',
  'private-expenses' => 'Private expenses',
  'ocr-hint-document_number' => 'Invoice number',
  'ocr-hint-issue_date' => 'Date',
  'ocr-hint-gross' => 'Amount',
  'ocr-hint-provider_id' => 'Supplier recognized',
  'ocr-hint-transaction' => 'Transaction recognized',
  'ocr-hint-currency' => 'Currency recognized',
  'ocr-hint-annotation' => 'Description recognized',
  'no-ocr-hints' => 'No suggestions',
  'accept-all-hints' => 'Accept all suggestions',
  'vat-sum' => 'Total',
  'no-vat' => 'The transaction is not subject to VAT records',
  'post' => 'Post entries',
  'settle' => 'Settle',
  'exchange-rate-tooltip-trip' => 'Purchases in foreign currencies paid by company card will be calculated by transaction rate used by issuing bank.',
  'exchange-rate-tooltip-trip-2' => 'Purchases in foreign currencies paid from own funds will be calculated by individual exchange rate declared by employee. All purchases without rate confirmation will be calculated by exchange rate in accordance with company policy.',
  'exchange-rate-tooltip-trip-3' => 'In case of advance payments exchange rate applicable for pay out will be used.',
  'exchange-rate-tooltip-expense' => 'Purchases in foreign currencies paid by company card will be calculated by transaction rate used by issuing bank.',
  'exchange-rate-tooltip-expense-2' => 'Purchases in foreign currencies paid from own funds will be calculated by individual exchange rate declared by employee. All purchases without rate confirmation will be calculated by exchange rate in accordance with company policy.',
  'exchange-rate-tooltip-expense-3' => 'In case of advance payments exchange rate applicable for pay out will be used.',
  'assign-to-request-element' => 'Assign to an item',
  'close-hints' => 'Close',
  'no-provider' => 'Supplier not choosen',
  'no-provider-error' => 'Supplier is not set',
  'voucher' => 'Voucher',
  'ticket' => 'Ticket',
  'confirmation' => 'Confirmation',
  'corporate-card' => 'CTE Card',
  'not-set' => 'Empty',
  'type-accounting-descritpion' => 'Invoices and receipts.',
  'type-other-descritpion' => 'Attachments, i.e. tickets.',
  'payment-tooltip-personal-card' => 'Company card',
  'payment-tooltip-personal-card-description' => 'Personal company card',
  'payment-tooltip-own-funds' => 'Own funds',
  'payment-tooltip-own-funds-description' => 'Cash or private payment card',
  'add-provider' => 'Add supplier',
  'add-provider-form' => 'Adding supplier',
  'provider-name' => 'Name',
  'provider-nip'  => 'Tax ID',
  'country'  => 'Country',
  'address'  => 'Address',
  'postcode' => 'Postcode',
  'ocr-hint-provider_suggested' => 'Supplier recognized',
  'no-rate' => 'No exchange rate for the given currency',
  'suggested-rate'    => 'Suggested FX rate',
  'rate-day' => 'at the date',
  'individual-rate' => ' Individual FX rate',
  'installment-rate' => 'FX rate at the date of cash advance',
  'suggested-installment-rate' => 'Suggested FX rate at the date of cash advance',
  'finance-rate' => 'FX rate edited by Finance user',
  'default-rate' => 'FX rate',
  'accepted-rate-day' => 'at the date',
  'rate-text' => 'Exchange rate',
  'date-vat' => 'VAT Date',
  'city' => 'City',
  'erp-id' => 'Erp ID',
  'transaction' => 'Transaction',

  'reservation-changes-container-title' => 'Booking changes',
  'reservation-changes-can-not-be-canceled' => 'You can’t cancel your booking',
  'reservation-changes-cancel-date' => 'You can cancel your booking up to :date :time',
  'reservation-changes-cancel-button-title' => 'Cancel booking',
  'reservation-changes-waiting-for-response' => 'We are confirming your cancellation…',
  'reservation-changes-canceled-message' => 'Booking canceled',
  'reservation-changes-cancellation-error' => 'You can’t cancel your booking. Please contact our travel support team',
  'reservation-changes-confirmation-title' => 'Are you sure?',
  'reservation-changes-confirmation-message' => 'Your booking will be cancelled',
  'reservation-changes-confirmation-yes' => 'Yes',
  'reservation-changes-confirmation-no' => 'No',
  'reservation-ticket-invalidated' => 'Booking cancelled',
  'reservation-ticket-confirmed' => 'Booking confirmed',

  'sum_of_other_documents' => 'Total amount',
);
