<?php
return array(
    'path-status-canceled' => 'Canceled',
    'path-status-rejected' => 'Rejected',
    'trip-name' => 'Trip to :route',
    'path-status-finish' => 'Posted',
    'trip-name-with-date' => 'Trip to :route between :date',
    'trip-name-with-date-no-route' => 'Trip in days :date',
    'path-header' => 'Submission of request',
    'path-status-trip' => 'Ongoing trip',
    'path-status-draft' => 'Draft',
    'path-status-waiting-for-acceptance' => 'Waiting for approval',
    'path-status-upcoming-trip' => 'Upcoming trip',
    'path-status-settlement' => 'Settlement',
    'path-status-acceptance-of-settlement' => 'Approval',
    'path-status-accounting' => 'Journal entries',
    'path-status-completing-documents' => 'Completing documents',
    'proposal-header' => 'Travel request',
    'basic-info-header' => 'Basic information',
    'mpk-label' => 'Cost centre',
    'project-label' => 'Project',
    'select-project' => 'Select project',
    'trip-substantiation-label' => 'Purpose',
    'expense-description' => 'Expense description',
    'path-add-reviewer' => 'Add approver',
    'path-header-status' => 'Status',
    'trip-purpose-label' => 'Trip purpose',
    'path-status-new' => 'New',
    'trip-did-not-have-place' => 'Trip did not take place',
    'unrealized-tooltip' => 'This trip is marked as unrealized. Claim was automatically filled based on data provided previously and has been sent to settlement.',
    'no-rate' => 'No exchange rate',
    'path-status-transferred' => 'Transferred',
    'path-status-transfer-error' => 'Transfer error',
    'allocation-label' => 'Allocation',
    'empty-project-option-label' => 'Not applicable',
    'trip-traveler-label' => 'Travelers',
    'trip-options' => 'Additional options',
    'trip-option-not-applicable' => 'Not applicable',
    'trip-option-add-traveler' => 'Add person',
    'trip-option-private' => 'Private trip',
    'private-trip' => 'Private trip',
    'trip-option-find-user' => 'Find user',
    'trip-traveler-group-label' => 'Group',
    'trip-traveler-remove-text' => 'Deleting person will require new trip plan preparation.',
    'trip-traveler-remove-btn-ok' => 'Confirm',
    'trip-traveler-remove-btn-cancel' => 'Cancel',
    'trip-type' => 'Trip type',
    'trip-type-delegation' => 'Allawances',
    'trip-type-non-delegation' => 'No allawances',
);
