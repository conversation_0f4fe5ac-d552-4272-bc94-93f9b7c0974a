<?php
return array (
  'error-invalid-provider' => '',
  'UzupeĹ‚nij dane' => 'Fill in data',
  'powyĹĽej 900 cm3' => 'above 900cm3',
  'global-quick-filters' => '',
  'request-requested-amount' => '',
  'Pozycje' => 'Positions',
  'Rodzaj' => 'Type',
  'Opis' => ' Description',
  'Wnioskowana kwota' => 'Amount requested',
  'Data wyjazdu' => 'Departure date',
  'Wyjazd z' => 'Departure from"',
  'Data powrotu' => 'Return date',
  'OdlegĹ‚oĹ›Ä‡' => 'Distance',
  'PojemnoĹ›Ä‡' => 'Engine capacity',
  'poniĹĽej 900 cm3' => 'Under 900cm3',
  'RyczaĹ‚t' => 'Allowance',
  'PozostaĹ‚e koszty' => 'Remaing costs',
  'Paliwo' => 'Fuel',
  'Data odbioru' => 'Pick-up date',
  'Data zwrotu' => 'Return date',
  'Koszt wynajmu' => 'Rental costs',
  'Typ pojazdu' => 'Type of a car',
  'Prywatny' => 'Private',
  'SĹ‚uĹĽbowy' => 'Company',
  'WynajÄ™ty' => 'Rental',
  'Miejsce rozpoczÄ™cia' => 'Start location',
  'Miejsce docelowe' => 'Destination',
  'Data przyjazdu' => 'Arrival date',
  'Miejsce' => 'Location',
  'Wylot z' => 'Departure from',
  'Przylot do' => 'Arrival to',
  'Data przylotu' => 'Arrival date',
  'PodrĂłĹĽ w obie strony' => 'Round trip',
  'Zaliczka' => 'Cash advance',
  'Not found exchange rate' => '',
  'Not enough arguments following: %s' => 'default.Not enough arguments following: %s',
  'Invalid JSON config file: %s' => 'default.Invalid JSON config file: %s',
  'Commands:' => 'default.Commands:',
  'default:' => 'default.default:',
  'aliases:' => 'default.aliases:',
  'boolean' => 'default.boolean',
  'count' => 'default.count',
  'string' => 'default.string',
  'array' => 'default.array',
  'number' => 'default.number',
  'required' => 'default.required',
  'choices:' => 'default.choices:',
  'Examples:' => 'default.Examples:',
  'generated-value' => 'default.generated-value',
  'Not enough non-option arguments: got %s' => 'default.Not enough non-option arguments: got %s, need at least %s',
  'Too many non-option arguments: got %s' => 'default.Too many non-option arguments: got %s, maximum of %s',
  'Invalid values:' => 'default.Invalid values:',
  'Argument check failed: %s' => 'default.Argument check failed: %s',
  'Implications failed:' => 'default.Implications failed:',
  'Arguments %s and %s are mutually exclusive' => 'default.Arguments %s and %s are mutually exclusive',
  'Did you mean %s?' => 'default.Did you mean %s?',
  'Options:' => 'default.Options:',
  'Edit Trip Request' => 'default.Edit Trip Request',
  'Plan podrĂłĹĽy' => 'Trip plan',
  'Inne koszty' => 'Other costs',
  'Dodaj' => 'Add',
  'notification-email' => 'Your new password:',
  'Today' => 'Today',
  'deny' => 'Lack of access',
  'close' => 'Close',
);
