<?php

return [
    'payments_header' => 'Payments - Card :card',
    'payments_th_payment_date' => 'Payment date',
    'payments_th_details' => 'Details',
    'payments_th_transaction_amount' => 'Transaction amount',
    'payments_th_settlement_amount' => 'Settlement amount',
    'payments_th_document_number' => 'Document Number',
    'payments_th_status' => 'Status',
    'payments_th_request_id' => 'Request ID',
    'payments_attach_button' => 'Match',
    'payments_cancel_button' => 'Cancel',
    'payments_status_reconciled' => 'Attached',
    'payments_status_not_reconciled' => 'Not attached',

    'statement_header' => 'Wyciągi',
    'statement_th_statement_date' => 'Data wyciągu',
    'statement_th_statement_id' => 'ID wyciągu',
    'statement_th_name' => 'Nazwa',
    'statement_th_statement_amount' => 'Saldo wyciągu',
    'statement_th_requested_by' => 'Employee',
    'statement_th_status' => 'Status',

    'statement_quick_filter_mine' => 'Moje',
    'statement_quick_filter_opened' => 'Otwarty',
    'statement_quick_filter_closed' => 'Zamknięty',

    'statement-status-open' => 'Otwarty',
    'statement-status-closed' => 'Zamknięty',

    'single_statement_header' => 'Statement :from - :to',

    'card_issue' => [
        'success' => 'The process of issuing cards has begun',
        'tenant_provider_id_is_missing' => 'Internal Settings Error: Tenant Provider ID',
        'tenant_id_is_missing' => 'Internal Settings Error: Tenant -> Company',
    ],
    'status' => [
        'none' => '-',
        'invited' => 'Invitation sent',
        'card_issued' => 'Card issuing',
        'done' => '',
    ],

    'account' => 'Account',
    'accounts' => 'Accounts',
    'account_date' => 'Date',
    'account_details' => 'Details',
    'account_card' => 'Card',
    'account_employee' => 'Employee',
    'account_amount' => 'Amount',
    'account_balance' => 'Balance',
    'account_status' => 'Status',
    'account_quick_filter_to_account' => 'To be posted',
    'account_quick_filter_accounted' => 'Posted',
    'account_details_for_period' => 'Statement period',
    'account_details_id' => 'Statement id ',

    'payment-attach-header' => 'Invoices',
    'payment-attach-th-invoice-date' => 'Invoice date',
    'payment-attach-th-invoice-number' => 'Number',
    'payment-attach-th-transaction-amount' => 'Transaction date',
    'payment-attach-th-billing-amount' => 'Billing date',
    'payment-attach-th-request-id' => 'Request ID',

    'no-invoice-title' => 'No invoices to attach',
    'no-invoice-description' => 'Add invoice to your settlement',
    'no-invoice-back' => 'Back',
];
