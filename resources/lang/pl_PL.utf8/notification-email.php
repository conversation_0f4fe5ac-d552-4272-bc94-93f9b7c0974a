<?php

return [
    /**
    * NOWE TŁUMACZENIA - które zostały znalezione w kodzie, a nie występują w plikach tłumaczeń.
    */
    'action-accept' => 'Zaakceptuj',
    'action-reject' => '<PERSON>dr<PERSON><PERSON>',

    /**
    * PUSTE TŁUMACZENIA - które występują zarówno w plikach tłumaczeń oraz w kodzie, ale nie mają przypisanej wartości
    */

    /**
    * USUNIĘTE TŁUMACZENIA - które występują w plikach tłumaczeń, a nie zostały znalezione w kodzie
    */

    /**
    * POZOSTAŁE TŁUMACZENIA - były dostępne wcześniej i mają tłumaczenie
    */
    'hello' => 'Witaj,',
    'action' => 'Przejdź do wniosku',
    'action-settlement' => 'Przejdź do rozliczenia',
    'thanks-for-using' => 'Dziękujemy za użycie naszej aplikacji',
    'users-email' => "E-mail użytkownika: :email",
    'request-settlement-has-been-accounted-message' => 'Twoje rozliczenie zostało zaksięgowane. Wyjazd do :name w dniach :trip_starts - :trip_ends',
    'request-settlement-accounted-details-message' => 'Rozliczenie zostało zaksięgowane. Wyjazd do :name w dniach :trip_starts - :trip_ends',
    'request-expense-settlement-has-been-accounted-message' => 'Twoje rozliczenie zostało zaksięgowane. Wydatek :purpose :date',
    'request-expense-settlement-accounted-details-message' => 'Twoje rozliczenie zostało zaksięgowane. Wydatek :purpose :date',
    'request-settlement-has-been-accounted-subject' => 'Twoje rozliczenie zostało zaksięgowane',
    'request-settlement-accounted-details-subject' => ':requestNumber :userName - :erpId - rozliczenie zostało zaksięgowane',
    'request-settlement-to-improvement-subject' => 'Twoje rozliczenie zostało zwrócone do poprawy',
    'request-settlement-to-decree-subject' => 'Twoje rozliczenie zostało zwrócone do poprawy',
    'request-settlement-to-improvement-message' => 'Twoje rozliczenie :name zostało zwrócone do poprawy',
    'request-settlement-to-decree-message' => 'Twoje rozliczenie :name zostało zwrócone do poprawy',
    'request-settlement-has-been-accepted-message' => 'Twoje rozliczenie :name zostało zatwierdzone.',
    'request-settlement-has-been-accepted-subject' => 'Rozliczenie :requestNumber :user zostało zatwierdzone',
    'you-recently-requested-to-reset-your-password-for-your-account' => 'Zgłoszono prośbę o zresetowanie hasła do Twojego konta.',
    'password-reset-button' => 'Zresetuj hasło',
    'thank-you-for-using-our-application' => 'Dziękujemy za korzystanie z naszej aplikacji.',
    'your-password-is: ' => 'Twoje hasło to: ',
    'your-request-has-been-accepted-message' => 'Twój wniosek :name został zaakceptowany',
    'your-request-has-been-accepted-subject' => 'Wniosek :requestNumber :user został zaakceptowany ',
    'subordinate-request-has-been-auto-accepted-message' => 'Użytkownik :user otrzymał automatyczną akceptację wniosku :name', //TODO:change text
    'subordinate-request-has-been-auto-accepted-subject' => 'Użytkownik :user otrzymał automatyczną akceptację wniosku :name', //TODO:change text
    'subordinate-request-has-been-settlement-auto-accepted-message' => 'Użytkownik :user otrzymał automatyczne zatwierdzenia wniosku :name', //TODO:change text
    'subordinate-request-has-been-settlement-auto-accepted-subject' => 'Użytkownik :user otrzymał automatyczne zatwierdzenia wniosku :name', //TODO:change text
    'request-waiting-for-acceptance-message' => 'Otrzymałeś nowy wniosek do zaakceptowania',
    'request-waiting-for-acceptance-subject' => 'Wniosek  :requestNumber :userName oczekuje na akceptację',
    'request-waiting-for-settlement-subject' => 'Rozliczenie :requestNumber :userName oczekuje na zatwierdzenie',
    'request-waiting-for-settlement-message' => 'Rozliczenie użytkownika :userName o nazwie :requestName - :date',
    'request-rejected-owner-notification-message' => 'Twój wniosek :name został odrzucony',
    'request-rejected-owner-notification-subject' => 'Twój wniosek został odrzucony',
    'request-rejected-acceptor-notification-message' => 'Wniosek :user został odrzucony',
    'request-rejected-acceptor-notification-subject' => 'Wniosek :user został odrzucony',
    'request-trip-started-message' => 'Podróż została rozpoczęta',
    'request-trip-started-subject' => 'Podróż została rozpoczęta',
    'request-returned-for-improvement-owner-message' => 'Twoje rozliczenie :name zostało zwrócone do poprawy',
    'request-returned-for-improvement-owner-subject' => 'TWoje rozliczenie zostało zwrócone do poprawy',
    'request-returned-for-improvement-owner-comment' => 'Komentarz: :comment',
    'request-returned-to-decree-owner-comment' => 'Twój wniosek :name został zwrócony do poprawy',
    'request-returned-for-improvement-acceptor-message' => 'Wniosek :user został zwrócony do poprawy',
    'request-returned-for-improvement-acceptor-subject' => 'Wniosek :user został zwrócony do poprawy',
    'request-canceled-by-owner-message' => ':user anulował swój wniosek',
    'request-canceled-by-owner-subject' => ':user anulował swój wniosek',
    'request-for-cash-advance-message' => ':user złożył wniosek o zaliczkę :name w dniach :trip_starts - :trip_ends MPK (:mpk)',
    'request-for-cash-advance-subject' => 'Prośba o zaliczkę :requestNumber :user',
    'request-for-cash-advance-installment-line' => 'Kwota zaliczki:',

    'request-return-to-improvement-btn' => 'Zwróć do poprawy',
    'request-accept-btn' => 'TAK, Zatwierdź',
    'request-decline-btn' => 'NIE, Odrzuć',
    'request-rejected-by-email' => 'odrzucono przez e-mail',
    'accounted-amount' => 'Kwota<br>rozliczona',
    'requested-amount' => 'Kwota<br>wnioskowana',

    'request-set-as-unrealized-subject' => 'Anulowanie podróży :name w dniach :trip_starts - :trip_ends ',
    'request-set-as-unrealized-message' => 'Twoja podróż właśnie została anulowana. Wszystkie niewykupione rezerwacje dla :name w dniach :trip_starts - :trip_ends zostaną automatycznie odwołane.',
    'request-set-as-unrealized-for-supervisor-message' => 'Użytkownik :user wybrał opcję “Podróż się nie odbyła” i anulował wszystkie niewykupione rezerwacje dla :name w dniach :trip_starts - :trip_ends',

    'request-finish-your-trip-right-now-subject' => 'Anulowanie podróży :name w dniach :trip_starts - :trip_ends ',
    'request-finish-your-trip-right-now-message' => 'Twoja podróż właśnie została anulowana. Wszystkie niewykupione rezerwacje dla :name w dniach :trip_starts - :trip_ends zostaną automatycznie odwołane.  ',
    'request-finish-your-trip-right-now-for-supervisor-message' => 'Użytkownik :user wybrał opcję “Zakończ podróż już teraz” i anulował wszystkie niewykupione rezerwacje dla :name w dniach :trip_starts - :trip_ends',

    'unaccounted-request-reminder-subject' => 'Przypomnienie :userName wniosek :requestNumber czeka na rozliczenie',
    'not-accepted-settlement-reminder-subject' => 'Przypomnienie :userName rozliczenie :requestNumber oczekuje na zatwierdzenie',
    'unaccounted-request-reminder-message-trip' => 'Masz nierozliczoną podróż służbową - :name',
    'unaccounted-request-reminder-message-expense' => 'Przypomnienie o nierozliczonym wydatku - :name',
    'unaccounted-request-reminder-message-invoice' => 'Przypomnienie o nierozliczonej fakturze - :name',
    'salutation' => "Z poważaniem, :name",

    'travel-insurance-subject' => 'Przypomnienie o ubezpieczeniu podróży',

    'request-settlement-accepted-subject' => 'Twoje rozliczenie zostało zatwierdzone',
    'request-settlement-accepted-message' => 'Twoje rozliczenie :name zostało zatwierdzone',
    'request-settlement-accounted-subject' => 'Twoje rozliczenie zostało zaksięgowane',
    'request-settlement-accounted-message' => 'Twoje rozliczenie ,,:name" zostało zaksięgowane',

    'password-reset-notification' => "Prośba o zmianę hasła",
    'new-password-notification' => "Twoje nowe hasło",
    'subordinate-sent-request-to-acceptance-with-project-message' => 'Użytkownik :user złożył wniosek :name. Dotyczy MPK (:mpk), projektu (:project)',
    'subordinate-sent-request-to-acceptance-without-project-message' => 'Użytkownik :user złożył wniosek :name. Dotyczy MPK (:mpk)',
    'subordinate-sent-request-to-acceptance-with-project-subject' => 'Użytkownik :user złożył wniosek dot. projektu (:project)/ dot. MPK (:mpk)',
    'subordinate-sent-request-to-acceptance-without-project-subject' => 'Użytkownik :user złożył wniosek dot. MPK (:mpk)',
    'subcopy' => 'Jeśli masz problem z przyciskiem ":text", skopiuj i wklej poniższy link do przeglądarki: [:url](:url)',
    'do-more' => 'Jeżeli chcesz zrobić coś więcej otwórz aplikację klikając na link [Mindento desktop](:url)',
    'offer-remarks-subject' => 'Uwagi dla rezerwacji',
    'link-was-used' => 'Wybrany link jest linkiem jednorazowym i został już wykorzystany',

    'welcome-to-mindento' => 'Witamy w Mindento!',
    'welcome-header' => 'Cześć, <br><br> Właśnie rozpoczynasz korzystanie z Mindento. To idealny moment by przekazać Ci podstawowe informacje i najważniejsze wskazówki.',
    'welcome_par_header_1' => 'Mindento jest aplikacją do rozliczania wydatków i obsługi delegacji.  Możesz w niej: zrobić zdjęcia faktur i elektronicznie rozliczyć swoje wydatki, jak również zarezerwować noclegi oraz bilety.',
    'welcome_par_header_2' => 'Zanim rozpoczniesz upewnij się, że masz ustawioną domyślnie inną przeglądarkę niż Internet Explorer. Rekomendujemy Chrome, Edge lub Firefox.',
    'your-credentials' => 'Dane logowania',
    'your-domain-is' => 'Adres aplikacji: ',
    'your-login-is' => 'Nazwa użytkownika: ',
    'your-password' => 'Hasło: ',
    'support-contact-email' => 'Jeżeli będziesz miał pytania związane z uruchomieniem lub działaniem aplikacji, kieruj zgłoszenia do <a href="mailto:<EMAIL>"><EMAIL></a>',

    'travel-support-contact' => 'Zgłoszenia dotyczące rezerwacji obsługuje Zespół Wsparcia Podróży: <EMAIL>  lub +48 22 101 33 23',
    'download-app-remainder' => 'Nie zapomnij zainstalować aplikacji mobilnej Mindento w aktualnej wersji, którą znajdziesz w',
    'request-created-by-agent-message' => 'Agent Podróży utworzył dla Ciebie wniosek.',
    'request-created-by-agent-subject' => 'Agent Podróży utworzył dla Ciebie wniosek',
    'click-our-magic-link' => 'Po zainstalowaniu wersji mobilnej otwórz wiadomość powitalną w skrzynce pocztowej na telefonie i kliknij <b>Magiczny Link</b>. Aplikacja zostanie skonfigurowana automatycznie',
    'click-here-magic-link' => 'kliknij tutaj: Magiczny Link',
    'login-to-app-mobile' => 'Do aplikacji webowej możesz zalogowac się <a href=" :link ">tutaj</a> korzystając z zintegrowanego logowania Microsoft Azure. Twój login i hasło jest taki sam jak do innych firmowych aplikacji',


    'log-in-to-web-version' => 'Wejdź na stronę aplikacji webowej.',
    'generate-password' => 'Wygeneruj hasło.',
    'generate-password-sso' => 'Jeżeli korzystasz z SSO  - użyj hasła, którego używasz logując do innych aplikacji',
    'generate-password-without-sso' => 'W pozostałych przypadkach wygeneruj hasło:',
    'password_reset_url' => 'link do generatora hasła',
    'download-and-configure-mobile-app' => 'Zainstaluj wersję mobilną aplikacji.',
    'find-our-application-in' => 'Aplikację mobilną w aktualnej wersji znajdziesz w ',
    'open-this-email-in-mailbox-at-your-smartfon-and-click-magic-link' => 'Po zainstalowaniu otwórz tę samą wiadomość w  skrzynce pocztowej na swoim telefonie i kliknij na ',
    'welcome-open-application-and-enter-domain' => 'Po zainstalowaniu otwórz aplikację, wpisz twój-url: :domainSlug i zaloguj się używając danych jak przy logowaniu do aplikacji webowej.',
    'welcome-your-pin-code' => 'Kod PIN do akceptacji wniosków i rozliczeń: ',
    'first-login-attention' => 'Uwaga! Po pierwszym logowaniu w aplikacji internetowej, utwórz własne hasło oraz kod PIN.',
    'magic-link-conf' => 'Magiczny Link Konfiguracyjny',
    'magic-link' => 'Magiczny Link',
    'mobile-app-will-be-configured-automatically' => 'Aplikacja zostanie skonfigurowana automatycznie.',
    'click' => 'Kliknij:',
    'ta-dah' => 'TADAM!',
    'still-need-help' => 'Potrzebujesz pomocy?',
    'it-is-wonderful-to-welcome-you-on-board' => 'Cieszymy się, że jesteś z nami,<br>Zespół Mindento',
    'settlement-link'=>'Link do rozliczenia:',

    'login-to-application-by-microsoft-azure' => 'Zaloguj się przez Microsoft Azure korzystając z loginu i hasła, których używasz do innych firmowych aplikacji.',
    'user-name' => 'Nazwa użytkownika: ',
    'payment-failed' => 'Płatność dla elementu :element we wniosku :request zakończona niepowodzeniem',
    'payment-failed-title' => 'Płatność zakończona niepowodzeniem',
    'notification.request-auto-reservation-failed-subject' => 'Rezerwacja podróży :name wygasła i wymaga ponownego wyszukania',
    'notification.request-auto-reservation-failed-message' => 'Automatyczna rezerwacja dla :user z wniosku :name wygasła. Wejdź do aplikacji i wyszukaj ponownie',
    'notification.request-reservation-failed-subject' => 'Próba rezerwacji zakończona niepowodzeniem',
    'notification.request-reservation-failed-message' => 'Wykup ofert z Twojego wniosku :name nie był możliwy. Wejdź do aplikacji i wyszukaj ponownie.',
    'request-installment-has-been-paid-message-for-trip' => 'Twoja zaliczka :name w dniach :trip_starts - :trip_ends  w kwocie :amount :currency  kurs :exchange_rate została wypłacona',
    'request-installment-has-been-paid-message-for-expense' => 'Twoja zaliczka :name w kwocie :amount :currency  kurs :exchange_rate została wypłacona',
    'request-installment-has-been-paid-message-without-currency-for-trip' => 'Twoja zaliczka :name w dniach :trip_starts - :trip_ends  w kwocie :amount :currency została wypłacona',
    'request-installment-has-been-paid-message-without-currency-for-expense' => 'Twoja zaliczka :name w kwocie :amount :currency została wypłacona',
    'request-installment-has-been-paid-subject' => 'Prośba o zaliczkę :user została zrealizowana',

    'voucher-issued-subject' => 'Rezerwacja zakończona powodzeniem :reservation_name_subject',
    'voucher-issued-message' => 'Wykup oferty z Twojego wniosku Wyjazd do :trip_name w dniach :trip_starts - :trip_ends zakończony powodzeniem. Szczegóły rezerwacji :reservation_name_message w załączniku ',
    'voucher-issued-message_alert' => 'Uwaga: :reservation_error_message',

    'accommodation-extra-services-requested-subject' => 'Special request for :full_name :hotel_name',
    'accommodation-extra-services-requested-message-part-1' => 'Special request:',
    'accommodation-extra-services-requested-message-part-2' => 'for a stay at:',
    'accommodation-extra-services-requested-booking-number' => 'Booking number: :booking_number',
    'accommodation-extra-services-requested-guest-name' => 'Guest name: :full_name',
    'accommodation-extra-services-requested-check-in-date' => 'Check in date: :check_in_date',
    'accommodation-extra-services-requested-check-out-date' => 'Check out date: :check_out_date',
    'accommodation-extra-services-requested-request-number' => 'Request ID: :request_number',

    'inactive-periodic-request-expense-cancel-subject' => 'Robiliśmy porządki w aplikacji Mindento',
    'inactive-periodic-request-expense-cancel-message' => 'Zauważyliśmy, że masz na swoim koncie rozliczenia wydatków bez żadnych dokumentów. Nasz robot robił w aplikacji porządki i je usunął. Lista Twoich rozliczeń jest teraz bardziej przejrzysta.',

    'claim-changed-total-amount-subject' => 'Twoje rozliczenie zmieniło wartość',
    'claim-changed-total-amount-message' => 'Twoje rozliczenie zmieniło wartość z :old_amount na :new_amount',
    'add-to-outlook-calendar-button-text' => 'Dodaj do kalendarza',

    'calendar-event-ticket-number' => 'Numer biletu',
    'calendar-event-passenger-name' => 'Pasażer',
    'calendar-event-guest-name' => 'Gość',
    'calendar-event-departure-date' => 'Data wyjazdu',
    'calendar-event-arrival-date' => 'Data przyjazdu',
    'calendar-event-flight-departure-date' => 'Data wylotu',
    'calendar-event-flight-arrival-date' => 'Data przylotu',
    'calendar-event-hotel-check-in-date' => 'Data zameldowania',
    'calendar-event-hotel-check-out-date' => 'Data wymeldowania',
    'calendar-event-request-number' => 'ID wniosku',
    'calendar-event-reservation-number' => 'Numer rezerwacji',
    'calendar-event-flight-number' => 'Numer lotu',
    'calendar-event-booking-number' => 'Booking number',
    'calendar-event-airline-name' => 'Nazwa linii lotniczej',
    'calendar-event-flight-prefix' => 'Lot',
    'calendar-event-train-prefix' => 'Pociąg',
    'calendar-event-hotel-prefix' => 'Hotel'
];
