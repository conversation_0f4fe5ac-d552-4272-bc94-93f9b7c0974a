<?php

return [
    'header__top-tooltip-content-1' => 'Zaznacz gdy zapewnione',
    'header__top-tooltip-content-2-1' => 'Ryczałt za nocleg: Zaznacz gdy ponosisz koszt noclegu we własnym zakresie',
    'header__top-tooltip-content-2-2' => 'Ryczałt za przejazdy lokalne: Zaznacz gdy w miejscu docelowym korzystasz z komunikacji miejskiej',
    'header__top-cell-1' => 'Pomniejszenia diet',
    'header__top-cell-2' => 'Ryczałty',
    'header__bottom-cell-1' => 'Czas według strefy lokalnej',
    'header__bottom-cell-2' => 'Śniadanie',
    'header__bottom-cell-3' => 'Obiad',
    'header__bottom-cell-4' => 'Kolacja',
    'header__bottom-cell-5' => 'Nocleg',
    'header__bottom-cell-6' => '<PERSON><PERSON><PERSON>azdy lokalne',
    'header__bottom-cell-7' => 'Noclegi Przejazdy lokalne',
    'header__top-tooltip-content-3' => 'Ryczałt za nocleg: Zaznacz gdy ponosisz koszt noclegu we własnym zakresie',
    'header__top-tooltip-content-4' => 'Ryczałt na przejazdy lokalne: Zaznacz gdy w miejscu docelowym korzystasz z komunikacji miejskiej',
    'day-element-border-crossing-container__border-crossing' => 'Przekroczenie granicy:',
    'day-element-border-crossing-container__target-country' => ' - Kraj docelowy',
    'form__country' => 'Państwo',
    'form__target' => 'Kraj docelowy',
    'form__date' => 'Data',
    'form__save-button' => 'Zapisz',
    'expense-container__add-button' => 'Dodaj przekroczenie granicy',
	'national-trip' => 'Krajowa',
	'abroad-trip' => 'Zagraniczna',
	'trip-type' => 'Rodzaj i przebieg podróży',
	'declarations' => 'Deklaracja diet',
	'back-to-border-crossings' => 'Wróć do harmonogramu',
	'go-to-deductions' => 'Zapisz i przejdź do deklaracji diet',
	'go-to-deductions-read' => 'Przejdź do deklaracji diet',
	'border-crossing' => 'Przekroczenie granicy',
	'destination-country' => 'Kraj docelowy',
    'current-deductions-status' => 'Aktualny status rozliczenia diet',
	'trip' => 'Rodzaj podróży',
	'trip-start' => 'Rozpoczęcie podróży',
	'trip-end' => 'Zakończenie podróży',
	'trip-process' => 'Przebieg podróży',
	'add-crossing' => 'Dodaj przekroczenie granicy',
	'add-target' => 'Dodaj miejsce docelowe',
    'tooltip-strong-1' => 'Miejscem rozpoczęcia i zakończenia podróży służbowej ',
    'tooltip-content-1' => 'jest miejscowość, w której znajduje się siedziba pracodawcy lub stałe miejsce wykonywania pracy. Po uzgodnieniu z pracodawcą, miejscem takim może być również miejscowość zamieszkania pracownika.',
    'tooltip-strong-2' => 'Rozpoczęciem podróży służbowej ',
    'tooltip-content-2' => 'jest czas opuszczenia miejscowości, w której zaczyna się wyjazd i przyjmuje się, że jest nim np. godzina odjazdu pociągu, wylotu samolotu.',
    'tooltip-strong-3' => 'Zakończeniem wyjazdu służbowego ',
    'tooltip-content-3' => 'jest godzina dotarcia do miejscowości wskazanej, jako miejsce zakończenia podróży, np. czas przyjazdu pociągu, przylotu samolotu.',
	'target-points' => 'Miejsca docelowe',
	'date-auto-suggested-tooltip-content' => 'Czas przekroczenia granicy zaproponowany automatycznie. Sprawdź poprawność podpowiedzi',
	'time'=> 'Czas lokalny',
	'local-time'=> 'Czas miejscowy',
	'accommodation'=> 'Nocleg',
	'local-trips' => 'Przejazdy lokalne',
	'breakfast' => 'Śniadanie',
	'dinner' => 'Kolacja',
	'lunch' => 'Obiad',
    'target-point' => 'Miejsce docelowe',
    'accommodation-confirmation-message' => 'Warunki otrzymania ryczałtu nie zostały spełnione.<br> Podróż poza porą nocną lub trwała krócej niż 6 godzin',
	'confirm-accommodation' => 'Tak, potwierdzam',
	'accept-accommodation' => 'Tak, rozumiem',
    'accommodation-cant-be-obtained' => 'Warunki otrzymania ryczałtu nie zostały spełnione. Podróż poza porą nocną lub trwała krócej niż 6 godzin',
    'price-included-breakfast' => 'Czy na pewno? Cena noclegu obejmowała śniadanie',
    'confirm-widget-title' => 'Czy na pewno?',
    'confirm-cancel-trip' => 'Tak, potwierdzam',
	'go-to-deductions-read-only' => 'Przejdź do deklaracji diet'
];
