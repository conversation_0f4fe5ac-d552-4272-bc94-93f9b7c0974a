<?php

return [
    /**
    * NOWE TŁUMACZENIA - które zostały znalezione w kodzie, a nie występują w plikach tłumaczeń.
    */

    /**
    * PUSTE TŁUMACZENIA - które występują zarówno w plikach tłumaczeń oraz w kodzie, ale nie mają przypisanej wartości
    */

    /**
    * USUNIĘTE TŁUMACZENIA - które występują w plikach tłumaczeń, a nie zostały znalezione w kodzie
    */

    /**
    * POZOSTAŁE TŁUMACZENIA - były dostępne wcześniej i mają tłumaczenie
    */
    'request-status-changed-to-acceptance-of-settlement' => 'Status wniosku zmieniony na zatwierdzenie rozliczenia',
    'request-status-changed-to-accounting' => 'Status wniosku zmieniony na dekretacja',
    'request-status-changed-to-canceled' => 'Status wniosku zmieniony na anulowany',
    'request-status-changed-to-draft' => 'Status wniosku zmieniony na wersję roboczą',
    'request-status-changed-to-finish' => 'Status wniosku zmieniony na zaksięgowany',
    'request-status-changed-to-transferred' => 'Status wniosku zmieniony na przeslany',
    'request-status-changed-to-rejected' => 'Status wniosku zmieniony na odrzucony',
    'request-status-changed-to-upcoming-trip' => 'Status wniosku zmieniony na nadchodzącą podróż',
    'request-status-changed-to-settlement' => 'Status wniosku zmieniony na rozliczenie',
    'request-status-changed-to-trip' => 'Status wniosku zmieniony na podróż',
    'request-status-changed-to-waiting-for-acceptance' => 'Status wniosku zmieniony na akceptację wniosku',
    'request-status-changed-to-transfer-error' => 'Status wniosku zmieniony na transfer nieudany',

    /**
     * Wniosek
     */
    'request-acceptor-accepted' => 'Użytkownik :user zaakceptował wniosek',
    'request-proxy-accepted' => 'Użytkownik :proxy zaakceptował wniosek w imieniu użytkownika :user',

    'request-acceptor-rejected' => 'Użytkownik :user odrzucił wniosek',
    'request-proxy-rejected' => 'User :proxy odrzucił wniosek w imieniu użytkownika :user',

    'request-acceptor-returned-for-improvement' => 'Użytkownik :user zwrócił wniosek do poprawy',
    'request-proxy-returned-for-improvement' => 'Użytkownik :proxy zwrócił wniosek do poprawy w imieniu użytkownika :user',

    'request-set-as-unrealized-agent' => 'Agent Podróży wybrał opcję Podróż się nie odbyła i anulował niewykupione rezerwacje',
    'request-set-as-unrealized-proxy' => 'Użytkownik :proxy wybrał opcję Podróż się nie odbyła i anulował niewykupione rezerwacje w imieniu użytkownika :user',
    'request-set-as-unrealized-user' => 'Użytkownik :user wybrał opcję Podróż się nie odbyła i anulował niewykupione rezerwacje',

    'request-finish-your-trip-right-now-agent' => 'Agent Podróży wybrał opcję Zakończ podróż już teraz i anulował niewykupione rezerwacje',
    'request-finish-your-trip-right-now-proxy' => 'Użytkownik :proxy wybrał opcję Zakończ podróż już teraz i anulował niewykupione rezerwacje w imieniu użytkownika :user',
    'request-finish-your-trip-right-now-user' => 'Użytkownik :user wybrał opcję Zakończ podróż już teraz i anulował niewykupione rezerwacje',


    /**
     * Rozliczenie
     */
    'request-accountant-accepted' => 'Użytkownik :user wysłał rozliczenie do ERP',
    'proxy-request-accountant-accepted' => 'Użytkownik :proxy wysłał rozliczenie do ERP w imieniu użytkownika :user',

    'request-accountant-returned-for-improvement' => 'Użytkownik :user zwrócił rozliczenie do poprawy',
    'proxy-accountant-returned-for-improvement' => 'Użytkownik :proxy zwrócił rozliczenie do poprawy w imieniu użytkownika :user',

    'request-settlement-acceptor-accepted' => 'Użytkownik :user zatwierdził rozliczenie',
    'proxy-settlement-acceptor-accepted' => 'Użytkownik :proxy zatwierdził rozliczenie w imieniu użytkownika :user',

    'request-acceptor-settlement-returned-for-improvement' => 'Użytkownik :user zwrócił rozliczenie do poprawy',
    'proxy-acceptor-settlement-returned-for-improvement' => 'Użytkownik :proxy zwrócił rozliczenie do poprawy w imieniu użytkownika :user',

    'user-changed-status-to-acceptance-of-settlement' => 'Użytkownik :user wysłał rozliczenie do zatwierdzenia',
    'proxy-changed-status-to-acceptance-of-settlement' => 'Użytkownik :proxy wysłał rozliczenie do zatwierdzenia w imieniu użytkownika :user',

    /**
     * Wniosek
     */
    'user-sent-request-to-acceptance' => 'Użytkownik :user wysłał wniosek do akceptacji',
    'proxy-sent-request-to-acceptance' => 'Użytkownik :proxy wysłał wniosek do akceptacji w imieniu użytkownika :user',

    'user-canceled-request' => 'Użytkownik :user anulował wniosek',
    'proxy-canceled-request' => 'Użytkownik :proxy anulował wniosek w imieniu użytkownika :user',

    'user-assigned-accounting-of-this-request' => 'Użytkownik :user przypisał księgowanie rozliczenia na siebie',
    'proxy-assigned-accounting-of-this-request' => 'Użytkownik :proxy przypisał księgowanie rozliczenia na użytkownika :user',

    'request-auto-accepted' => 'Wniosek zaakceptowany automatycznie przez :user',
    'request-auto-reserved' => 'Podjęto próbę automatycznej rezerwacji',

    'request-auto-reserved-offer' => 'Podjęto próbę automatycznej rezerwacji (:offer)',
    'request-auto-reserved-success' => 'Automatyczna rezerwacja zakończyła się powodzeniem (:offer) ',
    'request-auto-reserved-failure' => 'Automatyczna rezerwacja zakończyła się niepowodzeniem (:offer)',

    'request-manual-reserved-offer' => 'Podjęto próbę rezerwacji (:offer)',
    'request-manual-reserved-success' => 'Użytkownik :user_full_name dokonał rezerwacji (:offer)',
    'request-manual-reserved-failure' => 'Rezerwacja zakończyła się niepowodzeniem (:offer)',

    'request-auto-approved' => 'Rozliczenie zatwierdzone automatycznie przez :user',

    'auto-reserved-failure' => 'Próba automatycznej rezerwacji zakończyła się niepowodzeniem',
    'auto-reserved-failure-offer' => 'Próba automatycznej rezerwacji zakończyła się niepowodzeniem (:offer)',

    'you-cannot-delete-approver' => 'Nie możesz zmienić akceptującego',
    'you-cannot-delete-settlement-approver' => 'Nie możesz zmienić zatwierdzającego',

    /**
     * Agent
     */
    'agent-request-acceptor-accepted' => 'Agent Podróży zaakceptował wniosek',
    'agent-request-acceptor-rejected' => 'Agent Podróży odrzucił wniosek',
    'agent-request-acceptor-returned-for-improvement' => 'Agent Pordróży zwrócił wniosek do poprawy',

    'agent-request-accountant-accepted' => 'Agent Podróży wysłał rozliczenie do ERP',
    'agent-request-accountant-returned-for-improvement' => 'Użytkownik Agent Podróży zwrócił rozliczenie do poprawy',
    'agent-request-settlement-acceptor-accepted' => 'Agent Podróży zatwierdził rozliczenie',
    'agent-request-acceptor-settlement-returned-for-improvement' => 'Agent Podróży zwrócił rozliczenie do poprawy',
    'agent-user-changed-status-to-acceptance-of-settlement' => 'Agent Podróży wysłał rozliczenie do zatwierdzenia',

    'agent-request-auto-accepted' => 'Wniosek zaakceptowany automatycznie przez Agenta Podróży',
    'agent-sent-request-to-acceptance' => 'Agent Podróży wysłał wniosek do akceptacji',
    'agent-canceled-request' => 'Agent Podróży anulował wniosek',
    'agent-assigned-accounting-of-this-request' => 'Agent Podróży przypisał księgowanie rozliczenia na siebie',

    'communication-error-cccurred' => 'Wystąpił problem z połączeniem z systemem ERP',
    'employee-part-of-the-claim' => 'Księgowanie-Pracownik',
    'user-removed-assigned-accountant-from-request' => 'Użytkownik :user anulował przypisanie rozliczenia',
    'proxy-user-removed-assigned-accountant-from-request' => 'Użytkownik :proxy anulował przypisanie rozliczenia na użytkownika :user',

    'user-cancelled-reservation' => 'Użytkownik :user_full_name anulował rezerwację :reservation_name',
    'user-cancelled-reservation-as-someone' => 'Użytkownik :original_user_full_name anulował rezerwację :reservation_name w imieniu użytkownika :logged_as_user_full_name',
    'reservation-cancel-finished' => 'Anulowanie rezerwacji zakończone sukcesem (:offer)',

    'status-auto-changed' => 'Zmiana automatyczna (:status, :date, :time)'
];
