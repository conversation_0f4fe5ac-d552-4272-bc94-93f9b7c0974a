<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRuleAllOfferTravelersAreRequestTravelersRule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Instance::all()->each(function (\App\Instance $instance) {
            $rule = new \App\Rule();
            $rule->level = \App\Rule::LEVEL_ERROR;
            $rule->instance_id = $instance->id;
            $rule->name = \App\Services\RulesService\Rules\AllOfferTravelersAreRequestTravelersRule::NAME;
            $rule->parameters = [];
            $rule->save();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \App\Rule::query()->where('name', '=', \App\Services\RulesService\Rules\AllOfferTravelersAreRequestTravelersRule::NAME)->delete();
    }
}
