<?php

use Illuminate\Database\Migrations\Migration;

class FixRolesPermissions extends Migration
{
    public function up(): void
    {
        $administratorGroupIds = \App\Group::query()
            ->where('name', \App\User::GROUP_NAME_ADMINISTRATOR)
            ->pluck('id')
            ->toArray();

        \App\Permission::query()
            ->whereIn('group_id', $administratorGroupIds)
            ->where('ability', \App\Permission::REGULAR_REQUEST_EXPENSE)
            ->update(['can' => true]);
    }

    public function down(): void
    {
    }
}
