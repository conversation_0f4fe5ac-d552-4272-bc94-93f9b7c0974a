<?php

use \App\Vendors\Blueprint;
use \App\Vendors\Migration;

class CreateInstallmentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('installments', function (Blueprint $table) {
            $table->increments('id');
            $table->request();
            $table->instance();
            $table->decimal('amount', 15, 2);
            $table->timestamp('date')->nullable(true);
            $table->string('status');
            $table->currency();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('installments');
    }
}
