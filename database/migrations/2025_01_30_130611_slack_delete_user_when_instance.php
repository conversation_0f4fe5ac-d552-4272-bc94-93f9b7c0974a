<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SlackDeleteUserWhenInstance extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('slack_users', function (Blueprint $table) {
            $table->unsignedInteger('slack_instance_id')->change();
            $table->foreign('slack_instance_id')
                ->references('id')
                ->on('slack_instances')
                ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::table('slack_users', function (Blueprint $table) {
            $table->dropForeign(['slack_instance_id']);
        });
    }
}
