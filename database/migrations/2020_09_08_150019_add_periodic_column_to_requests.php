<?php

declare(strict_types=1);

use App\Group;
use App\Instance;
use App\Permission;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\DocumentElementGroup;
use Modules\Accounting\Priv\Entities\DocumentElementType;
use Modules\Accounting\Priv\Enum\AccountingAccountKindEnum;
use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;

class AddPeriodicColumnToRequests extends Migration
{
    public function up()
    {
        Schema::table('requests', function (Blueprint $table) {
            $table->boolean('periodic')->default(false);
        });

        /** @var Collection $groups */
        $groups = Group::where([])->get();
        $groups->each(function(Group $group) {
            $permission = new Permission();
            $permission->group_id = $group->id;
            $permission->ability = Permission::PERIODIC_REQUEST_EXPENSE;
            $permission->name = str_random(16);
            $permission->can = false;
            $permission->instance_id = $group->instance_id;
            $permission->save();
        });

        Instance::all()->each(function (Instance $instance) {
            $modulesSetting = $instance->modules;
            $modulesSetting['periodic_request_expenses']['enabled'] = false;
            $instance->modules = $modulesSetting;
            $instance->save();

            $accountingAccount = $instance->instanceAccountingAccount(AccountingAccountKindEnum::TECHNICAL(), AccountingAccountTypeEnum::DEBIT());

            $documentElementGroup = new DocumentElementGroup();
            $documentElementGroup->instance_id = $instance->id;
            $documentElementGroup->name = 'periodic';
            $documentElementGroup->slug = 'expense-periodic';
            $documentElementGroup->order = DocumentElementGroup::where('instance_id', $instance->id)
                ->orderBy('order', 'DESC')
                ->first()
                ->order++;
            $documentElementGroup->is_active = true;
            $documentElementGroup->save();

            $documentElementType = new DocumentElementType();
            $documentElementType->document_element_group_id = $documentElementGroup->id;
            $documentElementType->instance_id = $instance->id;
            $documentElementType->is_active = false;
            $documentElementType->accounting_account_id = $accountingAccount->id;
            $documentElementType->vat_number_id = null;
            $documentElementType->order = DocumentElementType::where('instance_id', $instance->id)
                ->orderBy('order', 'DESC')
                ->first()->order++;
            $documentElementType->name = 'periodic';
            $documentElementType->slug = 'expense-periodic-group-periodic-type';
            $documentElementType->visible_in_trip = false;
            $documentElementType->visible_in_expense = false;
            $documentElementType->exchange_rate_from = DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE;
            $documentElementType->save();
        });
    }

    public function down(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            $table->dropColumn('periodic');
        });

        /** @var \Illuminate\Database\Eloquent\Collection $groups */
        Permission::where('ability', Permission::PERIODIC_REQUEST_EXPENSE)->get()->each(function (Permission $permission) {
            $permission->delete();
        });

        DocumentElementGroup::where([
                ['name', '=', 'periodic'],
                ['slug', '=', 'expense-periodic']
            ])
            ->get()
            ->each(function (DocumentElementGroup $documentElementGroup) {
                $documentElementGroup->delete();
            });

        DocumentElementType::where([
                ['name', '=', 'periodic'],
                ['slug', '=', 'expense-periodic-group-periodic-type']
            ])
            ->get()
            ->each(function (DocumentElementType $documentElementType) {
                $documentElementType->delete();
            });

    }
}
