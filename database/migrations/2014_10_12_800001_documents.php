<?php

class Documents extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('documents', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();

            $table->instance();
            $table->user();
            $table->request();

            $table->string('type')->nullable();

            $table->string('name');

            $table->string('file_path');
            $table->string('file_name');
            $table->string('file_optimized')->default('');
            $table->string('file_extension');

            $table->text('ocr_data')->nullable();
            $table->smallInteger('ocr_time')->nullable();
            $table->string('status', 15)->nullable();
            $table->boolean('ocr_retry')->default(false);

            //accounts
            $table->integer('debit_account_id')->unsigned()->index()->nullable(true);
            $table->foreign('debit_account_id')->references('id')->on('accounting_accounts')->onDelete('cascade');
            $table->integer('credit_account_id')->unsigned()->index()->nullable(true);
            $table->foreign('credit_account_id')->references('id')->on('accounting_accounts')->onDelete('cascade');

            //accounting fields
            $table->provider(true);
            $table->string('document_number')->nullable();
            $table->string('document_accounting_number')->nullable();
            $table->timestamp('issue_date')->nullable();
            $table->timestamp('received_date')->nullable();
            $table->timestamp('accounting_date')->nullable();
            $table->string('annotation')->nullable();
            $table->amount(true, 'gross');
            $table->currency(true);
            $table->decimal('exchange_rate', 10, 4)->nullable();
            $table->string('payment')->nullable();
            $table->string('accounting_type')->nullable();
            //accounting fields end

            $table->boolean('requested')->default(true);
            $table->boolean('readonly')->default(false);

            //status dates
            $table->timestamp('settled_at')->nullable();
            $table->timestamp('accounted_at')->nullable();
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('documents');
    }
}
