<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RequestsIndexes extends Migration
{
    public function up()
    {
        Schema::table('requests', function (Blueprint $table) {
            $table->index('accounted_at');
            $table->index('finished_at');
            $table->index('erp_accounted_at');
            $table->index('erp_vat_at');
            $table->index('sent_at');
            $table->index('settled_at');
        });
    }

    public function down()
    {
        Schema::table('requests', function (Blueprint $table) {
            $table->dropIndex('accounted_at');
            $table->dropIndex('finished_at');
            $table->dropIndex('erp_accounted_at');
            $table->dropIndex('erp_vat_at');
            $table->dropIndex('sent_at');
            $table->dropIndex('settled_at');
        });
    }
}
