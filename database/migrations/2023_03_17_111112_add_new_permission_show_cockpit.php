<?php

use Illuminate\Database\Migrations\Migration;

class AddNewPermissionShowCockpit extends Migration
{
    public function up(): void
    {
        $now = \Carbon\Carbon::now();
        $configPermissions = config('permissions')['default_permissions'];
        $permissions = [];

        \App\Group::all()->each(function (\App\Group $group) use ($now, $configPermissions, &$permissions) {
            if (isset($configPermissions[$group->name]) && isset($configPermissions[$group->name][\App\Permission::SHOW_COCKPIT])) {
                $permissions[] = [
                    'created_at' => $now,
                    'updated_at' => $now,
                    'group_id' => $group->id,
                    'ability' => \App\Permission::SHOW_COCKPIT,
                    'scope' => null,
                    'name' => \Illuminate\Support\Str::random(),
                    'can' => $configPermissions[$group->name][\App\Permission::SHOW_COCKPIT],
                    'instance_id' => $group->instance_id
                ];
            }
        });

        \App\Permission::insert($permissions);
    }

    public function down(): void
    {
        \App\Permission::query()
            ->where('ability', \App\Permission::SHOW_COCKPIT)
            ->delete();
    }
}
