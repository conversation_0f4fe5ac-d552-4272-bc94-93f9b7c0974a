<?php

use App\Vendors\Blueprint;
use App\Vendors\Migration;

class CreateTravelExpensesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('travel_expenses', function (Blueprint $table) {
            $table->increments('id');
            $table->currency();
            $table->country(true);
            $table->decimal('expenses', 10, 3)->unsigned();
            $table->decimal('accommodation_limit', 10, 3)->unsigned();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('travel_expenses');
    }
}
