<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\DocumentElementGroup;

class CreateOrUpdateNewDocumentElementGroupWithTypes extends Migration
{
    private const GROUP_SLUG = 'expense-other-group';
    private const ACCOMODATION_GROUP_SLUG = 'expense-accommodatiom-group';
    private const ACCOMODATION_TYPE_NAME = 'hotel-non-statutory';
    private const TYPE_POSTFIX = '-type';
    private const TYPE_NAMES = [
        'post',
        'subscriptions',
        'tools',
        'trainings',
        'tasting',
        'groceries',
        'administration-fees',
        'taxi',
        'qhs',
        'other-costs',
        'fixed-assets',
        'appliances'
    ];

    public function up(): void
    {
        $instances = \App\Instance::query()->with('documentElementGroups')->get();

        /**
         * @var \App\Instance $instance
         */
        foreach ($instances as $instance) {
            $groupOther = $this->getGroupOther($instance);

            if ($groupOther === null) {
                $groupOther = $this->createGroup($instance->id);
            }

            $groupOther->load('types');

            foreach (self::TYPE_NAMES as $typeName) {
                if ($this->typeExists($groupOther->types, $typeName)) {
                    continue;
                }

                $this->createType($groupOther, $typeName);
            }

            $this->newTypeInAccomodationGroup($instance);
        }
    }

    private function newTypeInAccomodationGroup(\App\Instance $instance): void
    {
        $accomodationGroup = $instance->documentElementGroups
            ->where('slug', self::ACCOMODATION_GROUP_SLUG)
            ->first();

        if ($accomodationGroup === null) {
            return;
        }

        $this->createType($accomodationGroup, self::ACCOMODATION_TYPE_NAME);
    }

    private function createType(DocumentElementGroup $group, string $typeName): void
    {
        $instance = $group->instance;
        $vatNumber = $instance->vatNumbers->first();

        $vatNumberId = null;

        if ($vatNumber) {
            $vatNumberId = $vatNumber->id;
        }

        \Modules\Accounting\Priv\Entities\DocumentElementType::insert([
            'instance_id' => $instance->id,
            'document_element_group_id' => $group->id,
            'vat_number_id' => $vatNumberId,
            'accounting_account_id' => $instance->technical_account_id,
            'is_active' => \App\Enum\ActiveEnum::ACTIVE(),
            'order' => $group->types()->count() + 1,
            'name' => $typeName,
            'slug' => $group->slug . '-' . $typeName . self::TYPE_POSTFIX,
            'visible_in_trip' => 1,
            'visible_in_expense' => 1,
            'created_at' => \Carbon\Carbon::now(),
            'updated_at' => \Carbon\Carbon::now(),
            'exchange_rate_from' => \Modules\Accounting\Priv\Entities\DocumentElementType::EXCHANGE_RATE_FROM_DOCUMENT_DATE
        ]);
    }

    private function typeExists(\Illuminate\Support\Collection $groupTypes, string $typeName): bool
    {
        return $groupTypes->where('slug', self::GROUP_SLUG . '-' . $typeName . self::TYPE_POSTFIX)->isNotEmpty();
    }

    private function getGroupOther(\App\Instance $instance): ?DocumentElementGroup
    {
        return $instance->documentElementGroups
            ->where('slug', self::GROUP_SLUG)
            ->first();
    }

    private function createGroup(int $instanceId): DocumentElementGroup
    {
        $order = 0;

        $highestOrderGroup = DocumentElementGroup::where('instance_id', $instanceId)
            ->orderBy('order', 'DESC')
            ->first();

        if ($highestOrderGroup) {
            $order = ++$highestOrderGroup->order;
        }
        $documentElementGroup = new DocumentElementGroup();
        $documentElementGroup->instance_id = $instanceId;
        $documentElementGroup->name = 'other';
        $documentElementGroup->slug = self::GROUP_SLUG;
        $documentElementGroup->order = $order;
        $documentElementGroup->is_active = true;
        $documentElementGroup->save();

        return $documentElementGroup;
    }

    public function down(): void
    {
        // types will be deleted by cascade
        \App\Instance::query()->with('documentElementGroups')->each(
            function (\App\Instance $instance) {
                $group = $this->getGroupOther($instance);
                if ($group !== null) {
                    $group->delete();
                }
            }
        );
    }
}
