<?php

class RequestElementDocument extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('request_element_document', function(\App\Vendors\Blueprint $table) {
            $table->document();
            $table->integer('element_id')->unsigned()->nullable();
            $table->string('element_type')->nullable();

            $table->timestamps();
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('request_element_document');
    }
}
