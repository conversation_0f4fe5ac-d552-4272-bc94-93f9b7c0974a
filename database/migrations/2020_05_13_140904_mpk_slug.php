<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MpkSlug extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $mpks = \Modules\Accounting\Priv\Entities\Mpk::withTrashed()->get();

        Schema::table('mpks', function (Blueprint $table) {
            $table->string('slug', 36)->after('id');
            $table->boolean('is_active')->nullable(false)->default(true)->after('company_id');
            $table->integer('created_by')->nullable(true)->unsigned()->default(null)->references('id')->on('users')->onDelete('SET NULL');
            $table->integer('updated_by')->nullable(true)->unsigned()->default(null)->references('id')->on('users')->onDelete('SET NULL');
        });

        /** @var \Modules\Accounting\Priv\Entities\Mpk $mpk */
        foreach ($mpks as $mpk) {
            $mpk->slug = \Ramsey\Uuid\Uuid::uuid4();
            $mpk->is_active = ($mpk->deleted_at !== null ? \App\Enum\ActiveEnum::INACTIVE() : \App\Enum\ActiveEnum::ACTIVE());
            $mpk->save();
        }

        Schema::table('mpks', function (Blueprint $table) {
            $table->string('slug', 36)->after('id')->comment('')->nullable(false)->change();
            $table->unique('slug', 'mpks_slug_unique');
        });

        /** @var \Illuminate\Support\Collection $groups */
        $groups = \App\Group::where([])->get();
        $groups->each(function(\App\Group $group) {
            $permission = new \App\Permission();
            $permission->group_id = $group->id;
            $permission->ability = \App\Permission::DICTIONARIES_MANAGE;
            $permission->name = \str_random(16);
            $permission->can = ($group->name === \App\User::GROUP_NAME_ADMINISTRATOR);
            $permission->instance_id = $group->instance_id;
            $permission->save();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('mpks', function (Blueprint $table) {
            $table->dropUnique('mpks_slug_unique');
            $table->dropColumn('slug');
            $table->dropColumn('is_active');
            $table->dropColumn('created_by');
            $table->dropColumn('updated_by');
        });

        \App\Permission::where(['ability' => \App\Permission::DICTIONARIES_MANAGE])->delete();

    }
}
