<?php

use App\HasManyMpkCollection;
use App\User;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SyncMpk extends Migration
{
    public function up()
    {
        $results = User::query()
            ->select(['users.id as id', 'users.mpk_id'])
            ->leftJoin('user_has_mpk as m', 'm.user_id', '=', 'users.id')
            ->whereNull('m.id')
            ->get();

        $results->each(function (User $user) {
            $user->syncMpks(HasManyMpkCollection::createFromRequestData([
                [
                    'id' => $user->mpk_id,
                    'percentage' => 100,
                    'main' => true,
                ]
            ]));
        });
    }

    public function down()
    {
    }
}
