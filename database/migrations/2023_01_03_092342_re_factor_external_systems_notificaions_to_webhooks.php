<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ReFactorExternalSystemsNotificaionsToWebhooks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('external_systems_notifications', function (Blueprint $table) {
            $table->rename('webhook_attempts');
        });

        Schema::table('webhook_attempts', function (Blueprint $table) {
            $table->dropForeign("external_systems_notifications_request_id_foreign");
            $table->dropIndex("external_systems_notifications_request_id_unique");
            $table->string('type', 64)->after('id');
            $table->string("request_slug")->after("type");
        });

        \DB::statement("UPDATE webhook_attempts, requests SET webhook_attempts.request_slug = requests.slug, webhook_attempts.type = 'NewClaimTransferred' WHERE webhook_attempts.request_id = requests.id");

        Schema::table('webhook_attempts', function (Blueprint $table) {
            $table->dropColumn("request_id");
        });
    }

    public function down()
    {
        Schema::table('webhook_attempts', function (Blueprint $table) {
            $table->rename("external_systems_notifications");
        });

        \DB::statement("DELETE FROM external_systems_notifications WHERE type != 'NewClaimTransferred'");

        Schema::table('external_systems_notifications', function (Blueprint $table) {
            $table->integer("request_id")->unsigned()->after('id');
        });

        \DB::statement("UPDATE external_systems_notifications, requests SET external_systems_notifications.request_id = requests.id WHERE external_systems_notifications.request_slug = requests.slug");

        Schema::table('external_systems_notifications', function (Blueprint $table) {
            $table->integer("request_id")->unsigned()->unique()->change();
        });

        Schema::table('external_systems_notifications', function (Blueprint $table) {
            $table->foreign('request_id')->references('id')->on('requests')->onDelete('cascade')->onUpdate('cascade');
            $table->dropColumn("request_slug");
            $table->dropColumn('type');
        });
    }
}
