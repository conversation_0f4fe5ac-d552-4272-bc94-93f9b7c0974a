<?php

use Mo<PERSON>les\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Ramsey\Uuid\Uuid;

class CreateFeatures extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('features', function (Blueprint $table) {
            $table->increments('id');
            $table->string('slug', 64)->nullable(false)->unique();
            $table->string('type', 16)->nullable(false);
            $table->string('default', 32);
            $table->timestamps();
        });

        Schema::create('feature_settings', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('feature_id')->unsigned()->nullable(false);
            $table->foreign('feature_id')->references('id')->on('features');

            $table->integer('instance_id')->unsigned()->nullable(false);
            $table->foreign('instance_id')->references('id')->on('instances');

            $table->integer('company_id')->unsigned()->index()->nullable(true);
            $table->foreign('company_id')->references('id')->on('companies');

            $table->string('setting')->nullable(false);
            $table->timestamps();
            $table->index('instance_id', 'feature_settings_instance_id');
            $table->unique(['feature_id', 'instance_id', 'company_id'], 'feature_settings_unique');
        });

        $feature = new Feature();
        $feature->type = FeatureTypeEnum::SWITCH();
        $feature->slug = FeatureEnum::FEATURE_OTHER_COSTS_ARE_INCLUDED_IN_TRAVEL_POLICY();
        $feature->default = '1';
        $feature->save();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feature_settings');
        Schema::dropIfExists('features');
    }
}
