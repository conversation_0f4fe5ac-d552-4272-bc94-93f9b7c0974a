<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ChangeDefaultToPriorityColumnInCardUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('card_users', function (Blueprint $table) {
            $table->dropColumn('default');
            $table->smallInteger('priority')->unsigned()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('card_users', function (Blueprint $table) {
            $table->boolean('default')->default(false);
            $table->dropColumn('priority');
        });
    }
}
