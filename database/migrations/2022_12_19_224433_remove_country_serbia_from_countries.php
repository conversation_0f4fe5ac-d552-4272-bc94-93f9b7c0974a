<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migration;

class RemoveCountrySerbiaFromCountries extends Migration
{
    private const TABLES_DATA_TO_UPDATE = [
        'providers',
        'request_border_crossings',
        'target_points'
    ];

    private const TABLES_DATA_TO_REMOVE=[
        'accommodation_limits',
        'travel_expenses'
    ];

    public function up(): void
    {
        $countryToRemove = \App\Country::query()->where('polish_name', 'Serbia')->first();
        if (null === $countryToRemove) {
            return;
        }

        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        $countryToStay = \App\Country::query()
            ->where('country_code', $countryToRemove->country_code)
            ->where('id', '!=', $countryToRemove->id)
            ->first();

        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        foreach (self::TABLES_DATA_TO_UPDATE as $table) {
            \Illuminate\Support\Facades\DB::table($table)
                ->where('country_id', $countryToRemove->id)
                ->update(['country_id' => $countryToStay->id]);
        }

        foreach (self::TABLES_DATA_TO_REMOVE as $table) {
            \Illuminate\Support\Facades\DB::table($table)
                ->where('country_id', $countryToRemove->id)
                ->delete();
        }

        $countryToRemove->forceDelete();

        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }

    public function down(): void
    {
        // Cannot create down migration, because we have nothing to handle the changes
    }
}
