<?php

class AlterDocumentElementsProjectId extends \App\Vendors\Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->table('document_elements', function (\App\Vendors\Blueprint $table) {
            $table->project(true);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $this->getSchema()->table('document_elements', function (\App\Vendors\Blueprint $table) {
            $table->dropForeign('document_elements_project_id_foreign');
            $table->dropColumn('project_id');
        });
    }
}
