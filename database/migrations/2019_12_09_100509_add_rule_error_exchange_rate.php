<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRuleErrorExchangeRate extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $rules = collect([
            [
                'level' => App\Rule::LEVEL_ERROR,
                'name' => 'error_document_exchange_rate_rule',
                'parameters' => [
                    'enabled' => true
                ]
            ],
            [
                'level' => App\Rule::LEVEL_ERROR,
                'name' => 'error_border_crossing_exchange_rate_rule',
                'parameters' => [
                    'enabled' => true
                ]
            ]
        ]);

        \App\Instance::all()->each(function(\App\Instance $instance) use (&$rules) {

            $rules->each(function ($rule) use($instance) {
                $instanceRule = \App\Rule::where('instance_id', $instance->id)
                    ->where('name', $rule['name'])
                    ->first();

                if ( ! $instanceRule) {
                    $rule['instance_id'] = $instance;
                    factory(App\Rule::class)->create($rule);
                }
            });


        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
