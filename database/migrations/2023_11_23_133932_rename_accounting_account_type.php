<?php

use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;

class RenameAccountingAccountType extends Migration
{
    public function up(): void
    {
        AccountingAccount::withoutEvents(function () {
            AccountingAccount::get()->each(function (AccountingAccount $accountingAccount) {
                $accountingAccountType = AccountingAccountTypeEnum::DEBIT();

                if ($accountingAccount->getOriginal('type') === 'settlement') {
                    $accountingAccountType = AccountingAccountTypeEnum::CREDIT();
                }

                $accountingAccount->type = $accountingAccountType;
                $accountingAccount->save();
            });
        });
    }

    public function down(): void
    {
        AccountingAccount::withoutEvents(function () {
            AccountingAccount::get()->each(function (AccountingAccount $accountingAccount) {
                $accountingAccountType = 'normal';

                if ($accountingAccount->getOriginal('type') === AccountingAccountTypeEnum::CREDIT()->getValue()) {
                    $accountingAccountType = 'settlement';
                }

                $accountingAccount->type = $accountingAccountType;
                $accountingAccount->save();
            });
        });
    }
}
