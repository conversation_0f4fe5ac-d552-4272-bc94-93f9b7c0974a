<?php

use App\Vendors\Blueprint;
use App\Vendors\Migration;

class RequestPlaneTrips extends Migration
{
    public function up()
    {
        $this->getSchema()->create('request_plane_trips', function ( Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();
            $table->documentElementType();

            $table->string('flight_class');
            $table->dateTime('arrival_at');
            $table->boolean('round_trip');
            $table->dateTime('return_at')->nullable();
            $table->amount(true);
            $table->amountCurrency(true);

            $table->weight();
        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_plane_trips');
    }
}
