<?php

use App\Company;
use Illuminate\Support\Facades\Schema;
use App\Vendors\Blueprint;
use App\Vendors\Migration;
use Modules\DatatableConfig\Priv\Entities\DataTableConfig;
use Modules\DatatableConfig\Priv\Enums\ConfigTypeEnum;
use Modules\DatatableConfig\Priv\Enums\FieldTypeEnum;

class CreateDataTableConfig extends Migration
{
    private const ZBK_INSTANCES_ID = [52, 10000013];

    public function up(): void
    {
        $this->getSchema()->create('data_table_config', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->company();
            $table->string('type');
            $table->json('config');
            $table->unique(['type', 'company_id']);
        });

        $companies = Company::query()->whereIn('instance_id', self::ZBK_INSTANCES_ID)->get();

        foreach ($companies as $company) {
            factory(DataTableConfig::class)->create([
                'company_id' => $company->id,
                'type' => ConfigTypeEnum::SETTLEMENT,
                'config' => $this->getZbkConfig(),
            ]);
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('data_table_config');
    }

    private function getZbkConfig(): array
    {
        return
        [
            [
                'header' => 'request.settlement-date',
                'field' => 'settled_at',
                'sortField' => 'settled_at',
                'fieldType' => FieldTypeEnum::STRING(),
            ],
            [
                'header' => 'request.id-label',
                'field' => 'uid',
                'sortField' => 'number',
                'fieldType' => FieldTypeEnum::STRING(),
            ],
            [
                'header' => 'global.employee',
                'field' => 'user.full_name',
                'sortField' => 'user_last_name',
                'fieldType' => FieldTypeEnum::STRING(),
            ],
            [
                'header' => 'request.company_code',
                'field' => 'company_code',
                'sortField' => 'company_code',
                'fieldType' => FieldTypeEnum::STRING(),
            ],
            [
                'header' => 'request.accounted-date',
                'field' => 'accounted_at',
                'sortField' => 'accounted_at',
                'fieldType' => FieldTypeEnum::STRING(),
            ],
            [
                'header' => 'request.amount',
                'field' => 'requestElementsAccountedSumAmount',
                'sortField' => 'request_element_accounted_sum_amount',
                'fieldType' => FieldTypeEnum::CURRENCY(),
            ],
            [
                'header' => 'request.status',
                'field' => 'status_translated',
                'sortField' => 'status_translated',
                'fieldType' => FieldTypeEnum::STRING(),
            ]
        ];
    }
}
