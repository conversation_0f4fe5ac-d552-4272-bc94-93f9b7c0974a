<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Pub\Modules\Provider\Enums\SourceEnum;

class AddSourceToProviders extends Migration
{
    public function up(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->enum('source', SourceEnum::toArray())->default(SourceEnum::UNKNOWN())->after('erp_id');
            $table->string('city')->after('address')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->dropColumn('source');
            $table->dropColumn('city');
        });
    }
}
