<?php

declare(strict_types=1);

use App\Group;
use App\Permission;
use Illuminate\Database\Migrations\Migration;

class AddMycardAccountStatementsPermission extends Migration
{
    private const GROUPS = [
        \App\User::GROUP_MYCARD_ACCOUNTANT,
        \App\User::GROUP_MYCARD_MANAGER,
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        return; // disabled

        $groups = Group::query()->whereIn('name', self::GROUPS)->get();

        foreach ($groups as $group) {
            Permission::firstOrCreate([
                'instance_id' => $group->instance_id,
                'group_id' => $group->id,
                'ability' => 'mycardAccountStatements',
            ], [
                'name' => 'mycardAccountStatements',
                'scope' => null,
                'can' => true,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        return; // disabled

        $groupIds = Group::query()->whereIn('name', self::GROUPS)->select('id')->toBase()->get()->pluck('id');

        Permission::query()
            ->whereIn('group_id', $groupIds)
            ->where('ability', 'mycardAccountStatements')
            ->delete();
    }
}
