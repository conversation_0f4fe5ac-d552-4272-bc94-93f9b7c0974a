<?php

declare(strict_types=1);

use App\Group;
use App\Permission;
use Illuminate\Database\Migrations\Migration;

class DocumentAbility extends Migration
{
    private const GROUPS = [
        \App\User::GROUP_NAME_REGULAR => true,
        \App\User::GROUP_B2B => false,
    ];

    private const PERMISSIONS = [
        Permission::DOCUMENT_UPLOAD_AND_DELETE,
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Instance::all()
            ->each(function (\App\Instance $instance) {
                foreach (self::GROUPS as $groupName => $value) {
                    $group = $instance->groups()->firstOrCreate([
                        'name' => $groupName,
                    ],[
                        'instance_id' => $instance->id,
                        'slug' => \Illuminate\Support\Str::lower($groupName),
                        'visible' => false,
                        'type' => 'manual',
                        'created_at' => \Carbon\Carbon::now(),
                        'updated_at' => \Carbon\Carbon::now(),
                    ]);

                    $this->createPermission($group, $value);
                }
            });
    }

    private function createPermission(Group $group, bool $can): void
    {
        foreach (self::PERMISSIONS as $permission) {
            Permission::forceCreate([
                'instance_id' => $group->instance_id,
                'group_id' => $group->id,
                'ability' => $permission,
                'name' => $permission,
                'scope' => null,
                'can' => $can,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $groupIds = Group::query()->whereIn('name', self::GROUPS)->select('id')->toBase()->get()->pluck('id');

        Group::query()->whereIn('name', self::GROUPS)->delete();
        Permission::query()->whereIn('group_id', $groupIds)->delete();
    }
}
