<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOwnerColumnToCardsTable extends Migration
{
    public function up()
    {
        $this->getSchema()->table('cards', function (Blueprint $table) {

            $driver = Schema::connection($this->getConnection())->getConnection()->getDriverName();

            if ('sqlite' === $driver) {
                $table->integer('owner_id')->nullable()->unsigned()->index();

            } else {
                $table->integer('owner_id')->unsigned()->index()->nullable(false);
            }

            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');
        });

        $this->getSchema()->table('cards', function (Blueprint $table) {

            $driver = Schema::connection($this->getConnection())->getConnection()->getDriverName();

            if ('sqlite' === $driver) {
                $this->getSchema()->table('cards', function (Blueprint $table) {
                    $table->integer('owner_id')->nullable(false)->default(null)->change();
                });
            }
        });
    }

    public function down()
    {
        $this->getSchema()->table('cards', function (Blueprint $table) {
            $table->dropForeign('cards_owner_id_foreign');
            $table->dropColumn('owner_id');
        });
    }
}
