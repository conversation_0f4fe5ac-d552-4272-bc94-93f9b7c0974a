<?php

use App\Vendors\Blueprint;
use App\Vendors\Migration;

class CreateMycardCardholdersTable extends Migration
{
    public function up(): void
    {
        $this->getSchema()->create('mycard_cardholders', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->instance();
            $table->user();
            $table->string('cardholder_id');
            $table->string('status')->nullable(true);
            $table->unique(['user_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mycard_cardholders');
    }
}
