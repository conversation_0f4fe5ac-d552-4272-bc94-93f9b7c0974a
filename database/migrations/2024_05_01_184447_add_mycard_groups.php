<?php

declare(strict_types=1);

use App\Group;
use App\Permission;
use Illuminate\Database\Migrations\Migration;

class AddMycardGroups extends Migration
{
    private const GROUPS = [
        \App\User::GROUP_MYCARD_ACCOUNTANT,
        \App\User::GROUP_MYCARD_MANAGER,
        \App\User::GROUP_MYCARD_EMPLOYEE,
    ];

    private const PERMISSIONS = [
        Permission::MYCARD_STATEMENTS,
        Permission::MYCARD_PAYMENTS,
        Permission::MYCARD_CARD_ISSUING,
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Instance::all()
            ->each(function (\App\Instance $instance) {
                foreach (self::GROUPS as $groupName) {
                    $group = Group::forceCreate([
                        'instance_id' => $instance->id,
                        'slug' => \Illuminate\Support\Str::lower($groupName),
                        'name' => $groupName,
                        'visible' => false,
                        'type' => 'manual',
                        'created_at' => \Carbon\Carbon::now(),
                        'updated_at' => \Carbon\Carbon::now(),
                    ]);

                    $this->createPermission($group);
                }
            });
    }

    private function createPermission(Group $group)
    {
        foreach (self::PERMISSIONS as $permission) {
            Permission::forceCreate([
                'instance_id' => $group->instance_id,
                'group_id' => $group->id,
                'ability' => $permission,
                'name' => $permission,
                'scope' => null,
                'can' => false,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $groupIds = Group::query()->whereIn('name', self::GROUPS)->select('id')->toBase()->get()->pluck('id');

        Group::query()->whereIn('name', self::GROUPS)->delete();
        Permission::query()->whereIn('group_id', $groupIds)->delete();
    }
}
