<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RenameMileageAllowanceAccountingAccountIdColumnInRequestsTable extends Migration
{
    public function up(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            $table
                ->integer('mileage_allowance_debit_account_id')
                ->unsigned()
                ->nullable()
                ->after('settled_amount_before_accounting');

            $table
                ->foreign('mileage_allowance_debit_account_id')
                ->references('id')
                ->on('accounting_accounts');
        });

        \Illuminate\Support\Facades\DB::statement(
            "UPDATE requests SET mileage_allowance_debit_account_id=mileage_allowance_accounting_account_id"
        );

        Schema::table('requests', function (Blueprint $table) {
            $table->dropForeign(['mileage_allowance_accounting_account_id']);
            $table->dropColumn('mileage_allowance_accounting_account_id');
        });
    }

    public function down(): void
    {
        Schema::table('requests', function (Blueprint $table) {
            $table
                ->integer('mileage_allowance_accounting_account_id')
                ->unsigned()
                ->nullable()
                ->after('settled_amount_before_accounting');

            $table
                ->foreign('mileage_allowance_accounting_account_id')
                ->references('id')
                ->on('accounting_accounts');
        });

        \Illuminate\Support\Facades\DB::statement(
            "UPDATE requests SET mileage_allowance_accounting_account_id=mileage_allowance_debit_account_id"
        );

        Schema::table('requests', function (Blueprint $table) {
            $table->dropForeign(['mileage_allowance_debit_account_id']);
            $table->dropColumn('mileage_allowance_debit_account_id');
        });
    }
}
