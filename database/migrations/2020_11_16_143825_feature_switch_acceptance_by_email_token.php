<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class FeatureSwitchAcceptanceByEmailToken extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $feature = new \Modules\FeatureSwitcher\Priv\Entities\Feature();
        $feature->type = \Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum::SWITCH();
        $feature->slug = \Modules\FeatureSwitcher\Pub\Enums\FeatureEnum::FEATURE_ACCEPTANCE_BY_EMAIL_TOKEN_ENABLED();
        $feature->default = '1';
        $feature->save();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \Modules\FeatureSwitcher\Priv\Entities\Feature::where('slug', \Modules\FeatureSwitcher\Pub\Enums\FeatureEnum::FEATURE_ACCEPTANCE_BY_EMAIL_TOKEN_ENABLED())->delete();
    }
}
