<?php

use App\Instance;
use Illuminate\Database\Migrations\Migration;
use Modules\IntegrationFILE\Priv\Config\InstanceSettings;
use Modules\IntegrationFILE\Priv\Factories\SourceAbstractFactory;
use Modules\IntegrationFILE\Priv\Factories\TransformerFactory;
use Modules\IntegrationFILE\Pub\Enums\ImportTypeEnum;

class EnabledProvidersImportTransformerAlten extends Migration
{
    private const ALT_INSTANCE_ID = 78;

    public function up(): void
    {
        $instance = Instance::query()->where('id', self::ALT_INSTANCE_ID)->first();

        if (!$instance) {
            return;
        }

        $modules = $instance->modules;
        $instanceSettings = [
            InstanceSettings::IMPORT_ITERATOR => [
                ImportTypeEnum::USERS()->getValue() => SourceAbstractFactory::CSV_ITERATOR,
                ImportTypeEnum::PROJECTS()->getValue() => SourceAbstractFactory::CSV_ITERATOR,
            ],
            InstanceSettings::CSV_DELIMITER => ',',
            InstanceSettings::FIRST_ROW_AS_HEADER => false,
            InstanceSettings::TRANSFORMERS => [
                ImportTypeEnum::USERS()->getValue() => TransformerFactory::SAP_USER_TRANSFORMER,
                ImportTypeEnum::PROJECTS()->getValue() => TransformerFactory::SAP_PROJECT_TRANSFORMER,
                ImportTypeEnum::PROVIDERS()->getValue() => TransformerFactory::ALTEN_PROVIDER_TRANSFORMER,
            ],
        ];
        $modules[InstanceSettings::INSTANCE_SETTING] = $instanceSettings;

        $instance->modules = $modules;
        $instance->save();
    }

    public function down(): void
    {
    }
}
