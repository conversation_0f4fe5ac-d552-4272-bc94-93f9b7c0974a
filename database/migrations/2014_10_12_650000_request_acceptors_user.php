<?php

class RequestAcceptorsUser extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('request_acceptors_user', function(\App\Vendors\Blueprint $table) {
            $table->request();
            $table->user();
            $table->string('accepted')->default('pending');
            $table->boolean('default')->default(false);
            $table->integer('added_by')->unsigned()->index();
            $table->timestamp('acceptance_date')->nullable();
            $table->string('token')->nullable();
            $table->comment(true);

            $table->foreign('added_by')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('request_acceptors_user');
    }
}
