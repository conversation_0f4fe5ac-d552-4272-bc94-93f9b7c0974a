<?php

use App\Instance;
use App\Rule;
use App\Services\RulesService\Rules\RequestTargetPointsAreBeforeTransportationElementsChronologyRule;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRequestTargetPointsAreBeforeTransportationElementsRule extends Migration
{
    public function up(): void
    {
        $rules = collect([
            [
                'level' => Rule::LEVEL_ERROR,
                'name' => RequestTargetPointsAreBeforeTransportationElementsChronologyRule::NAME,
                'parameters' => []
            ]
        ]);

        Instance::withTrashed()->get()->each(
            function (Instance $instance) use (&$rules) {
                $rules->each(
                    function ($rule) use ($instance) {
                        $instanceRule = Rule::where('instance_id', $instance->id)
                            ->where('name', $rule['name'])
                            ->first();

                        if (!$instanceRule) {
                            $rule['instance_id'] = $instance;
                            factory(App\Rule::class)->create($rule);
                        }
                    }
                );
            }
        );
    }

    public function down(): void
    {
        Rule::where('name', RequestTargetPointsAreBeforeTransportationElementsChronologyRule::NAME)->delete();
    }
}
