<?php

class Mpks extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('mpks', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->instance();

            $table->string('code');
            $table->string('name');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('mpks');
    }
}
