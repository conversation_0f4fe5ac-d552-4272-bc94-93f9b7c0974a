<?php

use Illuminate\Database\Migrations\Migration;

class AddResponseStaticCacheFeatureSwitch extends Migration
{
    public function up()
    {
        return;

        $feature = new \Modules\FeatureSwitcher\Priv\Entities\Feature();
        $feature->type = \Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum::SWITCH();
        $feature->slug = \Modules\FeatureSwitcher\Pub\Enums\FeatureEnum::FEATURE_RESPONSE_STATIC_CACHE();
        $feature->default = '0';
        $feature->save();
    }

    public function down()
    {
        \Modules\FeatureSwitcher\Priv\Entities\Feature::where('slug', \Modules\FeatureSwitcher\Pub\Enums\FeatureEnum::FEATURE_RESPONSE_STATIC_CACHE())->delete();
    }
}
