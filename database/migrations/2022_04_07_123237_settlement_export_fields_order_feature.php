<?php

use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use App\Services\SettlementExport\Enums\AccountingColumnsOrderStrategyEnum;

class SettlementExportFieldsOrderFeature extends Migration
{
    public function up(): void
    {
        factory(Feature::class)->create([
            'slug' => FeatureEnum::FEATURE_SETTLEMENT_EXPORT_ACCOUNTING_COLUMNS_ORDER(),
            'type' => FeatureTypeEnum::ENUM(),
            'default' => AccountingColumnsOrderStrategyEnum::DEFAULT(),
            'class' => AccountingColumnsOrderStrategyEnum::class,
        ]);
    }

    public function down(): void
    {
        Feature::where('slug', FeatureEnum::FEATURE_SETTLEMENT_EXPORT_ACCOUNTING_COLUMNS_ORDER())
            ->each(function (Feature $feature) {
                $feature->delete();
            });
    }
}
