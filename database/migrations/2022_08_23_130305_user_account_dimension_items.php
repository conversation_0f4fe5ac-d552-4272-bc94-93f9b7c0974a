<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class UserAccountDimensionItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_account_dimension_items', function (Blueprint $table) {
            $table->integer('id', true, true);
            $table->uuid('slug')->unique();
            $table->integer('user_id', false, true);
            $table->integer('account_dimension_id', false, true);
            $table->integer('account_dimension_item_id', false, true);
            $table->timestamps();

            $table->foreign('user_id', 'uadi_user_id_fkey')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('account_dimension_id', 'uadi_account_dimension_id_fkey')->references('id')->on('account_dimensions')->onDelete('cascade');
            $table->foreign('account_dimension_item_id', 'uadi_account_dimension_item_id_fkey')->references('id')->on('account_dimension_items')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_account_dimension_items');
    }
}
