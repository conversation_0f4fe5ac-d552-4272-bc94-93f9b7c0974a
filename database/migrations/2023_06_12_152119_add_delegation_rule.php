<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;

class AddDelegationRule extends Migration
{
    public function up()
    {
        $instances = \App\Instance::all();
        foreach ($instances as $instance) {
            \App\Rule::updateOrInsert(
                [
                    'instance_id' => $instance->id,
                    'name' => \App\Compliance\Instances\Amrest\Rules\DelegationTripRule::NAME,
                ],
                [
                    'instance_id' => $instance->id,
                    'name' => \App\Compliance\Instances\Amrest\Rules\DelegationTripRule::NAME,
                    'level' => 'warning',
                    'parameters' => '[]',
                    'created_at' => '2022-12-08 09:00:00',
                    'updated_at' => '2022-12-08 09:00:00',
                ]
            );
        }
    }

    public function down()
    {
        $instances = \App\Instance::all();
        foreach ($instances as $instance) {
            \App\Rule::where('instance_id', $instance->id)->where('name', 'delegation_trip_rule')->delete();
        }
    }
}
