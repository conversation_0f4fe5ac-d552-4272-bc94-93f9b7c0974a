<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStaticCurrenciesMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $staticCurrencies = config('exchangerate.static-currencies');

        /**
         * execute only if database was seeded before (first time seed is handled with build-local script)
         */
        if (\App\Currency::count() > 0) {
            $maxOrder = \App\Currency::query()->orderBy('order', 'desc')->limit(1)->first()->order;
            foreach ($staticCurrencies as $code => $staticCurrency) {
                if (\App\Currency::where('code', '=', $code)->exists() === false) {
                    factory(\App\Currency::class)->create([
                        'code' => $code,
                        'name' => $staticCurrency['name'],
                        'order' => ++$maxOrder
                    ]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $codes = array_keys(config('exchangerate.static-currencies'));

        \App\Currency::query()->whereIn('code', $codes)->delete();
    }
}
