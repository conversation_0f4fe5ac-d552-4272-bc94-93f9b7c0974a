<?php

use App\HasManyMpkCollection;
use App\User;
use Illuminate\Database\Migrations\Migration;

class SetupUsersHasMpkTable extends Migration
{
    public function up(): void
    {
        $results = User::query()
            ->select(['users.id as id', 'users.mpk_id'])
            ->leftJoin('user_has_mpk as m', 'm.user_id', '=', 'users.id')
            ->whereNull('m.id')
            ->get();

        $results->each(function ($user) {
            $user->syncMpks(HasManyMpkCollection::createFromRequestData([[
                'id' => $user->mpk_id,
                'percentage' => 100,
                'main' => true,
            ]]));
        });
    }

    public function down(): void
    {
    }
}
