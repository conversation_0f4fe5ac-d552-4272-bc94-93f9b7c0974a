<?php

class RequestAccomodations extends \App\Vendors\Migration
{
    public function up()
    {
        $this->getSchema()->create('request_accomodations', function (\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();
            $table->dateTime('arrival_at');
            $table->dateTime('departure_at');
            $table->boolean('wifi')->default(false);
            $table->boolean('breakfast')->default(false);
            $table->unsignedTinyInteger('standard')->default(0);
            $table->unsignedSmallInteger('search_radius')->default(0);
            $table->searcherDisabled();
            $table->documentElementType();

            //requested cost
            $table->amount(true);
            $table->amountCurrency(true);

            $table->weight();

        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_accomodations');
    }
}
