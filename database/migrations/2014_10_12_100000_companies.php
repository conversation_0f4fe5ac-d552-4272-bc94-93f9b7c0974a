<?php

class Companies extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('companies', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->instance();

            $table->text('trip_agent')->nullable();
            $table->string('name');
            $table->string('nip');
            $table->string('code');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('companies');
    }
}
