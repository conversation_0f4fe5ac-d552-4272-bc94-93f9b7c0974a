<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddColumnSiblingIdTableDocumentElements extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_elements', function (Blueprint $table) {
            $table->unsignedBigInteger('sibling_id')
                ->nullable()
                ->references('id')
                ->on('document_elements')
                ->onDelete('SET NULL');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_elements', function (Blueprint $table) {
            $table->dropForeign(['sibling_id']);
            $table->dropColumn('sibling_id');
        });
    }
}
