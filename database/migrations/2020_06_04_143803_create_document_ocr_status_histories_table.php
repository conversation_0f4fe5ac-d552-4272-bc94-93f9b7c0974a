<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDocumentOcrStatusHistoriesTable extends Migration
{
    public function up(): void
    {
        Schema::create('document_ocr_status_histories', function (Blueprint $table) {
            $table->increments('id');
            $table->bigInteger('document_id')->index();
            $table->string('type')->nullable();
            $table->string('accounting_type')->nullable();
            $table->string('status')->index();
            $table->string('ocr_data')->nullable();
            $table->string('ocr_file_md5')->nullable();
            $table->string('ocr_provider_id')->nullable();
            $table->string('ocr_error')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('document_ocr_status_histories');
    }
}
