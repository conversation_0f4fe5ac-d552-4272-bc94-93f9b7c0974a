<?php

use App\Vendors\Blueprint;
use App\Vendors\Migration;

class CreateRequestUnrequestedElementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('request_unrequested_elements', function (Blueprint $table) {
            $table->increments('id');
            $table->instance();
            $table->request();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('request_unrequested_elements');
    }
}
