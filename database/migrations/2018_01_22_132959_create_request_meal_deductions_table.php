<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRequestMealDeductionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('request_meal_deductions', function (Blueprint $table) {
            $table->increments('id');
            $table->instance();
            $table->request();
            $table->timestamp('from')->nullable();
            $table->timestamp('to')->nullable();
            $table->boolean('breakfast')->default(false);
            $table->boolean('lunch')->default(false);
            $table->boolean('dinner')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('request_meal_deductions');
    }
}
