<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class HashSecretsMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $clients = \App\OAuthClient::query()->where('secret', 'not like' , '$2%')->get();

        /** @var \App\OAuthClient $client */
        foreach($clients as $client) {
            $client->secret = $client->secret;
            $client->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // one way migration
    }
}
