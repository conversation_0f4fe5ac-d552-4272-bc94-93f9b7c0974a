<?php

class RequestPrivateAccomodations extends \App\Vendors\Migration
{
    public function up()
    {
        $this->getSchema()->create('request_private_accomodations', function (\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();
            $table->dateTime('arrival_at');
            $table->dateTime('departure_at');

            //requested cost
            $table->amount(true);
            $table->amountCurrency(true);

            $table->weight();
        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_private_accomodations');
    }
}
