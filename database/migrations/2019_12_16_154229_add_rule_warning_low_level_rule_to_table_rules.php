<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddRuleWarningLowLevelRuleToTableRules extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $rules = collect([
            [
                'level' => App\Rule::LEVEL_AUTOMATION,
                'name' => 'warning_low_level_for_automatically_acceptation_plane_trip_rule',
                'parameters' => [
                    'enabled' => false,
                    'grades' => [0, 1]
                ]
            ]
        ]);

        \App\Instance::all()->each(function(\App\Instance $instance) use (&$rules) {

            $rules->each(function ($rule) use($instance) {
                $instanceRule = \App\Rule::where('instance_id', $instance->id)
                    ->where('name', $rule['name'])
                    ->first();

                if ( ! $instanceRule) {
                    $rule['instance_id'] = $instance;
                    factory(App\Rule::class)->create($rule);
                }
            });


        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('table_rules', function (Blueprint $table) {
            //
        });
    }
}
