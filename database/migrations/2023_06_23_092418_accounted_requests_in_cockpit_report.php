<?php

use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;

class AccountedRequestsInCockpitReport extends Migration
{
    /**
     * Registers FEATURE_ACCOUNTED_REQUESTS_IN_COCKPIT_REPORT feature in the features table
     */
    public function up(): void
    {
        factory(Feature::class)->create([
            'slug' => FeatureEnum::FEATURE_ACCOUNTED_REQUESTS_IN_COCKPIT_REPORT(),
            'type' => FeatureTypeEnum::SWITCH(),
            'default' => 0,
        ]);
    }

    /**
     * Removes FEATURE_ACCOUNTED_REQUESTS_IN_COCKPIT_REPORT from features table
     */
    public function down(): void
    {
        Feature::query()
            ->where('slug', '=', FeatureEnum::FEATURE_ACCOUNTED_REQUESTS_IN_COCKPIT_REPORT())
            ->delete();
    }
}
