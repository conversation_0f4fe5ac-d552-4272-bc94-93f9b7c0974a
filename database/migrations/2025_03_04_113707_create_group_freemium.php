<?php

use App\Group;
use App\Instance;
use Illuminate\Database\Migrations\Migration;

class CreateGroupFreemium extends Migration
{
    public function up(): void
    {
        Instance::all()
            ->each(function (Instance $instance) {
                $groupName = 'Freemium';

                $exists = Group::where('name', 'Freemium')->where('instance_id', $instance->id)->exists();

                if ($exists) {
                    return;
                }

                Group::forceCreate([
                    'instance_id' => $instance->id,
                    'slug' => \Illuminate\Support\Str::lower($groupName),
                    'name' => $groupName,
                    'visible' => true,
                    'type' => 'manual',
                    'created_at' => \Carbon\Carbon::now(),
                    'updated_at' => \Carbon\Carbon::now(),
                ]);
            });
    }

    public function down(): void
    {
    }
}
