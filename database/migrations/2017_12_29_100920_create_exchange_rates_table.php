<?php

use \App\Vendors\Blueprint;
use \App\Vendors\Migration;

class CreateExchangeRatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('exchange_rates', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamp('effective_date');
            $table->currency();
            $table->decimal('rate', 10, 4);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('exchange_rates');
    }
}
