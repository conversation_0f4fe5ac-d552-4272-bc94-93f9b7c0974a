<?php

class Users extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('users', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->timestamp('blocked_at')->nullable();
            $table->rememberToken();
            $table->softDeletes();

            $table->instance();
            $table->company();
            $table->mpk();
            $table->user(true);

            $table->boolean('is_admin')->default(false);

            $table->locale();
            $table->string('slug', 16);
            $table->string('email')->unique();
            $table->string('password');
            $table->string('grade');
            $table->string('level');
            $table->string('policy');
            $table->string('first_name');
            $table->string('last_name');
            $table->string('sex', 1)->default(\App\User::USER_SEX_MALE);
            $table->dateTime('birth_date')->nullable();
            $table->string('passport_number')->nullable();
            $table->dateTime('passport_valid_date')->nullable();
            $table->dateTime('passport_issue_date')->nullable();
            $table->string('phone')->nullable();
            $table->string('phone_ice')->nullable();
            $table->string('avatar')->nullable();
            $table->string('identity_card_number')->nullable();
            $table->string('erp_id')->default('0');
            $table->integer('nationality_id')->unsigned()->index();
            $table->integer('citizenship_id')->unsigned()->index();

            $table->foreign('nationality_id')->references('id')->on('countries')->onDelete('cascade');
            $table->foreign('citizenship_id')->references('id')->on('countries')->onDelete('cascade');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('users');
    }
}
