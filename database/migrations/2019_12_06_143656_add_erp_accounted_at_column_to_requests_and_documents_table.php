<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddErpAccountedAtColumnToRequestsAndDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('requests', function (Blueprint $table) {
            $table->dateTime('erp_accounted_at')->nullable()->after('accounted_at');
        });

        Schema::table('documents', function (Blueprint $table) {
            $table->dateTime('erp_accounted_at')->nullable()->after('accounted_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
