<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;

class CreateOcrHintsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('ocr_hints', function (Blueprint $table) {
            $table->increments('id');
            $table->instance();
            $table->document();
            $table->string('column');
            $table->string('value');
            $table->string('label');
            $table->boolean('accepted')->nullable();
            $table->timestamps();


//            $table->unique(array('column', 'document_id'));

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ocr_hints');
    }
}
