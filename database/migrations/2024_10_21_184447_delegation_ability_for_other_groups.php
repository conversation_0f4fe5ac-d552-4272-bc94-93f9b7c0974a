<?php

declare(strict_types=1);

use App\Group;
use App\Permission;
use Illuminate\Database\Migrations\Migration;

class DelegationAbilityForOtherGroups extends Migration
{
    private const GROUPS = [
        \App\User::GROUP_NAME_FINANCE,
        \App\User::GROUP_NAME_TAXI,
        \App\User::GROUP_NAME_AGENT,
        \App\User::GROUP_NAME_ADMINISTRATOR,
        \App\User::GROUP_NAME_REGIONAL_POSITION,
    ];

    private const PERMISSIONS = [
        Permission::ACCOUNT_DELEGATION,
        Permission::DOCUMENT_UPLOAD_AND_DELETE,
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \App\Instance::all()
            ->each(function (\App\Instance $instance) {
                foreach (self::GROUPS as $groupName) {
                    /** @var Group $group */
                    $group = $instance->groups()->where([
                        'name' => $groupName,
                    ])->first();

                    if ($group) {
                        $this->createPermission($group);
                    }
                }
            });
    }

    private function createPermission(Group $group): void
    {
        foreach (self::PERMISSIONS as $permission) {
            Permission::forceCreate([
                'instance_id' => $group->instance_id,
                'group_id' => $group->id,
                'ability' => $permission,
                'name' => $permission,
                'scope' => null,
                'can' => true,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);
        }
    }

    public function down()
    {
    }
}
