<?php

class RequestPrivateCarTrips extends \App\Vendors\Migration
{
    public function up()
    {
        $this->getSchema()->create('request_private_car_trips', function (\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();

            $table->dateTime('departure_at');
            $table->dateTime('return_at')->nullable();
            $table->boolean('round_trip');
            $table->decimal('distance', 12, 3)->nullable();
            $table->string('vehicle_type');

            $table->amount(true, 'other_costs_amount');
            $table->amountCurrency(true, 'other_costs_currency_id');
            $table->amount(true);
            $table->amountCurrency(true);

	        $table->string('license_plate');

            $table->weight();
        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_private_car_trips');
    }
}
