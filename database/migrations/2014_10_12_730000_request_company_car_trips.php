<?php

class RequestCompanyCarTrips extends \App\Vendors\Migration
{
    public function up()
    {
        $this->getSchema()->create('request_company_car_trips', function (\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();
            $table->documentElementType();

            $table->dateTime('departure_at');
	        $table->dateTime('return_at')->nullable();
            $table->boolean('round_trip');

            //fuel cost
            $table->amount(true, 'fuel_cost');
            $table->amountCurrency(true, 'fuel_cost_currency_id');

            //other costs
            $table->amount(true, 'other_costs_amount');
            $table->amountCurrency(true, 'other_costs_currency_id');

            //requested cost
            $table->amount(true);
            $table->amountCurrency(true);

	        $table->string('license_plate');

            $table->weight();
        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_company_car_trips');
    }
}
