<?php

use App\Vendors\Blueprint;
use App\Vendors\Migration;

class AddSearcherToRequestPlaneTripsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->table('request_plane_trips', function (Blueprint $table) {
            $table->searcherDisabled();
            $table->boolean('direct_only')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        $this->getSchema()->table('request_plane_trips', function (Blueprint $table) {
            $table->dropColumn('searcher_disabled');
            $table->dropColumn('direct_only');
        });
    }
}
