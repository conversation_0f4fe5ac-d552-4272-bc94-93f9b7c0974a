<?php

declare(strict_types=1);

use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Database\Migrations\Migration;

class AddRequestIdSlagToNotificationMoved extends Migration
{
    public function up()
    {
        $this->hydrateRequestIdSlugIfPossible();
    }


    public function down()
    {
        //
    }

    private function hydrateRequestIdSlugIfPossible(): void
    {
        DB::beginTransaction();

        try {
            $this->setRequestSlugIds();

            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();

            throw $exception;
        }
    }

    private function setRequestSlugIds(): void
    {
        /** @var \Illuminate\Support\Collection|null $notificationModels */
        $notificationModels = DatabaseNotification::where([
            ['request_id_slug', '=', null],
            ['data', 'like', '%request_id%']
        ])
            ->limit(100)
            ->get();

        if ($notificationModels->count() > 0) {
            $notificationModels->each(function ($notificationModel) {
                if (isset($notificationModel->data) === true
                    && isset($notificationModel->data['data']) === true
                    && isset($notificationModel->data['data']['request_id']) === true
                ) {
                    $notificationModel->request_id_slug = $notificationModel->data['data']['request_id'];
                    $notificationModel->save();
                }
            });

            $this->setRequestSlugIds();
        }
    }
}
