<?php

class RequestTrainTrips extends \App\Vendors\Migration
{
    public function up()
    {
        $this->getSchema()->create('request_train_trips', function (\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->softDeletes();

            $table->instance();
            $table->request();
            $table->documentElementType();

            $table->dateTime('departure_at');
            $table->dateTime('arrival_at')->nullable();
            $table->dateTime('return_at')->nullable();
            $table->boolean('round_trip');
            $table->searcherDisabled();
            $table->tinyInteger('service_class')->unsigned()->nullable();
            //requested cost
            $table->amount(true);
            $table->amountCurrency(true);

            $table->weight();
        });
    }

    public function down()
    {
        $this->getSchema()->dropIfExists('request_train_trips');
    }
}
