<?php

declare(strict_types=1);

use App\Group;
use App\Instance;
use App\Permission;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Str;

class OtherCostsAbility extends Migration
{
    private const GROUPS = [
        User::GROUP_NAME_REGULAR => true,
        User::GROUP_NAME_ADMINISTRATOR => true,
        User::GROUP_B2B => false,
    ];

    private const PERMISSIONS = [
        Permission::SHOW_OTHER_COSTS,
    ];

    public function up(): void
    {
        Instance::all()
            ->each(function (Instance $instance) {
                foreach (self::GROUPS as $groupName => $value) {
                    /** @var Group $group */
                    $group = $instance->groups()->firstOrCreate([
                        'name' => $groupName,
                    ], [
                        'instance_id' => $instance->id,
                        'slug' => Str::lower($groupName),
                        'visible' => false,
                        'type' => 'manual',
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);

                    $this->createPermission($group, $value);
                }
            });
    }

    public function down(): void
    {
        $groupIds = Group::query()->whereIn('name', self::GROUPS)->select('id')->toBase()->get()->pluck('id');

        Group::query()->whereIn('name', self::GROUPS)->delete();
        Permission::query()->whereIn('group_id', $groupIds)->delete();
    }

    private function createPermission(Group $group, bool $can): void
    {
        foreach (self::PERMISSIONS as $permission) {
            Permission::forceCreate([
                'instance_id' => $group->instance_id,
                'group_id' => $group->id,
                'ability' => $permission,
                'name' => $permission,
                'scope' => null,
                'can' => $can,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}
