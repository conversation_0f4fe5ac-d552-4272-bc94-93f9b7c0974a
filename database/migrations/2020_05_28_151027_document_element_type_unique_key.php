<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class DocumentElementTypeUniqueKey extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_element_types', function (Blueprint $table) {
            $table->unique(['slug', 'instance_id'], 'document_element_types_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_element_types', function (Blueprint $table) {
            $table->dropUnique('document_element_types_unique');
        });
    }
}
