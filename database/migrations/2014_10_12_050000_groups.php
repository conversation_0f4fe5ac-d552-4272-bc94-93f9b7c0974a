<?php

class Groups extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('groups', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->instance();

            $table->string('name');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('groups');
    }
}
