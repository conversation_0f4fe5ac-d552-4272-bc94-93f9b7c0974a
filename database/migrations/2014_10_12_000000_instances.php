<?php

class Instances extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('instances', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->timestamp('blocked_at')->nullable();
            $table->softDeletes();

            $table->string('domain')->unique();
            $table->locale();
            $table->text('trip_agent');
            $table->currency();
            $table->country();
            $table->text('accounting_reminder_settings');
            $table->string('name');
            $table->string('address');
            $table->string('city');
            $table->string('post_code');
            $table->string('nip');
            $table->string('ios_version')->nullable();
            $table->boolean('ios_force_update')->default(false);
            $table->string('android_version')->nullable();
            $table->boolean('android_force_update')->default(false);
            $table->text('modules');

            $table->integer('providers_account_id')->unsigned()->index()->nullable(true);
            $table->integer('technical_account_id')->unsigned()->index()->nullable(true);
            $table->integer('foreign_travel_expenses_debit_account_id')->unsigned()->index()->nullable(true);
            $table->integer('national_travel_expenses_debit_account_id')->unsigned()->index()->nullable(true);
            $table->integer('foreign_travel_expenses_credit_account_id')->unsigned()->index()->nullable(true);
            $table->integer('national_travel_expenses_credit_account_id')->unsigned()->index()->nullable(true);
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('instances');
    }
}
