<?php
class RequestChanges extends \App\Vendors\Migration {
    public function up() {
        $this->getSchema()->create('request_changes', function(\App\Vendors\Blueprint $table) {
            $table->increments('id');
            $table->timestamps();

            $table->instance();
            $table->request();
            $table->user(true);

            $table->longText('changes');
        });
    }

    public function down() {
        $this->getSchema()->dropIfExists('request_changes');
    }
}
