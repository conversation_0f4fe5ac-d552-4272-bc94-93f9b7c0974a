<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Common\Enums\SupportedCurrencyCodesEnum;
use Modules\ExchangeRate\Priv\Models\ExchangeRateProvider;

class AddRoFxExchange extends Migration
{
    private const SLUG = 'bnr';
    private const TABLE_NAME = 'exchange_rates_' . self::SLUG;

    public function up(): void
    {
        Schema::create(self::TABLE_NAME, function (Blueprint $table) {
            $table->increments('id');
            $table->timestamp('effective_date')->nullable(true);
            $table->integer('currency_id')->unsigned()->index()->nullable(false);
            $table->foreign('currency_id')->references('id')->on('currencies')->onDelete('restrict');

            $table->decimal('rate', 14, 8);
            $table->integer('divider')->default(1);
            $table->timestamps();
        });

        $rok = \App\Currency::where('code', '=', SupportedCurrencyCodesEnum::RON())->first();
        if ($rok !== null) {
            /** @var ExchangeRateProvider $exchangeRateProvider */
            factory(ExchangeRateProvider::class)->create([
                'slug' => self::SLUG,
                'name' => 'Banca Nationala a Romaniei',
                'currency_id' => $rok->id,
                'created_at' => \Carbon\Carbon::now(),
                'updated_at' => \Carbon\Carbon::now(),
            ]);
        }
    }

    public function down(): void
    {
        ExchangeRateProvider::query()->where('slug', self::SLUG)->delete();
        Schema::dropIfExists(self::TABLE_NAME);
    }
}
