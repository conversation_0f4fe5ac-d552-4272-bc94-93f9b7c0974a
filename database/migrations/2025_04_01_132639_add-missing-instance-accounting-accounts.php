<?php

use App\Enum\ActiveEnum;
use App\Instance;
use App\InstanceAccountingAccounts;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Enum\AccountingAccountKindEnum;
use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;
use Modules\Accounting\Priv\Exceptions\AccountingAccountNotFound;

class AddMissingInstanceAccountingAccounts extends Migration
{

    public function up(): void
    {
        foreach (Instance::withoutTrashed()->get() as $instance) {
            $instanceAccountingAccountsToSave = [];

            foreach (AccountingAccountKindEnum::values() as $accountingAccountKind) {
                foreach (AccountingAccountTypeEnum::values() as $accountingAccountType) {
                    foreach ([true, false] as $isForeign) {
                        $accountingAccount = $instance->instanceAccountingAccount(
                            $accountingAccountKind,
                            $accountingAccountType,
                            $isForeign
                        );
                        if ($accountingAccount instanceof AccountingAccount === false) {
                            try {
                                $instanceAccountingAccountsToSave[] = $this->createMissingAccountingAccount(
                                    $instance,
                                    $accountingAccountKind,
                                    $accountingAccountType,
                                    $isForeign
                                );
                            } catch (AccountingAccountNotFound $e) {
                                continue;
                            }
                        }
                    }
                }
            }

            foreach ($instanceAccountingAccountsToSave as $instanceAccountingAccount) {
                $instanceAccountingAccount->save();
            }
        }
    }

    public function down(): void
    {
    }

    private function createMissingAccountingAccount(
        Instance $instance,
        AccountingAccountKindEnum $accountingAccountKind,
        AccountingAccountTypeEnum $accountingAccountType,
        bool $isForeign
    ): InstanceAccountingAccounts {
        $accountingAccount = new InstanceAccountingAccounts();
        $accountingAccount->instance_id = $instance->id;
        $accountingAccount->accounting_account_id = $this->matchAccountingAccount(
            $instance,
            $accountingAccountKind,
            $accountingAccountType,
            $isForeign
        )->id;
        $accountingAccount->kind = $accountingAccountKind;
        $accountingAccount->type = $accountingAccountType;
        $accountingAccount->foreign = $isForeign;

        return $accountingAccount;
    }

    private function matchAccountingAccount(
        Instance $instance,
        AccountingAccountKindEnum $accountingAccountKind,
        AccountingAccountTypeEnum $accountingAccountType,
        bool $isForeign
    ): AccountingAccount {
        $inverseType = AccountingAccountTypeEnum::DEBIT()->equals($accountingAccountType) ?
            AccountingAccountTypeEnum::CREDIT() :
            AccountingAccountTypeEnum::DEBIT();

        $accountingAccount = $instance->instanceAccountingAccount(
            $accountingAccountKind,
            $accountingAccountType,
            !$isForeign
        );

        if ($accountingAccount instanceof AccountingAccount) {
            return $accountingAccount;
        }

        $accountingAccount = $instance->instanceAccountingAccount(
            $accountingAccountKind,
            $inverseType,
            $isForeign
        );

        if ($accountingAccount instanceof AccountingAccount) {
            return $accountingAccount;
        }

        $accountingAccount = $instance->instanceAccountingAccount(
            $accountingAccountKind,
            $inverseType,
            !$isForeign
        );

        if ($accountingAccount instanceof AccountingAccount) {
            return $accountingAccount;
        }

        $accountingAccount = AccountingAccount::where([
            'instance_id' => $instance->id,
            'is_active' => ActiveEnum::ACTIVE(),
            'type' => $accountingAccountType->getValue()
        ])->first();

        if ($accountingAccount instanceof AccountingAccount) {
            return $accountingAccount;
        }

        $accountingAccount = AccountingAccount::where([
            'instance_id' => $instance->id,
            'is_active' => ActiveEnum::ACTIVE(),
            'type' => $inverseType,
        ])->first();

        if ($accountingAccount instanceof AccountingAccount === false) {
            throw new AccountingAccountNotFound(
                $isForeign,
                $accountingAccountType,
                $accountingAccountKind,
            );
        }

        return $accountingAccount;
    }
}
