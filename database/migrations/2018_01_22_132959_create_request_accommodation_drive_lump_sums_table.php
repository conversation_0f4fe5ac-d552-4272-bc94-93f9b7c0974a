<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRequestAccommodationDriveLumpSumsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('request_accommodation_drive_lump_sums', function (Blueprint $table) {
            $table->increments('id');
            $table->instance();
            $table->request();
            $table->timestamp('from')->nullable();
            $table->timestamp('to')->nullable();
            $table->boolean('accommodation')->default(false);
            $table->boolean('drive')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('request_accommodation_drive_lump_sums');
    }
}
