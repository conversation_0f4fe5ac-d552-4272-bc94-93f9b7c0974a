<?php

declare(strict_types=1);

use App\Instance;
use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Entities\FeatureSetting;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Enums\IntegrationModeEnum;
use Modules\IntegrationFILE\Pub\Enums\ExportFormatEnum;

class AddsFileIntegrationToAlt extends Migration
{
    private const INSTANCE_ID = 78;

    public function up(): void
    {
        if (!Instance::query()->where('id', self::INSTANCE_ID)->exists()) {
            return;
        }

        $slug = (string)FeatureEnum::FEATURE_INTEGRATION_MODE();
        $feature = Feature::query()->where('slug', $slug)->first();

        if (null === $feature) {
            return;
        }

        $featureSetting = new FeatureSetting();
        $featureSetting->feature_id = $feature->id;
        $featureSetting->instance_id = self::INSTANCE_ID;
        $featureSetting->setting = (string)IntegrationModeEnum::FILE();
        $featureSetting->parameters = ['format' => ExportFormatEnum::ALT_XLSX()->getValue()];
        $featureSetting->save();
    }

    public function down(): void
    {
        FeatureSetting::query()
            ->where('instance_id', self::INSTANCE_ID)
            ->where(
                'setting',
                'FILE',
            )
            ->delete();
    }
}
