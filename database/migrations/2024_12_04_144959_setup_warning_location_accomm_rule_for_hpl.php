<?php

use App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule;
use App\Instance;
use App\Rule;
use Illuminate\Database\Migrations\Migration;

class SetupWarningLocationAccommRuleForHpl extends Migration
{
    public function up(): void
    {
        $instance = Instance::query()->where('domain', 'LIKE', '%hpl048%')->first();

        if ($instance) {
            Rule::updateOrInsert(
                [
                    'instance_id' => $instance->id,
                    'name' => WarningLocationAccommodationRule::NAME,
                ],
                [
                    'level' => Rule::LEVEL_WARNING,
                    'parameters' => json_encode($this->getParameters()),
                ]
            );
        }
    }

    private function getParameters(): array
    {
        $parameters = [
            'default' => 0,
            'US' => [
                'default' => [
                    '1_room' => 645,
                ],
                'groups' => [
                    'ny' => [
                        'locations' => [
                            [ // New York City
                                'northeast' => ["lat" => 40.917577, "lng" => -73.700272],
                                'southwest' => ["lat" => 40.477399, "lng" => -74.25909],
                            ],
                        ],
                        '1_room' => 1000,
                    ],
                ],
            ],
            'PL' => [
                'default' => ['1_room' => 430],
            ],
            'DE' => [
                'default' => ['1_room' => 520],
            ],
            'FR' => [
                'default' => ['1_room' => 645],
            ],
            'GB' => [
                'default' => ['1_room' => 820],
            ],
            'CH' => [
                'default' => ['1_room' => 820],
            ],
            'AT' => [
                'default' => ['1_room' => 500],
            ],
            'ES' => [
                'default' => ['1_room' => 560],
            ],
            'PH' => [
                'default' => ['1_room' => 500],
            ],
            'RO' => [
                'default' => ['1_room' => 430],
            ],
        ];

        $gradeParameters = [];
        for ($i = 0; $i <= 4; $i++) {
            $gradeParameters['grade-' . $i] = $parameters;
        }

        return $gradeParameters;
    }

    public function down(): void
    {
    }
}
