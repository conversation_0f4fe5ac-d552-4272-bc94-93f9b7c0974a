<?php

use App\Services\DocumentCreditAccountingAccount\Enums\DocumentCreditAccountingAccountStrategyEnum;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Entities\FeatureSetting;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Ramsey\Uuid\Uuid;

class EnableAccountsByPaymentAndCountryAndCurrencyForEgo extends Migration
{
    private const EGO_INSTANCE_ID = 65;

    public function up()
    {
        if (!\App\Instance::query()->where('id', self::EGO_INSTANCE_ID)->exists()) {
            return;
        }

        $slug = (string)FeatureEnum::FEATURE_DOCUMENT_CREDIT_ACCOUNTING_ACCOUNT_STRATEGY();
        $feature = Feature::query()->where('slug', $slug)->first();

        if (null === $feature) {
            return;
        }

        $featureSetting = new FeatureSetting();
        $featureSetting->feature_id = $feature->id;
        $featureSetting->instance_id = self::EGO_INSTANCE_ID;
        $featureSetting->setting = (string)DocumentCreditAccountingAccountStrategyEnum::BY_PAYMENT_AND_COUNTRY_AND_CURRENCY_TYPE(
        );
        $featureSetting->parameters = [
            'own_country_instance_currency_instance' => $this->getOwnCountryInstanceCurrencyInstance()->id,
            'own_country_instance_currency_foreign' => $this->getOwnCountryInstanceCurrencyInstance()->id,
            'own_country_foreign_currency_instance' => $this->getOwnCountryInstanceCurrencyInstance()->id,
            'own_country_foreign_currency_foreign' => $this->getOwnCountryInstanceCurrencyInstance()->id,
            'service_card_country_instance_currency_instance' => $this->accountServiceCardInstance()->id,
            'service_card_country_instance_currency_foreign' => $this->accountServiceCardForeign()->id,
            'service_card_country_foreign_currency_instance' => $this->accountServiceCardInstance()->id,
            'service_card_country_foreign_currency_foreign' => $this->accountServiceCardForeign()->id,
        ];
        $featureSetting->save();
    }

    public function down()
    {
        FeatureSetting::query()
            ->where('instance_id', self::EGO_INSTANCE_ID)
            ->where(
                'setting',
                (string)DocumentCreditAccountingAccountStrategyEnum::BY_PAYMENT_AND_COUNTRY_AND_CURRENCY_TYPE()
            )
            ->delete();
    }

    private function getOwnCountryInstanceCurrencyInstance(): AccountingAccount
    {
        $accountCard = AccountingAccount::query()
            ->where('account_number', '234-7')
            ->where('instance_id', self::EGO_INSTANCE_ID)
            ->first();

        if (null !== $accountCard) {
            return $accountCard;
        }

        $accountCard = new AccountingAccount();
        $accountCard->slug = Uuid::uuid4()->toString();
        $accountCard->account_number = '234-7';
        $accountCard->name = 'Rozrachunki - Pracownicy - Środki własne';
        $accountCard->type = AccountingAccountTypeEnum::CREDIT();
        $accountCard->instance_account = 0;
        $accountCard->is_active = 1;
        $accountCard->instance_id = self::EGO_INSTANCE_ID;
        $accountCard->save();

        return $accountCard;
    }

    private function accountServiceCardForeign(): AccountingAccount
    {
        $accountOwn = AccountingAccount::query()
            ->where('account_number', '234-3')
            ->where('instance_id', self::EGO_INSTANCE_ID)
            ->first();

        if (null !== $accountOwn) {
            return $accountOwn;
        }

        $accountOwn = new AccountingAccount();
        $accountOwn->slug = Uuid::uuid4()->toString();
        $accountOwn->account_number = '234-3';
        $accountOwn->name = 'Rozrachunki - Pracownicy  - Karta służbowa';
        $accountOwn->type = AccountingAccountTypeEnum::CREDIT();
        $accountOwn->instance_account = 0;
        $accountOwn->is_active = 1;
        $accountOwn->instance_id = self::EGO_INSTANCE_ID;
        $accountOwn->save();

        return $accountOwn;
    }

    private function accountServiceCardInstance(): AccountingAccount
    {
        $accountOwn = AccountingAccount::query()
            ->where('account_number', '235')
            ->where('instance_id', self::EGO_INSTANCE_ID)
            ->first();

        if (null !== $accountOwn) {
            return $accountOwn;
        }

        $accountOwn = new AccountingAccount();
        $accountOwn->slug = Uuid::uuid4()->toString();
        $accountOwn->account_number = '235';
        $accountOwn->name = 'Rozrachunki - Pracownicy  - Karta służbowa - waluta';
        $accountOwn->type = AccountingAccountTypeEnum::CREDIT();
        $accountOwn->instance_account = 0;
        $accountOwn->is_active = 1;
        $accountOwn->instance_id = self::EGO_INSTANCE_ID;
        $accountOwn->save();

        return $accountOwn;
    }
}
