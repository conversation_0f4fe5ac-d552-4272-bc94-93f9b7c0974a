<?php

use App\Instance;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\Mpk;

class AddMpksForWeg extends Migration
{
    private const WEG_INSTANCE_ID = 43;

    private const COMP_WEG_ID = 63;
    private const COMP_ENEXON_ID = 64;

    public function up(): void
    {
        if (!Instance::query()->where('id', self::WEG_INSTANCE_ID)->exists()) {
            return;
        }

        $dict = [
            self::COMP_ENEXON_ID => [
                ['AJAMU', 'DYREKTOR DS. PRZEMYSŁU'], // exists
                ['SBOZ1', 'KEY BIURO RYNKU ZIELONEJ ENERGII'],
                ['SBPNZ', 'Dyr. Reg. Sprzedaży Rynku Północnego'],
                ['381', 'Wrocław OZE'],
                ['308', '<PERSON>ział Efektywności Energetycznej'],
            ],
            self::COMP_WEG_ID => [
                ['WURT1', 'WURTH MP'],
                ['WURT2', 'WURTH PSZ'],
                ['WURT3', 'WURTH OM'],
            ],
        ];

        foreach ($dict as $mpkCompanyId => $mpks) {
            foreach ($mpks as $mpk) {
                if (Mpk::where('code', $mpk[0])->where('instance_id', self::WEG_INSTANCE_ID)->exists()) {
                    continue;
                }

                Mpk::create([
                    'company_id' => $mpkCompanyId,
                    'instance_id' => self::WEG_INSTANCE_ID,
                    'code' => $mpk[0],
                    'name' => $mpk[1],
                    'slug' => Mpk::generateSlug(),
                    'is_active' => 1,
                ]);
            }
        }
    }

    public function down(): void
    {
        Mpk::where('instance_id', self::WEG_INSTANCE_ID)
            ->whereIn('code', ['AJAMU', 'SBOZ1', 'SBPNZ', '381', '308', 'WURT1', 'WURT2', 'WURT3'])
            ->delete();
    }
}
