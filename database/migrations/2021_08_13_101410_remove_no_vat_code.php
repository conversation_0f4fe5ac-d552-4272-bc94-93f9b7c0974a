<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemoveNoVatCode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_element_types', function (Blueprint $table) {
            $table->integer('vat_number_id')->unsigned()->nullable(true)->change();
        });

        Schema::table('vat_numbers', function (Blueprint $table) {
            $table->boolean('default_non_taxable')->default(false)->after('value');
        });

            DB::statement(
            "update document_element_types set vat_number_id=null where vat_number_id in (select id from vat_numbers vn where code='BRAKVAT')"
        );

        DB::statement(
            "update document_elements set vat_number_id=null where vat_number_id in (select id from vat_numbers vn where code='BRAKVAT')"
        );

        DB::statement(
            "delete from vat_numbers where code='BRAKVAT'"
        );


        DB::statement(
            "update vat_numbers set default_non_taxable=1 where code='VNPD'"
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vat_numbers', function (Blueprint $table) {
            $table->dropColumn('default_non_taxable');
        });
    }
}
