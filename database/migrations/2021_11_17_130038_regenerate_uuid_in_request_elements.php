<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RegenerateUuidInRequestElements extends Migration
{
    private $tables = [
        'request_plane_trips',
        'request_train_trips',
        'request_rented_car_trips',
        'request_private_car_trips',
        'request_company_car_trips',
        'request_bus_trips',
        'request_accomodations',
        'request_private_accomodations',
        'request_ferry_boats',
        'target_points',
    ];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        foreach($this->tables as $table) {
            DB::statement("UPDATE $table SET `uuid` = SUBSTR(MD5(RAND()), 1, 16)");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
