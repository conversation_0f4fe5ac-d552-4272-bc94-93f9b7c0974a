<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddDeductibilityToAllowances extends Migration
{

    public function up()
    {
        Schema::table('request_accounting_travel_expenses', function (Blueprint $table) {
            $table
                ->enum('deductibility', \Modules\Accounting\Priv\Enum\DeductibilityEnum::toArray())
                ->after('cost_of_earning');
        });

        DB::statement('update request_accounting_travel_expenses set deductibility=?', [
            (string)\Modules\Accounting\Priv\Enum\DeductibilityEnum::DEDUCTIBLE()
        ]);

        Schema::table('requests', function (Blueprint $table) {
            $table
                ->enum('mileage_allowance_deductibility', \Modules\Accounting\Priv\Enum\DeductibilityEnum::toArray())
                ->after('mileage_allowance_cost_of_earning');
        });

        DB::statement('update requests set mileage_allowance_deductibility=?', [
            (string)\Modules\Accounting\Priv\Enum\DeductibilityEnum::DEDUCTIBLE()
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('request_accounting_travel_expenses', function (Blueprint $table) {
            $table->dropColumn('deductibility');
        });

        Schema::table('requests', function (Blueprint $table) {
            $table
                ->dropColumn('mileage_allowance_deductibility');
        });
    }
}
