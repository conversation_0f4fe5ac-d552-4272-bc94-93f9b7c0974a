<?php

declare(strict_types=1);

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddLockedToOfferBreakfastMealDeduction extends Migration
{
    public function up(): void
    {
        Schema::table('request_meal_deductions', function (Blueprint $table) {
            $table->boolean('locked_breakfast')->default(false)->after('breakfast');
        });
    }

    public function down(): void
    {
        Schema::table('request_meal_deductions', function (Blueprint $table) {
            $table->dropColumn('locked_breakfast');
        });
    }
}
