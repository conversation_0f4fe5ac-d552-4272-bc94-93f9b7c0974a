<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddColUrlPolicyFile extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('companies', function (Blueprint $table) {

            $driver = Schema::connection($this->getConnection())->getConnection()->getDriverName();

            if ('sqlite' === $driver) {
                $table->string('policy_file',255)->default('');
                $table->string('policy_file_token',100)->default('');
            } else {
                $table->string('policy_file',255);
                $table->string('policy_file_token',100);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('policy_file');
            $table->dropColumn('policy_file_token');
        });
    }
}
