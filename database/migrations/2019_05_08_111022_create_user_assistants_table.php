<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;

class CreateUserAssistantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('user_assistants', function (Blueprint $table) {
            $table->increments('id');
            $table->user();
            $table->assistant();
            $table->timestamp('from')->nullable(false);
            $table->timestamp('to')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_assistants');
    }
}
