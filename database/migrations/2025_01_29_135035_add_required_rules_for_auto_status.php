<?php

use App\Instance;
use App\Rule;
use App\Services\RulesService\Rules\RequestHasAccountingDocuments;
use App\Services\RulesService\Rules\RequestHasSettledLumpSumAmount;
use Illuminate\Database\Migrations\Migration;

class AddRequiredRulesForAutoStatus extends Migration
{
    public function up()
    {
        $instance = Instance::query()->where('name', 'LIKE', '%MS Services%')->first();

        if ($instance) {
            Rule::updateOrInsert(
                [
                    'instance_id' => $instance->id,
                    'name' => RequestHasSettledLumpSumAmount::NAME,
                ],
                [
                    'level' => Rule::LEVEL_WARNING,
                    'parameters' => json_encode([]),
                ]
            );

            Rule::updateOrInsert(
                [
                    'instance_id' => $instance->id,
                    'name' => RequestHasAccountingDocuments::NAME,
                ],
                [
                    'level' => Rule::LEVEL_WARNING,
                    'parameters' => json_encode([]),
                ]
            );
        }
    }

    public function down()
    {
    }
}
