<?php

use App\Instance;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Entities\FeatureSetting;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;

class AutoChangeStatusesToAccountFeature extends Migration
{
    public function up()
    {
        $feature = new Feature();
        $feature->slug = FeatureEnum::FEATURE_AUTO_CHANGE_STATUS_TO_ACCOUNTING();
        $feature->type = FeatureTypeEnum::SWITCH();
        $feature->default = 0;
        $feature->save();

        $instance = Instance::query()->where('name', 'LIKE', '%MS Services%')->first();

        if ($instance) {
            $featureSetting = new FeatureSetting();
            $featureSetting->feature_id = $feature->id;
            $featureSetting->instance_id = $instance->id;;
            $featureSetting->setting = '1';
            $featureSetting->save();

            $feature2 = Feature::query()
                ->where('slug', '=', FeatureEnum::FEATURE_AUTO_CHANGE_STATUS_TO_FINISH())->first();

            $featureSetting = new FeatureSetting();
            $featureSetting->feature_id = $feature2->id;
            $featureSetting->instance_id = $instance->id;;
            $featureSetting->setting = '1';
            $featureSetting->save();
        }
    }

    public function down()
    {
        Feature::query()
            ->where('slug', '=', FeatureEnum::FEATURE_AUTO_CHANGE_STATUS_TO_ACCOUNTING())
            ->delete();
    }
}
