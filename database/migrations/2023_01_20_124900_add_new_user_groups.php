<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddNewUserGroups extends Migration
{
    private const GROUPS_TO_ADD = [
        \App\User::GROUP_NAME_CEO,
        \App\User::GROUP_NAME_CFO,
        \App\User::GROUP_NAME_BM,
        \App\User::GROUP_NAME_TAXI
    ];

    public function up(): void
    {
        $now = \Carbon\Carbon::now();

        $groups = [];

        \App\Instance::all()
            ->each(function (\App\Instance $instance) use (&$groups, $now) {
                foreach (self::GROUPS_TO_ADD as $groupName) {
                    $groups[] = [
                        'instance_id' => $instance->id,
                        'slug' => \Illuminate\Support\Str::lower($groupName),
                        'name' => $groupName,
                        'visible' => true,
                        'type' => 'manual',
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }
            });

        \App\Group::insert($groups);


        $permissions = [];

        \App\Group::query()
            ->whereIn('name', self::GROUPS_TO_ADD)
            ->get()
            ->each(function (\App\Group $group) use (&$permissions, $now) {
                collect(config('permissions.default_permissions')[$group->name])
                    ->each(function (bool $can, string $ability) use (&$permissions, $group, $now) {
                        $permissions[] = [
                            'instance_id' => $group->instance_id,
                            'group_id' => $group->id,
                            'ability' => $ability,
                            'name' => \Illuminate\Support\Str::random(),
                            'scope' => null,
                            'can' => $can,
                            'created_at' => $now,
                            'updated_at' => $now,
                        ];
                    });
            });

        \App\Permission::insert($permissions);
    }


    public function down(): void
    {
        \App\Group::query()->whereIn('name', self::GROUPS_TO_ADD)->delete();
    }
}
