<?php

use App\Instance;
use App\Request;
use Illuminate\Database\Migrations\Migration;
use Modules\Report\Priv\Entity\Report;
use Modules\Report\Priv\Entity\ReportInstance;
use Modules\Report\Priv\Transformer\Input\RequestTrip\RequestTripTransformer;
use Mo<PERSON>les\Report\Priv\Transformer\Output\ColumnOutputDataTransformer;
use Modules\Report\Priv\Transformer\Output\RowOutputDataTransformer;

class AddRequestReport extends Migration
{
    private const MAPPING = [
        ["name" => "Grade", "type" => "keyword"],
        ["name" => "Others", "type" => "keyword"],
        ["name" => "Company", "type" => "keyword"],
        ["name" => "Request", "type" => "keyword"],
        ["name" => "Acceptors", "type" => "keyword"],
        ["name" => "User HR ID", "type" => "keyword"],
        ["name" => "Cost Centre", "type" => "keyword"],
        ["name" => "instance_id", "type" => "keyword"],
        ["name" => "Name Surname", "type" => "keyword"],
        ["name" => "Request number", "type" => "keyword"],
        ["name" => "acceptors_hr_id", "type" => "keyword"],
        ["name" => "Acceptors ERP ID", "type" => "keyword"],
        ["name" => "Request created at", "type" => "date", "format" => "yyyy-MM-dd HH:mm:ss"],
        ["name" => "plane_trips", "type" => "object"],
        ["name" => "train_trips", "type" => "object"],
        ["name" => "accommodations", "type" => "object"],
    ];

    private const FILTERS = [
        ['name' => 'Request created at', 'format' => 'Y-m-d H:i:s', 'frontend_name' => ['range' => ['from' => 'from', 'to' => 'to']]]
    ];

    public function up(): void
    {
        $report = new Report();
        $report->name = 'request_trips';
        $report->base_model = Request::class;
        $report->mapping = self::MAPPING;
        $report->filter = self::FILTERS;
        $report->input_transformer = RequestTripTransformer::class;
        $report->output_transformer = RowOutputDataTransformer::class;
        $report->save();

        $instance = Instance::query()->where('domain', 'LIKE', '%vtl%')->first();

        if ($instance === null) {
            return;
        }

        $reportInstance = new ReportInstance();
        $reportInstance->report_id = $report->id;
        $reportInstance->instance_id = $instance->id;
        $reportInstance->output_transformer = ColumnOutputDataTransformer::class;
        $reportInstance->save();
    }

    public function down(): void
    {
        Report::query()->where('name', '=', 'request_trips')->delete();
    }
}
