<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class ErrorAbroadAccomodationRuleMigration extends Migration
{
    public function up()
    {
        $rules = collect(
            [
                [
                    'level' => App\Rule::LEVEL_AUTOMATION,
                    'name' => 'error_abroad_accomodation_rule',
                    'parameters' => [
                        'grade-0' => [
                            'auto_acceptance' => false
                        ],
                        'grade-1' => [
                            'auto_acceptance' => false
                        ],
                        'grade-2' => [
                            'auto_acceptance' => true
                        ],
                        'grade-3' => [
                            'auto_acceptance' => true
                        ]
                    ]
                ]
            ]
        );

        \App\Instance::all()->each(
            function (\App\Instance $instance) use (&$rules) {
                $rules->each(
                    function ($rule) use ($instance) {
                        $instanceRule = \App\Rule::where('instance_id', $instance->id)
                            ->where('name', $rule['name'])
                            ->first();

                        if (!$instanceRule) {
                            $rule['instance_id'] = $instance;
                            factory(App\Rule::class)->create($rule);
                        }
                    }
                );
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
