<?php

use App\Document;
use App\DocumentElement;
use Illuminate\Database\Migrations\Migration;

class UpdateNetValueForOldDocuments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DocumentElement::withoutEvents(function () {
            DocumentElement::where(function ($q) {
                $q->where('net', 0)->orWhereNull('net');
            })->get()->each(function (DocumentElement $document) {
                $document->recalculateNet();
                $document->recalculateTax();
                $document->save();
            });
        });

        Document::withoutEvents(function () {
            Document::where(function ($q) {
                $q->where('net', 0)->orWhereNull('net');
            })->get()->each(function (Document $document) {
                $document->recalculate();
                $document->save();
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
