<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInstanceAccountingAccountsTable extends Migration
{
    public function up(): void
    {
        Schema::create('instance_accounting_accounts', function (Blueprint $table) {
            $table->increments('id');

            $table->unsignedInteger('instance_id');
            $table->foreign('instance_id')->references('id')->on('instances')->onDelete('restrict');

            $table->unsignedInteger('accounting_account_id');
            $table->foreign('accounting_account_id')->references('id')->on('accounting_accounts')->onDelete('restrict');

            $table->string('kind');
            $table->string('type');
            $table->boolean('foreign');

            $table->timestamps();

            $table->unique(['instance_id', 'kind', 'type', 'foreign'], 'instance_accounting_account_unique');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('instance_accounting_accounts');
    }
}
