<?php

use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Ramsey\Uuid\Uuid;

class AttachAcceptorsAsFeatureMigration extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::beginTransaction();

        /** @var Feature $feature */
        $featureRequest = factory(Feature::class)->create([
            'slug' => FeatureEnum::FEATURE_MANUAL_ATTACH_ACCEPTORS(),
            'type' => FeatureTypeEnum::SWITCH(),
            'default' => 0,
        ]);

        /** @var Feature $feature */
        $featureSettlement = factory(Feature::class)->create([
            'slug' => FeatureEnum::FEATURE_MANUAL_ATTACH_SETTLEMENT_ACCEPTORS(),
            'type' => FeatureTypeEnum::SWITCH(),
            'default' => 0,
        ]);

        \App\Instance::withTrashed()->get()->each(function(\App\Instance $instance) use ($featureRequest, $featureSettlement) {

            $attachAcceptors = $instance->getModuleSettings('defaultAcceptors')['attachAcceptors'] ?? false;
            if($attachAcceptors) {
                factory(\Modules\FeatureSwitcher\Priv\Entities\FeatureSetting::class)->create([
                    'feature_id' => $featureRequest->id,
                    'instance_id' => $instance->id,
                    'setting' => 1
                ]);
            }
            $canAttachSettlementAcceptors = $instance->getModuleSettings('defaultAcceptors')['attachSettlementAcceptors'] ?? false;
            if($canAttachSettlementAcceptors) {
                factory(\Modules\FeatureSwitcher\Priv\Entities\FeatureSetting::class)->create([
                    'feature_id' => $featureSettlement->id,
                    'instance_id' => $instance->id,
                    'setting' => 1
                ]);
            }
        });

        DB::commit();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Feature::whereIn('slug', [
            FeatureEnum::FEATURE_MANUAL_ATTACH_ACCEPTORS(),
            FeatureEnum::FEATURE_MANUAL_ATTACH_SETTLEMENT_ACCEPTORS(),
        ])
            ->each(function (Feature $feature) {
                $feature->delete();
            });
    }
}
