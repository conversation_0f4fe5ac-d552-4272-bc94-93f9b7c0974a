<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AccountDimensionsDocumentHeaderType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('
            ALTER TABLE `account_dimensions` CHANGE `visibility` `visibility` VARCHAR(64) NOT NULL;
        ');

        Schema::create('document_account_dimension_items', function (Blueprint $table) {
            $table->integer('id', true, true);
            $table->uuid('slug')->unique();
            $table->integer('document_id', false, true);
            $table->integer('request_id', false, true);
            $table->integer('account_dimension_id', false, true);
            $table->integer('account_dimension_item_id', false, true);
            $table->timestamps();

            $table->foreign('request_id', 'ddi_request_id_fkey')->references('id')->on('requests')->onDelete('cascade');
            $table->foreign('document_id', 'ddi_document_id_fkey')->references('id')->on('documents')->onDelete('cascade');
            $table->foreign('account_dimension_id', 'ddi_account_dimension_id_fkey')->references('id')->on('account_dimensions')->onDelete('cascade');
            $table->foreign('account_dimension_item_id', 'ddi_account_dimension_item_id_fkey')->references('id')->on('account_dimension_items')->onDelete('cascade');
        });

        $documentAccountDimensionItemRepository = resolve(\Modules\Analytics\Priv\Repositories\DocumentAccountDimensionItemRepository::class);

        \App\Document::chunk(100, function (\Illuminate\Database\Eloquent\Collection $documents) use ($documentAccountDimensionItemRepository) {
            $documents->each(function(\App\Document $document) use ($documentAccountDimensionItemRepository) {
                if($document->request !== null) {
                    /** @var \Modules\Analytics\Priv\Entities\RequestAccountDimensionItem $accountDimensionItem */
                    foreach($document->request->accountDimensionItems as $accountDimensionItem) {
                        $documentAccountDimensionItemRepository->connectWithItem($document->id, $document->request_id, $accountDimensionItem->accountDimensionItem);
                    }
                }
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('document_account_dimension_items');
    }
}
