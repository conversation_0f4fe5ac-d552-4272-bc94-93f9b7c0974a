<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use App\Services\RulesService\Rules\RequestAccountingAllowancesHasAccountDimensionsRule;
use App\Rule;
use App\Instance;

class RequestAccountingAccountDimensionItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('request_accounting_account_dimension_items', function (Blueprint $table) {
            $table->integer('id', true, true);
            $table->uuid('slug')->unique();
            $table->integer('request_id', false, true);
            $table->integer('account_dimension_id', false, true);
            $table->integer('account_dimension_item_id', false, true);
            $table->timestamps();

            $table->foreign('request_id', 'raadi_request_id_fkey')->references('id')->on('requests')->onDelete('cascade');
            $table->foreign('account_dimension_id', 'raadi_account_dimension_id_fkey')->references('id')->on('account_dimensions')->onDelete('cascade');
            $table->foreign('account_dimension_item_id', 'raadi_account_dimension_item_id_fkey')->references('id')->on('account_dimension_items')->onDelete('cascade');
        });

        $rules = collect([
            [
                'level' => Rule::LEVEL_ERROR,
                'name' => RequestAccountingAllowancesHasAccountDimensionsRule::NAME,
                'parameters' => []
            ],
        ]);

        Instance::withTrashed()->get()->each(
            function (Instance $instance) use (&$rules) {
                $rules->each(
                    function ($rule) use ($instance) {
                        $instanceRule = Rule::where('instance_id', $instance->id)
                            ->where('name', $rule['name'])
                            ->first();

                        if (!$instanceRule) {
                            $rule['instance_id'] = $instance;
                            factory(Rule::class)->create($rule);
                        }
                    }
                );
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('request_accounting_account_dimension_items');
        Rule::where('name', RequestAccountingAllowancesHasAccountDimensionsRule::NAME)->delete();

    }
}
