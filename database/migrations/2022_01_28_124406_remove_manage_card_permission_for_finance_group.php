<?php

use Illuminate\Database\Migrations\Migration;

class RemoveManageCardPermissionForFinanceGroup extends Migration
{
    public function up(): void
    {
        $this->execute();
    }

    public function down(): void
    {
        $this->execute(true);
    }

    private function execute(bool $can = false): void
    {
        $financeGroupIds = \App\Group::query()
            ->where('name', \App\User::GROUP_NAME_FINANCE)
            ->pluck('id')
            ->toArray();

        \App\Permission::query()
            ->whereIn('group_id', $financeGroupIds)
            ->where('ability', \App\Permission::INSTANCE_SETTINGS_MANAGE_COMPANY_CARDS)
            ->update(['can' => $can]);
    }
}
