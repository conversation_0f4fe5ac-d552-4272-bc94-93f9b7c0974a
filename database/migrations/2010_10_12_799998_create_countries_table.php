<?php

use \App\Vendors\Blueprint;
use \App\Vendors\Migration;

class CreateCountriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('countries', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
            $table->string('country_code')->nullable();
            $table->string('polish_name');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('countries');
    }
}
