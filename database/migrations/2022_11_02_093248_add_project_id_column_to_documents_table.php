<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Enums\FeatureTypeEnum;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;

class AddProjectIdColumnToDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->integer('project_id')->unsigned()->index()->nullable()->after('mpk_id');
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade')->onUpdate('cascade');
        });

        \Illuminate\Support\Facades\DB::statement(
            "
            UPDATE documents
            INNER JOIN requests
            ON documents.request_id = requests.id
            SET documents.project_id = requests.project_id
        "
        );

        factory(Feature::class)->create([
            'slug' => FeatureEnum::FEATURE_MANAGE_DOCUMENT_PROJECT_ENABLED(),
            'type' => FeatureTypeEnum::SWITCH(),
            'default' => '0',
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->dropForeign(['project_id']);
            $table->dropColumn('project_id');
        });

        Feature::where('slug', FeatureEnum::FEATURE_MANAGE_DOCUMENT_PROJECT_ENABLED())
            ->each(function (Feature $feature) {
                $feature->delete();
            });
    }
}
