<?php

use App\Vendors\Migration;
use App\Vendors\Blueprint;

class CreateVatNumbersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $this->getSchema()->create('vat_numbers', function (Blueprint $table) {
            $table->increments('id');
            $table->instance();
            $table->accountingAccount();
            $table->string('name');
            $table->string('code');
            $table->decimal('value', 6, 3)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('vat_numbers');
    }
}
