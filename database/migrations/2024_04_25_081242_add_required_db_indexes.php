<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migration;

class AddRequiredDbIndexes extends Migration
{
    public function up(): void
    {
        $this->addIndexIfNotExists('document_elements', 'idx_request_element', ['request_element_id', 'request_element_type', 'deleted_at']);
        $this->addIndexIfNotExists('requests', 'idx_slug_deleted_at', ['slug', 'deleted_at']);
        $this->addIndexIfNotExists('locations', 'idx_localizable', ['localizable_id', 'localizable_type']);
    }

    public function down(): void
    {
        $this->dropIndexIfExists('document_elements', 'idx_request_element');
        $this->dropIndexIfExists('requests', 'idx_slug_deleted_at');
        $this->dropIndexIfExists('locations', 'idx_localizable');
    }

    protected function addIndexIfNotExists(string $table, string $index, array $columns): void
    {
        $existingIndex = DB::select("SHOW INDEX FROM $table WHERE Key_name = ?", [$index]);

        if (empty($existingIndex)) {
            $columnsList = implode(', ', array_map(function($column) {
                return "`$column`";
            }, $columns));

            $sql = sprintf("ALTER TABLE %s ADD INDEX %s (%s)", $table, $index, $columnsList);
            DB::statement($sql);
        }
    }

    protected function dropIndexIfExists(string $table, string $index): void
    {
        $existingIndex = DB::select("SHOW INDEX FROM $table WHERE Key_name = ?", [$index]);

        if (!empty($existingIndex)) {
            $sql = sprintf("ALTER TABLE %s DROP INDEX %s", $table, $index);
            DB::statement($sql);
        }
    }
}
