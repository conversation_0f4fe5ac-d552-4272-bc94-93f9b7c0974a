<?php

use App\Request;
use App\Company;
use App\Instance;
use App\User;
use Illuminate\Database\Migrations\Migration;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Report\Priv\Entity\Report;
use Modules\Report\Priv\Entity\ReportInstance;
use Modules\Report\Priv\Transformer\Input\Expense\ExpenseTransformer;
use Modules\Report\Priv\Transformer\Output\ColumnOutputDataTransformer;

class CreateExpensesReport extends Migration
{
    private const MAPPING = [
        ['name' => 'instance_id', 'type' => 'keyword'],
        ['name' => 'Request number', 'type' => 'keyword'],
        ['name' => 'Request created at', 'type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
        ['name' => 'Approval date', 'type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
        ['name' => 'Posting date', 'type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
        ['name' => 'Claim date', 'type' => 'date', 'format' => 'yyyy-MM-dd HH:mm:ss'],
        ['name' => 'Status', 'type' => 'keyword'],
        ['name' => 'Employee', 'type' => 'keyword'],
        ['name' => 'Employee ERP ID', 'type' => 'keyword'],
        ['name' => 'Approver', 'type' => 'keyword'],
        ['name' => 'Approver ERP ID', 'type' => 'keyword'],
        ['name' => 'Cost Centre', 'type' => 'keyword'],
        ['name' => 'Company', 'type' => 'keyword'],
        ['name' => 'Expense', 'type' => 'object'],
        ['name' => 'Account dimension', 'type' => 'object'],
    ];

    private const FILTERS = [
        [
            'name' => 'Request created at',
            'format' => 'Y-m-d H:i:s',
            'frontend_name' => [
                'range' => [
                    'to' => 'to',
                    'from' => 'from',
                ],
            ],
        ],
        [
            'name' => 'Employee',
            'resource' => [
                'model' => User::class,
                'property' => 'full_name',
            ],
            'frontend_name' => 'user_id',
        ],
        [
            'name' => 'Cost Centre',
            'resource' => [
                'model' => Mpk::class,
                'property' => 'name',
            ],
            'frontend_name' => 'mpk_id',
        ],
        [
            'name' => 'Account dimension',
            'resource' => [
                'model' => AccountDimension::class,
                'valueModel' => AccountDimensionItem::class,
                'property' => 'name',
                'valueProperty' => 'name',
                'identifier' => 'slug',
            ],
            'frontend_name' => [
                'name' => 'account-dimensions_',
                'type' => 'contains'
            ],
        ],
        [
            'name' => 'Company',
            'resource' => [
                'model' => Company::class,
                'property' => 'name',
            ],
            'frontend_name' => 'company_id',
        ],
        [
            'name' => 'Status',
            'resource' => [
                'model' => Request::class,
                'fromStaticMethod' => 'statusesList',
            ],
            'frontend_name' => [
                'name' => 'statuses',
                'type' => 'multiple'
            ],
        ],
    ];

    private const REPORT_NAME = 'expenses';

    public function up(): void
    {
        $report = new Report();
        $report->name = self::REPORT_NAME;
        $report->base_model = Request::class;
        $report->mapping = self::MAPPING;
        $report->filter = self::FILTERS;
        $report->input_transformer = ExpenseTransformer::class;
        $report->output_transformer = ColumnOutputDataTransformer::class;
        $report->save();

        $instance = Instance::query()->where('domain', 'LIKE', '%vtl%')
            ->orWhere('domain', 'LIKE', '%tst048%')
            ->orWhere('domain', 'LIKE', '%mss048%')
            ->first();

        if ($instance === null) {
            return;
        }

        $reportInstance = new ReportInstance();
        $reportInstance->report_id = $report->id;
        $reportInstance->instance_id = $instance->id;
        $reportInstance->output_transformer = ColumnOutputDataTransformer::class;
        $reportInstance->save();
    }

    public function down(): void
    {
        Report::query()->where('name', '=', self::REPORT_NAME)->delete();
    }
}