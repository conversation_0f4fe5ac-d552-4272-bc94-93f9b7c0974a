<?php

use App\Instance;
use Illuminate\Database\Migrations\Migration;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use Modules\FeatureSwitcher\Priv\Entities\FeatureSetting;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Enums\IntegrationModeEnum;
use Modules\IntegrationFILE\Pub\Enums\ExportFormatEnum;

class MndEnableFileIntegration extends Migration
{
    private const INSTANCE_NAME = 'Mindento  Sp';

    public function up()
    {
        $instance = Instance::query()->where('name', 'like', '%' . self::INSTANCE_NAME . '%')->first();
        if (!$instance) {
            return;
        }

        $slug = (string)FeatureEnum::FEATURE_INTEGRATION_MODE();
        $feature = Feature::query()->where('slug', $slug)->first();

        if (null === $feature) {
            return;
        }

        $featureSetting = new FeatureSetting();
        $featureSetting->feature_id = $feature->id;
        $featureSetting->instance_id = $instance->id;
        $featureSetting->setting = (string)IntegrationModeEnum::FILE();
        $featureSetting->parameters = ['format' => ExportFormatEnum::MND_XLSX_ENOVA()->getValue()];
        $featureSetting->save();
    }

    public function down()
    {
        $instance = Instance::query()->where('name', 'like', '%' . self::INSTANCE_NAME . '%')->first();
        if (!$instance) {
            return;
        }

        FeatureSetting::query()
            ->where('instance_id', $instance->id)
            ->where(
                'setting',
                'FILE',
            )
            ->delete();
    }
}
