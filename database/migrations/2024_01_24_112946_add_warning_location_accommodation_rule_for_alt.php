<?php

use App\Compliance\Instances\Amrest\Rules\WarningLocationAccommodationRule;
use App\Instance;
use App\Rule;
use Illuminate\Database\Migrations\Migration;

class AddWarningLocationAccommodationRuleForAlt extends Migration
{
    public function up(): void
    {
        $instanceExists = Instance::where('id', 78)->exists();

        if ($instanceExists) {
            Rule::updateOrInsert(
                [
                    'instance_id' => 78,
                    'name' => WarningLocationAccommodationRule::NAME,
                ],
                [
                    'level' => Rule::LEVEL_WARNING,
                    'parameters' => json_encode($this->getParameters()),
                ]
            );
        }
    }

    public function down(): void
    {
        Rule::where('name', WarningLocationAccommodationRule::NAME)->delete();
    }

    private function getParameters(): array
    {
        $parameters = [
            'default' => 300,
            'PL' => [
                'default' => [
                    '1_room' => 300,
                    '2_room' => 300,
                ],
                'groups' => [
                    'group_450' => [
                        'locations' => [
                            [ // Warszawa
                                'northeast' => ["lat" => 52.3679992, "lng" => 21.2710984],
                                'southwest' => ["lat" => 52.0978767, "lng" => 20.8512898],
                            ],
                        ],
                        '1_room' => 450,
                        '2_room' => 450,
                    ],
                    'group_400' => [
                        'locations' => [
                            [  // Kraków
                                'northeast' => ["lat" => 50.1261259, "lng" => 20.2174976],
                                'southwest' => ["lat" => 49.9674054, "lng" => 19.7922485],
                            ],
                            [ // Trójmiasto
                                'northeast' => ["lat" => 54.577007921350265, "lng" => 18.73675367961356],
                                'southwest' => ["lat" => 54.225273750914226, "lng" => 18.357725362179842],
                            ],
                        ],
                        '1_room' => 400,
                        '2_room' => 400,
                    ],
                    'group_350' => [
                        'locations' => [
                            [ // Poznań
                                'northeast' => ["lat" => 52.514644, "lng" => 17.064122],
                                'southwest' => ["lat" => 52.314731, "lng" => 16.768451],
                            ],
                            [ // Wrocław
                                'northeast' => ["lat" => 51.2114736, "lng" => 17.1763478],
                                'southwest' => ["lat" => 51.04268219999999, "lng" => 16.80738],
                            ],
                        ],
                        '1_room' => 350,
                        '2_room' => 350,
                    ],
                ],
            ],
        ];

        $gradeParameters = [];
        for ($i = 0; $i <= 4; $i++) {
            $gradeParameters['grade-' . $i] = $parameters;
        }

        return $gradeParameters;
    }
}
