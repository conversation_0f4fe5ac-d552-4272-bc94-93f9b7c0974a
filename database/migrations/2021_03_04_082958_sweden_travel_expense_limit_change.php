<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class SwedenTravelExpenseLimitChange extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sweden = \App\Country::where(['country_code' => 'SE'])->first();

        if ($sweden === null) {
            return;
        }

        DB::statement(
            'update travel_expenses set accommodation_limit=1800 where country_id=? and instance_id is null and accommodation_limit=18000',
            [
                $sweden->id
            ]
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
