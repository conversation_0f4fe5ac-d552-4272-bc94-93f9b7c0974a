<?php
/** @var Illuminate\Database\Eloquent\Factory $factory */

use Carbon\Carbon;
use Faker\Generator as Faker;

$factory->define(App\RequestMileageAllowance::class, function (Faker $faker) {

    static $instances;

    if (!$instances) {
        $instances = \App\Instance::with(['requests.privateCarTrips', 'accountingAccounts'])
            ->whereNotIn('domain', [
                config('vaterval.demoDomain'),
                config('vaterval.emptyDomain'),
            ])
            ->get();
    }

    $instance = $instances->random(1)->first();
    $request  = $instance->requests->random(1)->first();

    $startDate = Carbon::now()->addDays(rand(2, 15))
        ->hour(rand(0,23))
        ->minute(rand(0,59))
        ->second(rand(0,59));

    $endDate = clone $startDate->addMinutes(rand(40, 12*60));

    $car = $request->privateCarTrips->random(1)->first();
    return [
        'instance_id'           => $instance,
        'request_id'            => $request,
        'private_car_trip_id'   => $car,
        'mpk_id'                => $request->mpk_id,
        'accounting_account_id' => $instance->accountingAccounts->random(1)->first(),
        'departure_date'        => $startDate,
        'arrival_date'          => $endDate,
        'round_trip'            => rand(0, 1),
        'vehicle_type'          => $car->vehicle_type,
        'cost_of_earning'       => rand(0, 1),
        'distance'              => $faker->randomFloat(3, 0, 3000),
    ];
});