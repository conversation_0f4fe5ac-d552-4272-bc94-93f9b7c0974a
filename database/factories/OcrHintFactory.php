<?php

use App\Ocr\Enum\ColumnNameEnum;
use App\Ocr\OcrHint;
use Faker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(OcrHint::class, function(Faker $faker) {
    static $instances;

    if ( !$instances ) {
        $instances = \App\Instance::with(['documents', 'providers'])
            ->whereNotIn('domain', [
                config('vaterval.demoDomain'),
                config('vaterval.emptyDomain'),
            ])
            ->get();
    }

    $columnNames = collect(array_map(fn(ColumnNameEnum $column) => $column->getValue(), ColumnNameEnum::values()));

    $column = $columnNames->random(1)->first();
    $instance = $instances->get(1)->first();
    $document = $instance->documents->random(1)->first();

    switch ($column) {
        case 'provider_id':
        {
            $provider = $instance->providers->random(1)->first();
            $value = (string) $provider->id;
            $label = $provider->name . ' ('.$provider->registry_number.')';
            break;
        }
        case 'document_number': {
            $value = $faker->swiftBicNumber;
            $label = $value;
            break;
        }
        case 'gross': {
            $value = $faker->randomFloat(2, 0, 10000);
            $label = $value;
            break;
        }
        case 'issue_date': {
            $value = $faker->date('Y-m-d');
            $label = $value;
            break;
        }
        case ColumnNameEnum::PROVIDER_SUGGESTED()->getValue():
        case ColumnNameEnum::PURCHASER_SUGGESTED()->getValue(): {
            $value = 0;
            $label = $value;
            $params = '{"name":"Mindento Sp. z o.o.","registry_number":"PL1132923883","address":"Mokotowska 1","postcode":"00-640","country_id":115,"city":"Warszawa"}';
            break;
        }
    }

    return [
        'instance_id' => $instance->id,
        'document_id' => $document->id,
        'column' => $column,
        'params' => $params ?? null,
        'value' => $value,
        'label' => $label,
    ];
});
