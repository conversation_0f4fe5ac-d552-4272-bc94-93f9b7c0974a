<?php

use <PERSON>aker\Generator as Faker;
use <PERSON>\Uuid\Uuid;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(Modules\Accounting\Priv\Entities\Mpk::class, function(Faker $faker) {
    static $instances;

    if(!$instances) {
        $instances = \App\Instance::all();
    }

    $instance = $instances->random(1)->first();

    return [
        'instance_id' => $instance,
        'name'        => $faker->text(16),
        'code'        => 1,
        'slug'        => Uuid::uuid4(),
    ];
});
