<?php

use <PERSON>aker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\User::class, function(Faker $faker) {
    static $instances;
    static $countries;

    if(!$instances) {
        $instances = \App\Instance::with(['mpks', 'projects', 'companies'])->get();
        $countries = \App\Country::all();
    }

    $instance = $instances->random(1)->first();

    $email = $faker->numberBetween(0, 1000) . $faker->unique()->safeEmail . $faker->numberBetween(0, 1000);
    return [
        'slug'           => str_random(16),
        'instance_id'    => $instance,
        'company_id'     => $instance->companies->random(1)->first(),
        'mpk_id'         => $instance->mpks->random(1)->first(),
        'first_name'     => $faker->firstName,
        'last_name'      => $faker->lastName,
        'grade'          => 0,
        'level'          => 1,
        'policy'         => $faker->word,
        'email'          => $email,
        'employee_unique_identifier' => $email,
        'erp_id'         => $faker->numberBetween(0, 1000),
        'remember_token' => str_random(10),
        'phone'          => $faker->e164PhoneNumber,
        'locale'         => 'pl_PL.utf8',
        'avatar'         => null,
        'phone_ice'      => $faker->e164PhoneNumber,
        'nationality_id'     => $countries->random(1)->first(),
        'citizenship_id'     => $countries->random(1)->first(),
        'birth_date' => $faker->dateTimeBetween('-70 years', '-20 years'),
        'passport_number' => strtoupper($faker->bothify("?? #######")),
        'passport_issue_date' => $faker->dateTimeBetween('-2 years', '-3 months'),
        'passport_valid_date' => $faker->dateTimeBetween('+2 months', '+10 years'),
        'identity_card_number' => strtoupper($faker->bothify("??? ########")),
    ];
});

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\User::class, function() {
    static $instance;
    static $country;

    if(!$instance || !$country) {
        $withInstance = ['companies', 'mpks'];
        $instance = \App\Instance::with($withInstance)->first();
        $country = \App\Country::first();
    }

    return [
        'slug'           => \Tests\SessionTrait::$USER_SLUG,
        'instance_id'    => $instance,
        'company_id'     => $instance->companies->first(),
        'mpk_id'         => $instance->mpks->first(),
        'first_name'     => 'Krzysztof',
        'last_name'      => 'Ze Szczecina',
        'grade'          => 0,
        'level'          => 1,
        'policy'         => 'policy',
        'email'          => $email = '<EMAIL>',
        'employee_unique_identifier' => $email,
        'erp_id'         => 'erp id',
        'remember_token' => 'random remember token',
        'phone'          => '*********',
        'locale'         => 'pl_PL.utf8',
        'avatar'         => null,
        'phone_ice'      => '*********',
        'nationality_id'     => $country,
        'citizenship_id'     => $country,
        'birth_date' => new DateTime('1990-01-01'),
        'passport_number' => '*********',
        'passport_issue_date' => new DateTime('2018-01-01'),
        'passport_valid_date' => new DateTime('2028-01-01'),
        'identity_card_number' => 'ABC123456',
    ];
}, 'logged');
