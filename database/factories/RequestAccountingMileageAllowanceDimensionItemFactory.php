<?php

use <PERSON>aker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(\Modules\Analytics\Priv\Entities\RequestAccountingMileageAllowanceAccountDimensionItem::class, function() {
    $slugGenerator = resolve(\App\Services\SlugGeneratorService::class);

    return [
        'slug' => $slugGenerator->generate(),
        'request_id' => null,
        'request_accounting_mileage_allowance_id' => null,
        'account_dimension_id' => null,
        'account_dimension_item_id' => null,
    ];
});