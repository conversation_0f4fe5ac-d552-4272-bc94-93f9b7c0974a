<?php

use Faker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\Company::class, function(Faker $faker) {
    $faker->addProvider(new \Faker\Provider\en_US\Company($faker));
    $faker->addProvider(new \Faker\Provider\at_AT\Payment($faker));

    static $instances;

    if(!$instances) {
        $instances = \App\Instance::all();
    }

    $instance = $instances->random(1)->first();

    return [
        'created_at'  => \Carbon\Carbon::now()->subDay(rand(1, 10)),
        'instance_id' => $instance,
        'name'        => $faker->company,
        'nip'         => $faker->vat,
        'code'        => $faker->numberBetween(100, 999),
        'slug' => resolve(\App\Services\SlugGeneratorService::class)->generate()
    ];
});
