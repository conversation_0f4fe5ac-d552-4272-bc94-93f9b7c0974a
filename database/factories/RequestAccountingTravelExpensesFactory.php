<?php
/** @var Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;

$factory->define(App\RequestAccountingTravelExpenses::class, function (Faker $faker) {
    static $requests;

    if ( !$requests ) {
        $requests = \App\Request::with('instance')->get();
    }

    $request = $requests->random(1)->first();
    return [
        'request_id' => $request->id,
        'instance_id' => $request->instance_id,
        'accounting_account_id' => $request->instance->accounting_account_id,
        'mpk_id' => $request->mpk_id,
        'cost_of_earning' => rand(0, 1),
        'amount' => $faker->randomFloat(2, 0, 1000),
    ];
});