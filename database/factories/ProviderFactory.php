<?php
/** @var Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;

$factory->define(App\Provider::class, function(Faker $faker) {
    static $instances;
    static $countries;

    if ( !$instances ) {
        $instances = \App\Instance::all();
    }
    if ( !$countries ) {
        $countries = \App\Country::all();
    }

    /**
     * @var \App\Instance $instance
     */
    $instance = $instances->random(1)->first();
    /**
     * @var \App\Country $country
     */
    $country = $countries->random(1)->first();

    return [
        'instance_id'     => $instance,
        'country_id'      => $country,
        'address'         => $faker->streetAddress,
        'name'            => $faker->company,
        'postcode'        => $faker->postcode,
        'erp_id'          => $faker->numberBetween(0, 1000),
        'registry_number' => $faker->bankAccountNumber,
        'slug' => resolve(\App\Services\SlugGeneratorService::class)->generate()
    ];
});