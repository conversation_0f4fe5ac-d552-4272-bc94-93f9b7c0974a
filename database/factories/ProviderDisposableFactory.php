<?php
/** @var Illuminate\Database\Eloquent\Factory $factory */

use App\Country;
use App\Instance;
use Faker\Generator as Faker;
use Mindento\Provider\Disposable\Model\Provider;

$factory->define(Provider::class, function (Faker $faker) {
    static $instances;
    static $countries;
    $instances = $instances ?? Instance::all();
    $countries = $countries ?? Country::all();
    $instance = $instances->random(1)->first();
    $country = $countries->random(1)->first();

    return [
        'instance_id' => $instance,
        'country_id' => $country,
        'address' => $faker->streetAddress,
        'city' => $faker->city,
        'name' => $faker->company,
        'postcode' => $faker->postcode,
        'erp_id' => $faker->numberBetween(0, 1000),
        'tax_id' => $faker->bankAccountNumber,
    ];
});