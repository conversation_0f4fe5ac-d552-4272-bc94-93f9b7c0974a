<?php

use <PERSON>aker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\PrivateCarTrip::class, function(Faker $faker) {

    $currencies = \App\Currency::all();

    $departure_at = Carbon\Carbon::now()->addDays(rand(2, 15))
        ->hour(rand(0,23))
        ->minute(rand(0,59))
        ->second(rand(0,59));

    $return_at = clone $departure_at->addMinutes(rand(40, 12*60));

    return [
        'departure_at'            => $departure_at,
        'return_at'               => $return_at,
        'round_trip'              => (bool)rand(0,1),
        'distance'                => $faker->randomFloat(3, 10, 3000),
        'vehicle_type'            => \App\PrivateCarTrip::vehicleTypes()->random(1)->first(),
        'other_costs_amount'      => $faker->randomFloat(null, 10, 200),
        'other_costs_currency_id' => $currencies->random(1)->first()->id,
        'amount'                  => $faker->randomFloat(null, 10, 20000),
        'amount_currency_id'      => $currencies->random(1)->first()->id,
    ];
});
