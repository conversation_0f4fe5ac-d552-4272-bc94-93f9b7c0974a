<?php

use Faker\Generator as Faker;
use Modules\ExchangeRate\Priv\Providers\FxRates\Nbp\NbpFxRateProvider;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\Instance::class, function (Faker $faker) {
    $faker->addProvider(new \Faker\Provider\en_US\Company($faker));
    $faker->addProvider(new \Faker\Provider\at_AT\Payment($faker));

    static $currencies;
    static $countries;

    if (!$currencies) {
        $currencies = \App\Currency::all();
    }

    if (!$countries) {
        $countries = \App\Currency::all();
    }

    $modules = [
        'projects' => [
            'enabled' => rand(0, 1) === 1
        ],
        'compliance' => [
            'name' => 'amrest'
        ]
    ];

    return [
        'slug'                         => str_random(16),
        'name'                         => $faker->company,
        'address'                      => $faker->address,
        'city'                         => $faker->city,
        'post_code'                    => $faker->postcode,
        'nip'                          => $faker->vat,
        'domain'                       => $faker->unique()->domainName,
        'trip_agent'                   => $faker->text,
        'currency_id'                  => $currencies->random(1)->first(),
        'country_id'                   => $countries->random(1)->first(),
        'android_version'              => 'v1.0.0',
        'ios_version'                  => 'v1.0.0',
        'ios_force_update'             => rand(0, 1),
        'android_force_update'         => rand(0, 1),
        'accounting_reminder_settings' => [
            'first_reminder_after_days' => 8,
            'next_reminder_after_days'  => 14,
            'cycle_reminder_after_days' => 8
        ],
        'modules' => $modules,
        'mpks_sync_setting' => [
            'strategy' => 'AmrestMpksSyncStrategy',
            'frequency' => 'daily',
            'time' => '21:05',
            'host' => '*************',
            'user_name' => 'talend.mindento_sftp',
            'password' => 'a24Vdg5',
            'key_path' => storage_path('amrest_id_rsa.ppk'),
            'path' => '/talend.mindento_sftp/test/',
        ],
        'exchange_rate_provider' => NbpFxRateProvider::SLUG,
    ];
});
