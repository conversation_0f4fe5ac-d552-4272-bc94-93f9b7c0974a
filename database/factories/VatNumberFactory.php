<?php

use Faker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(Modules\Accounting\Priv\Entities\VatNumber::class, function(Faker $faker) {
    static $instances;

    if ( !$instances ) {
        $instances = \App\Instance::with('accountingAccounts')->get();
    }

    $instance = $instances->random(1)->first();
    return [
        'instance_id' => $instance,
        'accounting_account_id' => $instance->accountingAccounts->random(1)->first(),
        'name' => $faker->word,
        'code' => $faker->word,
        'value' => rand(1,99),
        'is_active' => \App\Enum\ActiveEnum::ACTIVE(),
        'slug' => resolve(\App\Services\SlugGeneratorService::class)->generate(),
        'default_non_taxable' => false
    ];
});
