<?php
/** @var Illuminate\Database\Eloquent\Factory $factory */

use Faker\Generator as Faker;

$factory->define(\Modules\ExchangeRate\Priv\Models\ExchangeRateEcb::class, function (Faker $faker) {
    static $currencies;

    if (!$currencies) {
        $currencies = \App\Currency::all();
    }

    return [
        'effective_date' => new Carbon\Carbon(date('Y-m-d')),
        'currency_id'    => $currencies->random(1)->first(),
        'rate'           => $faker->randomFloat(7, 0, 30)
    ];
});