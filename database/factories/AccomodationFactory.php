<?php

use Faker\Generator as Faker;

/** @var Illuminate\Database\Eloquent\Factory $factory */
$factory->define(App\Accomodation::class, function(Faker $faker) {
    $arrival_at = Carbon\Carbon::now()->addDays(rand(2, 15))
        ->hour(rand(0,23))
        ->minute(rand(0,59))
        ->second(rand(0,59));

    $return_at = clone $arrival_at->addMinutes(rand(40, 12*60));


    return [
        'amount' => $faker->randomFloat(null, 10, 20000),
        'amount_currency_id' => \App\Currency::all()->random(1)->first()->id,
        'arrival_at' => $arrival_at,
        'departure_at' => $return_at,
        'wifi' => rand(0, 1),
        'breakfast' => rand(0, 1),
        'standard' => rand(0, 5),
        'number_of_rooms' => 1,
    ];
});
