<?php

class ProjectsSeeder extends SeederExtended
{
    /**
     * @var \App\Services\SlugGeneratorService
     */
    protected $slugGenerator;

    public function __construct()
    {
        $this->slugGenerator = resolve(\App\Services\SlugGeneratorService::class);
    }

    public function run()
    {
        $projects = collect(self::fakerProjects());

        \App\Instance::all()->each(function (\App\Instance $instance) use ($projects) {
            $projects->each(function ($code, $name) use ($instance) {
                factory(\Modules\Analytics\Priv\Entities\Project::class)->create([
                    'instance_id' => $instance,
                    'code' => $code,
                    'name' => $name,
                    'slug' => $this->slugGenerator->generate()
                ]);
            });
        });
    }
}
