<?php


use Illuminate\Database\Seeder;

class RequestMileageAllowanceSeeder extends Seeder
{
    use \StartEndDateTrait;

    public function run()
    {
        App\Request::with(['instance.accountingAccounts', 'privateCarTrips'])->get()->each(function (App\Request $request) {
            $trips = collect();
            $cars = $request->privateCarTrips->keyBy('id');

            while ($cars->isNotEmpty()) {

                $car = $cars->random(1)->first();
                $cars->forget($car->id);

                $dates = $this->getElementDates($request->trip_starts, $request->trip_ends);

                $trips->push(factory(App\RequestMileageAllowance::class)->create([
                    'instance_id'           => $request->instance_id,
                    'request_id'            => $request->id,
                    'private_car_trip_id'   => $car->id,
                    'accounting_account_id' => $request->instance->accountingAccounts->random(1)->first()->id,
                    'departure_date' => $dates['start'],
                    'arrival_date' => $dates['end'],
                ]));

            }

            $trips->each(function(App\RequestMileageAllowance $trip) {
                $trip->departureLocation()->save(factory(\App\Location::class)->create([
                    'instance_id' => $trip->instance_id,
                    'column' => 'departure_from',
                    'localizable_id' => $trip->id,
                    'localizable_type' => \App\RequestMileageAllowance::RELATION_NAME,
                ]));


                $trip->destinationLocation()->save(factory(\App\Location::class)->create([
                    'instance_id' => $trip->instance_id,
                    'column' => 'arrival_to',
                    'localizable_id' => $trip->id,
                    'localizable_type' => \App\RequestMileageAllowance::RELATION_NAME,
                ]));
            });
        });
    }
}