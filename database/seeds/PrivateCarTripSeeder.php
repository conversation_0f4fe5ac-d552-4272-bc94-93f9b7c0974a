<?php

use Illuminate\Database\Seeder;

class PrivateCarTripSeeder extends Seeder
{
    use \StartEndDateTrait;

    public function run()
    {
        \App\Request::with(['instance.users'])->get()->each(function (\App\Request $request) {
            $dates = $this->getElementDates($request->trip_starts, $request->trip_ends);

            $trip = factory(\App\PrivateCarTrip::class)->create([
                'instance_id' => $request->instance_id,
                'request_id' => $request,
                'departure_at' => $dates['start'],
                'return_at' => $dates['end'],
            ]);

            $trip->departureLocation()->save(factory(\App\Location::class)->create([
                'instance_id' => $trip->instance_id,
                'column' => 'departure_location',
                'localizable_id' => $trip->id,
                'localizable_type' => \App\PrivateCarTrip::RELATION_NAME,
            ]));


            $trip->destinationLocation()->save(factory(\App\Location::class)->create([
                'instance_id' => $trip->instance_id,
                'column' => 'destination_location',
                'localizable_id' => $trip->id,
                'localizable_type' => \App\PrivateCarTrip::RELATION_NAME,
            ]));
        });
    }
}
