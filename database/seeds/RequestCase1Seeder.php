<?php


class RequestCase1Seeder extends RequestCaseSeeder
{
    protected $request;
    protected static $exchangeRates = [
        '13/02' => 0.4211,
        '14/02' => 0.42,
        '15/02' => 0.4196,
        '16/02' => 0.4196,
        '19/02' => 0.4183,
        '20/02' => 0.4164
    ];
    protected static $locations = [
        'warsaw' => [
            'country' => 'Polska',
            'city' => "Warszawa",
            'province' => 'mazowieckie',
            'address' => 'Nowy Świat',
            'lat' => 52.2304758,
            'long' => 21.0216325,
            'name' => 'warszawa',
            'country_code' => 'PL'
        ],
        'stockholm' => [
            'country' => 'Szwecja',
            'city' => "Sztokholm",
            'province' => 'sztokholm',
            'address' => 'Stockholm',
            'lat' => 59.3367794,
            'long' => 18.0657015,
            'name' => 'warszawa',
            'country_code' => 'SE'
        ],
    ];

    protected static $demo = false;

    public function run()
    {
        foreach(static::$exchangeRates as $day => $rate) {
            $date = explode('/', $day);

            factory(\Modules\ExchangeRate\Priv\Models\ExchangeRateNbp::class)->create([
                'currency_id' => $this->findCurrencyByCode('SEK'),
                'rate' => $rate,
                'effective_date' => \Carbon\Carbon::create(2018, $date[1], $date[0])->startOfDay()
            ]);
        }

        //seed case o kazdym statusie wniosku
        \App\Request::getStatuses()->filter(function($status) {
            return !in_array($status, [\App\Request::STATUS_DELETED, \App\Request::STATUS_TRIP]);
        })->each(function($status, $key) {
            $this->request = factory(\App\Request::class)->create([
                'instance_id' => 1,
                'slug' => 'case1_v'.$key,
                'user_id' => 1,
                'company_id' => 1,
                'mpk_id' => 1,
                'trip_starts' => '2018-02-15 08:00:00',
                'trip_ends' => '2018-02-15 22:00:00',
                'type' => \App\Request::TYPE_TRIP,
                'status' => $status,
            ]);

            $this->createLocation('warsaw', [
                'column' => 'start_location',
                'localizable_id' => $this->request,
                'localizable_type' => \App\Request::RELATION_NAME,
                'additional_data' => 'ZRH'
            ]);

            $this->createLocation('stockholm', [
                'column' => 'end_location',
                'localizable_id' => $this->request,
                'localizable_type' => \App\Request::RELATION_NAME,
                'additional_data' => 'ZRH'
            ]);


            $this->warsawTaxiFromHomeToAirport();
            $borderCrossing = $this->createBorderCrossing('2018-02-15 09:00:00', 'Szwecja');
            $this->trainFromStockholmCityToStockholmAirport();
            $borderCrossing = $this->createBorderCrossing('2018-02-15 18:00:00', 'Polska');
            $this->warsawBus();

            $travelExpenses = factory(App\RequestMealDeduction::class)->create([
                'instance_id' => 1,
                'request_id' => $this->request->id,
                'date' => '2018-02-15 00:00:00',
                'breakfast' => false,
                'lunch' => true,
                'dinner' => false,
                'accomodation' => true,
                'drive' => true
            ]);
        });
    }

    protected function warsawTaxiFromHomeToAirport()
    {
        $taxi =   factory(\App\Cost::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'cost_type' => \App\Cost::TYPE_TAXI,
            'description' => 'Warsaw airport',
            'amount' => 40,
            'amount_currency_id' => $this->findCurrencyByCode('PLN')->id
        ]);

        $type = $this->findElementTypeByName('Taxi');
        $fileinfo = $this->fakeDocumentFile();

        $document = factory(App\Document::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'user_id' => 1,
            'type' => \App\Document::TYPE_ACCOUNTING,
            'provider_id' => null,
            'document_number' => '342554',
            'currency_id' => $this->findCurrencyByCode('PLN')->id,
            'gross' => 40,
            'issue_date' => '2018-02-15 08:00:00',
            'received_date' => '2018-02-15 08:00:00',
            'accounting_date' => '2018-02-15 08:00:00',
            'exchange_rate' => 1,
            'payment' => \App\Document::PAYMENT_TYPE_OWN,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_RECEIPT,
            'name'           => $fileinfo['basename'],
            'file_name'      => $fileinfo['basename'],
            'file_extension' => strtolower($fileinfo['extension']),
        ]);

        factory(\App\Ocr\OcrHint::class, 2)->create([
            'document_id' => $document->id,
            'instance_id' => $document->instance_id
        ]);

        $documentElement = $this->createDocumentElement($type, $document->id, 40);
    }

    protected function trainFromStockholmCityToStockholmAirport()
    {
        $train = factory(App\TrainTrip::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'departure_at' => '2018-02-15 14:30:00',
            'amount' => 280,
            'amount_currency_id' => $this->findCurrencyByCode('SEK')->id,
            'access_to' => false,
            'access_from' => false,
        ]);
        $departureLocation = $this->createLocation('stockholm', [
            'column' => 'departure_location',
            'localizable_id' => $train->id,
            'localizable_type' => \App\TrainTrip::RELATION_NAME
        ]);

        $destinationLocation = $this->createLocation('stockholm', [
            'column' => 'destination_location',
            'localizable_id' => $train->id,
            'localizable_type' => \App\TrainTrip::RELATION_NAME
        ]);

        $fileinfo = $this->fakeDocumentFile();
        $document = factory(App\Document::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'user_id' => 1,
            'type' => \App\Document::TYPE_ACCOUNTING,
            'provider_id' => null,
            'document_number' => 'ARL56823',
            'currency_id' => $this->findCurrencyByCode('SEK'),
            'gross' => 280,
            'issue_date' => '2018-02-15 14:30:00',
            'received_date' => '2018-02-15 14:30:00',
            'accounting_date' => '2018-02-15 14:30:00',
            'exchange_rate' => static::$exchangeRates['14/02'],
            'payment' => \App\Document::PAYMENT_TYPE_OWN,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_INVOICE,
            'name'           => $fileinfo['basename'],
            'file_name'      => $fileinfo['basename'],
            'file_extension' => strtolower($fileinfo['extension']),
        ]);

        factory(\App\Ocr\OcrHint::class, 2)->create([
            'document_id' => $document->id,
            'instance_id' => $document->instance_id
        ]);

        $type = $this->findElementTypeByName('Pociąg');
        $documentElement = $this->createDocumentElement($type, $document->id, 280);
    }

    protected function warsawBus()
    {
        $taxi =   factory(\App\Cost::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'cost_type' => \App\Cost::TYPE_OTHER_BUSINESS_TRIPS,
            'description' => 'Warsaw airport to home',
            'amount' => 3.7,
            'amount_currency_id' => $this->findCurrencyByCode('PLN')->id
        ]);

        $type = $this->findElementTypeByName('Opłaty za przejazdy');
        $document = factory(App\Document::class)->create([
            'instance_id' => 1,
            'request_id' => $this->request->id,
            'user_id' => 1,
            'type' => \App\Document::TYPE_ACCOUNTING,
            'provider_id' => null,
            'document_number' => '2300023',
            'currency_id' => $this->findCurrencyByCode('PLN')->id,
            'gross' => 3.7,
            'issue_date' => '2018-02-15 18:00:00',
            'received_date' => '2018-02-15 18:00:00',
            'accounting_date' => '2018-02-15 18:00:00',
            'exchange_rate' => 1,
            'payment' => \App\Document::PAYMENT_TYPE_OWN,
            'accounting_type' => \App\Document::ACCOUNTING_TYPE_RECEIPT
        ]);

        factory(\App\Ocr\OcrHint::class, 2)->create([
            'document_id' => $document->id,
            'instance_id' => $document->instance_id
        ]);

        $documentElement = $this->createDocumentElement($type, $document->id, 3.7);
    }
}