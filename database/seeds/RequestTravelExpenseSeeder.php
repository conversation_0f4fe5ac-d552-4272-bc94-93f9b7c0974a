<?php

use Illuminate\Database\Seeder;

class RequestTravelExpenseSeeder extends Seeder
{
    use \StartEndDateTrait;

    public function run()
    {
        \App\Request::with([ 'instance' ])->get()->each(function(\App\Request $request) {
            for ($i = 0; $i < rand(1, 5); ++$i) {
                $dates = $this->getElementDates($request->trip_starts, $request->trip_ends);
                factory(\App\RequestMealDeduction::class)->create([
                    'request_id'  => $request,
                    'instance_id' => $request->instance,
                    'date' => $dates['start']
                ]);
            }
        });
    }
}