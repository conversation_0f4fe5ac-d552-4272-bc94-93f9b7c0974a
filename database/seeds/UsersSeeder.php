<?php

use App\Instance;
use App\User;
use Illuminate\Database\Seeder;

class UsersSeeder extends Seeder
{
    public function run()
    {
        switch (App::environment()) {
            case 'local':
                $this->seedLocal();
                break;

            case 'testing':
                $this->seedTesting();
                break;
        }
    }

    protected function seedLocal()
    {
        $instances = Instance::with(['mpks', 'companies', 'groups'])->get();

        $instance = $instances->first();

        $superAdminCompany = $instance->companies->random(1)->first();

        // Super administrator
        $superadmin = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $superAdminCompany,
            'first_name'  => 'superadmin',
            'last_name'   => 'superadmin',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);

        // Administrator instancji
        $admin = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'admin',
            'last_name'   => 'admin',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'is_admin'    => true,
            'user_id'     => $superadmin->id,
            'pin_code' => bcrypt('1111'),
        ]);

        $finance = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $superAdminCompany,
            'first_name'  => 'company',
            'last_name'   => 'company',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $admin->id,
            'pin_code' => bcrypt('1111'),
        ]);

        $regular = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'user',
            'last_name'   => 'user',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);


        $regular2 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'user2',
            'last_name'   => 'user',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);

        //instance 1 - special users
        $manager = factory(App\User::class)->create([
            'id'          => 1000,
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'Jarek',
            'last_name'   => 'Tkaczyk',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);

        $regular3 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'Michał',
            'last_name'   => 'Popielnicki',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $superadmin->id,
            'pin_code' => bcrypt('1111'),
        ]);

        $regular4 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'user',
            'last_name'   => 'sad',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $manager->id,
            'slug'        => 'aa',
            'pin_code' => bcrypt('1111'),
        ]);

        $regular5 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'Adrian',
            'last_name'   => 'Testing',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'slug'        => 'testing',
            'pin_code' => bcrypt('1111'),
        ]);

        $regular6 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'Zosia',
            'last_name'   => 'Gąbek',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'slug'        => 'gosia.z',
            'pin_code' => bcrypt('1111'),
        ]);

        //księgowa
        $finance2 = factory(App\User::class)->create([
            'id'          => 9000,
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'Pani',
            'last_name'   => 'Księgowa',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'slug'        => 'ksiegowa',
            'pin_code' => bcrypt('1111'),
        ]);

        //kontroler
        $control = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'control',
            'last_name'   => 'user',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);

        //agent
        $agent = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'Agent',
            'last_name'   => 'agent',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'is_admin'    => false,
            'slug'        => 'agent',
            'pin_code' => bcrypt('1111'),
        ]);

        //agent
        $integrationAPIUser = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $instance->mpks->random(1)->first(),
            'company_id'  => $instance->companies->random(1)->first(),
            'first_name'  => 'IntegrationAPI',
            'last_name'   => 'User',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'is_admin'    => false,
            'slug'        => 'integration-api-user',
            'locale' => 'en',
            'pin_code' => bcrypt('1111'),
        ]);

        $adminGroup = \App\Group::where(['name' => 'Administrator', 'instance_id' => $instance->id])->first();
        $adminGroup->users()->attach([$admin->id, $superadmin->id]);

        /** @var \App\Company $company */
        foreach ($instance->companies as $key => $company) {
            $aadUser = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id' => $instance->mpks->random(1)->first(),
                'company_id' => $company,
                'first_name' => 'M',
                'last_name' => 'M',
                'email' => $key === 0 ? '<EMAIL>' : '<EMAIL>',
                'pin_code' => bcrypt('1111'),
                'employee_unique_identifier' => '<EMAIL>',
            ]);

            $adminGroup->users()->attach([$aadUser->id]);
        }

        $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $instance->id])->first();
        $regularGroup->users()->attach([$regular->id, $regular2->id, $regular3->id, $regular4->id, $regular5->id, $regular6->id]);

        $financeGroup = \App\Group::where(['name' => 'Finance', 'instance_id' => $instance->id])->first();
        $financeGroup->users()->attach([$finance->id, $finance2->id]);

        $managerGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_APPROVER, 'instance_id' => $instance->id])->first();
        $managerGroup->users()->attach($manager->id);

        $controlGroup = \App\Group::where(['name' => 'Control', 'instance_id' => $instance->id])->first();
        $controlGroup->users()->attach([$control->id]);

        $agentGroup = \App\Group::where(['name' => 'Agent', 'instance_id' => $instance->id])->first();
        $agentGroup->users()->attach([$agent->id]);

        $integrationAPIGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_INTEGRATION_API, 'instance_id' => $instance->id])->first();
        $integrationAPIGroup->users()->attach([$integrationAPIUser->id]);

        //fake active users
        $company = \App\Company::where('code', 'like', '222')->first();

        $users = factory(\App\User::class, 100)->create([
            'instance_id' => $company->instance_id,
            'company_id' => $company->id,
            'user_id' => null,
            'pin_code' => bcrypt('1111'),
        ]);

        $users = $users->shuffle();

        $managers = $users->slice(0, 25);
        $finances = $users->slice(25, 25);
        $regulars = $users->slice(50, 25);
        $admins   = $users->slice(75, 25);

        $managerGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_APPROVER, 'instance_id' => $company->instance_id])->first();
        $managerGroup->users()->attach($managers->pluck('id')->toArray());

        $financeGroup = \App\Group::where(['name' => 'Finance', 'instance_id' => $company->instance_id])->first();
        $financeGroup->users()->attach($finances->pluck('id')->toArray());

        $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $company->instance_id])->first();
        $regularGroup->users()->attach($regulars->pluck('id')->toArray());

        $adminGroup = \App\Group::where(['name' => 'Administrator', 'instance_id' => $company->instance_id])->first();
        $adminGroup->users()->attach($admins->pluck('id')->toArray());

//@mindento.com
        $mpk = \Modules\Accounting\Priv\Entities\Mpk::where(['name' => 'Produkcja Warszawa', 'instance_id' => $company->instance_id])->first();
        $company = \App\Company::where('code','like', '222')->first();

        $mindentoManager01 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Manager 1',
            'last_name'   => 'Manager 1',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);
        $managerGroup->users()->attach($mindentoManager01);

        $mindentoManager02 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Manager 2',
            'last_name'   => 'Manager 2',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);
        $managerGroup->users()->attach($mindentoManager02);

        $mindentoRegular01 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Regular 1',
            'last_name'   => 'Regular 1',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $mindentoManager01->id,
            'pin_code' => bcrypt('1111'),
        ]);
        $regularGroup->users()->attach($mindentoRegular01);

        $mindentoRegular02 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Regular 2',
            'last_name'   => 'Regular 2',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $mindentoManager02->id,
            'pin_code' => bcrypt('1111'),
        ]);
        $regularGroup->users()->attach($mindentoRegular02);

        $mindentoFinance01 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Finance 1',
            'last_name'   => 'Finance 1',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);
        $financeGroup->users()->attach($mindentoFinance01);


        $mindentoFinance02 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Finance 2',
            'last_name'   => 'Finance 2',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $mindentoFinance01->user_id,
            'pin_code' => bcrypt('1111'),
        ]);
        $financeGroup->users()->attach($mindentoFinance02);

        $mindentoMain01 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Main 1',
            'last_name'   => 'Main 1',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);
        $adminGroup->users()->attach($mindentoMain01);

        $mindentoMain02 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Main 2',
            'last_name'   => 'Main 2',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'pin_code' => bcrypt('1111'),
        ]);
        $adminGroup->users()->attach($mindentoMain02);

        $mindentoController01 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Controller 1',
            'last_name'   => 'Controller 1',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $mindentoMain01->id,
            'pin_code' => bcrypt('1111'),
        ]);
        $controlGroup->users()->attach($mindentoController01);

        $mindentoController02 = factory(App\User::class)->create([
            'instance_id' => $instance->id,
            'mpk_id'      => $mpk->id,
            'company_id'  => $company->id,
            'first_name'  => 'Controller 2',
            'last_name'   => 'Controller 2',
            'email'       => '<EMAIL>',
            'employee_unique_identifier'       => '<EMAIL>',
            'user_id'     => $mindentoMain02->id,
            'pin_code' => bcrypt('1111'),
        ]);
        $controlGroup->users()->attach($mindentoController02);
    }

    protected function seedTesting()
    {
        factory(User::class, \Tests\SessionTrait::$USER_SLUG)->create();

        Instance::with(['mpks', 'companies', 'groups'])->each(function (Instance $instance) {
            $superAdminCompany = $instance->companies->random(1)->first();
            $superadmin = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $superAdminCompany,
                'first_name'  => 'superadmin',
                'last_name'   => 'superadmin',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'superadmin' : ($instance->id . '-superadmin'),
                'pin_code' => bcrypt('1111'),
            ]);

            $admin = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $instance->companies->random(1)->first(),
                'first_name'  => 'admin',
                'last_name'   => 'admin',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'is_admin'    => true,
                'user_id'     => $superadmin->id,
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'admin' : ($instance->id . '-admin'),
                'pin_code' => bcrypt('1111'),
            ]);

            $finance = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $superAdminCompany,
                'first_name'  => 'finance',
                'last_name'   => 'finance',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'user_id'     => $admin->id,
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'finance' : ($instance->id . '-finance'),
                'pin_code' => bcrypt('1111'),
            ]);

            $regular = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $instance->companies->random(1)->first(),
                'first_name'  => 'user',
                'last_name'   => 'user',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'user' : ($instance->id . '-user'),
                'pin_code' => bcrypt('1111'),
            ]);

            $manager = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $instance->companies->random(1)->first(),
                'first_name'  => 'manager',
                'last_name'   => 'manager',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'manager' : ($instance->id . '-manager'),
                'pin_code' => bcrypt('1111'),
            ]);

            //kontroler
            $control = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $instance->companies->random(1)->first(),
                'first_name'  => 'control',
                'last_name'   => 'control',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'control' : ($instance->id . '-control'),
                'pin_code' => bcrypt('1111'),
            ]);

            //agent
            $agent = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $instance->companies->random(1)->first(),
                'first_name'  => 'agent',
                'last_name'   => 'agent',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'is_admin'    => false,
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'agent' : ($instance->id . '-agent'),
                'pin_code' => bcrypt('1111'),
            ]);

            //agent
            $integrationAPIUser = factory(App\User::class)->create([
                'instance_id' => $instance->id,
                'mpk_id'      => $instance->mpks->random(1)->first(),
                'company_id'  => $instance->companies->random(1)->first(),
                'first_name'  => 'IntegrationAPI',
                'last_name'   => 'User',
                'email'       => $instance->domain === 'mindento-web.testing' ? '<EMAIL>' : ($instance->id . '-<EMAIL>'),
                'is_admin'    => false,
                'slug'        => $instance->domain === 'mindento-web.testing' ? 'integration-api-user' : ($instance->id . '-integration-api-user'),
                'locale'      => 'en',
                'pin_code' => bcrypt('1111'),
            ]);

            $adminGroup = \App\Group::where(['name' => 'Administrator', 'instance_id' => $instance->id])->first();
            $adminGroup->users()->attach([$admin->id, $superadmin->id]);

            $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $instance->id])->first();
            $regularGroup->users()->attach([$regular->id]);

            $financeGroup = \App\Group::where(['name' => 'Finance', 'instance_id' => $instance->id])->first();
            $financeGroup->users()->attach([$finance->id]);

            $managerGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_APPROVER, 'instance_id' => $instance->id])->first();
            $managerGroup->users()->attach($manager->id);

            $controlGroup = \App\Group::where(['name' => 'Control', 'instance_id' => $instance->id])->first();
            $controlGroup->users()->attach([$control->id]);

            $agentGroup = \App\Group::where(['name' => 'Agent', 'instance_id' => $instance->id])->first();
            $agentGroup->users()->attach([$agent->id]);

            $integrationAPIGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_INTEGRATION_API, 'instance_id' => $instance->id])->first();
            $integrationAPIGroup->users()->attach([$integrationAPIUser->id]);
        });
    }
}
