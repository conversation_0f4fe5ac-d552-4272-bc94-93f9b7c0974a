<?php

use Illuminate\Database\Seeder;

class CommentsSeeder extends Seeder {
    public function run() {
        \App\Request::with(['instance.users'])->get()->each(function(\App\Request $request) {
            factory(App\Comment::class, 4)->create([
                'request_id'  => $request,
                'instance_id' => $request->instance_id,
                'user_id'     => $request->instance->users->random(1)->first(),
            ]);
        });

        factory(App\Comment::class, 16)->create();
    }
}
