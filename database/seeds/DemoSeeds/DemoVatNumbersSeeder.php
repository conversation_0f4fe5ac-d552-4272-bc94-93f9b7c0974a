<?php


class DemoVatNumbersSeeder extends VatNumbersSeeder
{

    protected $vatNumbers;
    public function __construct()
    {
        $this->vatNumbers = collect(config('vaterval.vatNumbers'));
        $this->instancesCollection = \App\Instance::with('accountingAccounts')
            ->whereIn('domain', [
                'demo.mindento.com',
                'merigo.mindento.com',
                'mindento.mindento.com'
            ])->get();
    }
}
