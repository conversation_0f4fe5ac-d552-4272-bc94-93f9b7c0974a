<?php

class DemoUserTestingInstanceSeeds extends \Illuminate\Database\Seeder
{
    public function run()
    {
        //MINDENTO
        $mindentoInstance = \App\Instance::with(['mpks', 'companies', 'groups'])->where(['domain' => 'mindento.mindento.com'])->first();
        $mindentoCompany = $mindentoInstance->companies->first();

        $adminMindento = factory(App\User::class)->create([
            'instance_id' => $mindentoInstance->id,
            'mpk_id'      => $mindentoInstance->mpks->random(1)->first(),
            'company_id'  => $mindentoCompany,
            'first_name'  => 'admin',
            'last_name'   => 'admin',
            'email'       => '<EMAIL>',
            'is_admin'    => true,
            'user_id'     => null,
        ]);

        $testMindento = factory(App\User::class)->create([
            'instance_id' => $mindentoInstance->id,
            'mpk_id'      => $mindentoInstance->mpks->random(1)->first(),
            'company_id'  => $mindentoCompany,
            'first_name'  => 'Adrian',
            'last_name'   => 'Testing',
            'email'       => '<EMAIL>',
            'is_admin'    => true,
            'user_id'     => null,
        ]);

        $financeMindento = factory(App\User::class)->create([
            'instance_id' => $mindentoInstance->id,
            'mpk_id'      => $mindentoInstance->mpks->random(1)->first(),
            'company_id'  => $mindentoCompany,
            'first_name'  => 'Księgowa',
            'last_name'   => 'Księgowa',
            'email'       => '<EMAIL>',
            'phone'       => '+48123456789',
            'avatar'      => "//". config('vaterval.demoDomain').'/uploads/avatar_ksiegowa.jpg',
            'user_id'     => null,
            'slug'        => 'ksiegowa_mindento'
        ]);

        $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $mindentoInstance->id])->first();
        $regularGroup->users()->attach([
            $adminMindento->id,
            $testMindento->id,
        ]);

        $financeGroup = \App\Group::where(['name' => 'Finance', 'instance_id' => $mindentoInstance->id])->first();
        $financeGroup->users()->attach([
            $financeMindento->id,
        ]);

        //MERIGO
        $merigoInstance = \App\Instance::with(['mpks', 'companies', 'groups'])->where(['domain' => 'merigo.mindento.com'])->first();
        $merigoCompany = $merigoInstance->companies->first();

        $adminMerigo = factory(App\User::class)->create([
            'instance_id' => $merigoInstance->id,
            'mpk_id'      => $merigoInstance->mpks->random(1)->first(),
            'company_id'  => $merigoCompany,
            'first_name'  => 'admin',
            'last_name'   => 'admin',
            'email'       => '<EMAIL>',
            'is_admin'    => true,
            'user_id'     => null,
        ]);

        $testMerigo = factory(App\User::class)->create([
            'instance_id' => $merigoInstance->id,
            'mpk_id'      => $merigoInstance->mpks->random(1)->first(),
            'company_id'  => $merigoCompany,
            'first_name'  => 'User',
            'last_name'   => 'Testing',
            'email'       => '<EMAIL>',
            'is_admin'    => true,
            'user_id'     => null,
        ]);

        $financeMerigo = factory(App\User::class)->create([
            'instance_id' => $merigoInstance->id,
            'mpk_id'      => $merigoInstance->mpks->random(1)->first(),
            'company_id'  => $merigoCompany,
            'first_name'  => 'Księgowa',
            'last_name'   => 'Księgowa',
            'email'       => '<EMAIL>',
            'phone'       => '+48123456789',
            'avatar'      => "//". config('vaterval.demoDomain').'/uploads/avatar_ksiegowa.jpg',
            'user_id'     => null,
            'slug'        => 'merigo'
        ]);

        $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $merigoInstance->id])->first();
        $regularGroup->users()->attach([
            $adminMerigo->id,
            $testMerigo->id,
        ]);

        $financeGroup = \App\Group::where(['name' => 'Finance', 'instance_id' => $merigoInstance->id])->first();
        $financeGroup->users()->attach([
            $financeMerigo->id,
        ]);

    }
}