<?php


class DemoDocumentElementGroupSeeder extends DocumentElementGroupSeeder
{
    public function __construct()
    {
        $this->groupNames = collect(config('vaterval.document_element_group_names'));

        $this->instancesCollection = \App\Instance::with('documentElementTypes')
            ->whereIn('domain', [
            'demo.mindento.com',
            'merigo.mindento.com',
            'mindento.mindento.com'
        ])->get();
    }
}