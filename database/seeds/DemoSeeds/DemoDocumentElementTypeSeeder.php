<?php


class DemoDocumentElementTypeSeeder extends DocumentElementTypeSeeder
{
    public function __construct()
    {
        $this->types = collect(config('vaterval.document_element_types'));

        $this->instancesCollection = \App\Instance::with(['documentElementGroups', 'vatNumbers', 'accountingAccounts'])
        ->whereIn('domain', [
            'demo.mindento.com',
            'merigo.mindento.com',
            'mindento.mindento.com'
        ])->get();
    }
}