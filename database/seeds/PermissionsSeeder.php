<?php

use Illuminate\Database\Seeder;

class PermissionsSeeder extends Seeder
{
    protected $groupsCollection;

    /**
     * PermissionsSeeder constructor.
     */
    public function __construct()
    {
        $this->groupsCollection = \App\Group::all();
    }

    public function run()
    {
        $permissionsSeed = collect(config('permissions.default_permissions'));

        $this->groupsCollection->each(function (\App\Group $group) use ($permissionsSeed) {
            $permissions = collect($permissionsSeed->get($group->name));
            $groupId = $group->id;
            $instanceId = $group->instance_id;

            $permissions->each(function ($value, $key) use ($groupId, $instanceId) {
                factory(App\Permission::class)->create([
                    'ability' => $key,
                    'group_id' => $groupId,
                    'can' => $value,
                    'instance_id' => $instanceId
                ]);
            });
        });
    }
}
