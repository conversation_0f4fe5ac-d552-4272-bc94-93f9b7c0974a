<?php

use Illuminate\Database\Seeder;

class InstallmentsSeeder extends Seeder
{
    public function run()
    {
        $requests = \App\Request::orderBy('id', 'asc')->get();
        factory(App\Installment::class, 4)->create([
            'request_id' => $requests->first()
        ]);

        $requests->each(function (\App\Request $request) {
            factory(App\Installment::class, rand(0, 4))->create([
                'instance_id' => $request->instance_id,
                'request_id'  => $request
            ]);
        });
    }
}