<?php

class NetkataSeeder extends DatabaseSeeder
{
    protected $instance;
    protected $company;
    protected $mpks;

    public function run()
    {
        try {

            $this->flushEventListeners();
            $this->mpks = collect();

            $this->call(CurrenciesSeeder::class);
            $this->call(ExchangeRateProviderSeeder::class);
            $this->call(CountriesSeeder::class);
            $this->seedInstance();
            $this->call(AccountingAccountsSeeder::class);
            $this->call(VatNumbersSeeder::class);
            $this->seedCompany($this->instance);
            $this->seedMpks($this->instance);
            $this->call(GroupsSeeder::class);
            $this->seedUsers($this->instance);
//        $this->call(UsersSeeder::class);
            $this->call(PermissionsSeeder::class);
            $this->call(RuleSeeder::class);
            $this->call(KPISeeder::class);
            $this->call(TravelExpensesSeeder::class);
            $this->call(AccommodationLimitsSeeder::class);
            $this->call(ProvidersSeeder::class);
            $this->call(DocumentElementGroupSeeder::class);
            $this->call(DocumentElementTypeSeeder::class);
        } catch (Throwable $e) {
            dd($e);
        }


    }

    public function seedInstance()
    {
        $currency = \App\Currency::where(['code' => 'PLN'])->first();
        $country = \App\Country::where(['country_code' => 'PL'])->first();

        $this->instance = factory(App\Instance::class)->create([
            'domain' => 'netkata.vtl.netkata.io',
            'name' => 'Netkata sp. z o.o.',
            'currency_id' => $currency,
            'post_code' => '00-496',
            'nip' => '701 069 49 47',
            'country_id' => $country,
            'trip_agent' => 'Katarzyna Skrzypek <br /> <EMAIL> <br /> 784 65 82 99',
            'modules' => [
                'compliance' => [
                    'name' => 'amrest'
                ]
            ]
        ]);
        $this->createInstanceLocation($this->instance);
    }

    protected function createInstanceLocation($instance)
    {
        factory(\App\Location::class)->create([
            'country' => 'Polska',
            'country_code' => 'PL',
            'city' => "Warszawa",
            'province' => 'mazowieckie',
            'address' => 'Nowy Świat 5/11',
            'lat' => 52.2304758,
            'long' => 21.0216325,
            'name' => 'netkata',
            'formatted_address' => 'Nowy Świat 5/11, 00-496 Warszawa',
            'instance_id' => $instance->id,
            'column' => 'location',
            'localizable_id' => $instance->id,
            'localizable_type' => \App\Instance::RELATION_NAME,
        ]);
    }

    protected function seedCompany($instance)
    {
        $this->company = factory(\App\Company::class)->create([
            'created_at'  => \Carbon\Carbon::now(),
            'instance_id' => $instance->id,
            'name'        => 'Netkata sp. z o.o.',
            'nip'         => '701 069 49 47',
            'code'        => '1',
        ]);
    }

    protected function seedMpks($instance)
    {
        $mpks = [
            [
                'instance_id' => $instance->id,
                'code' => '1',
                'name' => 'Projekty',
            ],
            [
                'instance_id' => $instance->id,
                'code' => '2',
                'name' => 'Frontkom',
            ],
            [
                'instance_id' => $instance->id,
                'code' => '3',
                'name' => 'Konferencje',
            ],
            [
                'instance_id' => $instance->id,
                'code' => '4',
                'name' => 'Sales',
            ],
            [
                'instance_id' => $instance->id,
                'code' => '5',
                'name' => 'Inne',
            ],
        ];

        $slugGenerator = resolve(\App\Services\SlugGeneratorService::class);
        foreach ($mpks as $mpk) {
            $mpks['slug'] = $slugGenerator->generate();
            $this->mpks->push(factory(\Modules\Accounting\Priv\Entities\Mpk::class)->create($mpk));
        }
    }

    protected function seedUsers($instance)
    {
        $michal = $this->seedUser('Michał', 'Popielnicki', '<EMAIL>', 1);
        $tom = $this->seedUser('Tomasz', 'Sieroń', '<EMAIL>', 4);
        $bartek = $this->seedUser('Bartek', 'Mitura', '<EMAIL>', 1, $michal->id);
        $ola = $this->seedUser('Aleksandra', 'Wróblewska', '<EMAIL>', 1, $michal->id);
        $damian = $this->seedUser('Damian', 'Domański', '<EMAIL>', 1, $michal->id);
        $monika = $this->seedUser('Monika', 'Kleczewska', '<EMAIL>', 5);
        $krzysiek = $this->seedUser('Krzysztof', 'Bezrąk', '<EMAIL>', 1, $michal->id);
        $kacper = $this->seedUser('Kacper', 'Rzosiński', '<EMAIL>', 1, $tom->id);

        $adminGroup = \App\Group::where(['name' => 'Administrator', 'instance_id' => $instance->id])->first();
        $adminGroup->users()->attach([$michal->id, $tom->id]);

        $regularGroup = \App\Group::where(['name' => 'Regular', 'instance_id' => $instance->id])->first();
        $regularGroup->users()->attach([$krzysiek->id, $kacper->id]);

        $financeGroup = \App\Group::where(['name' => 'Finance', 'instance_id' => $instance->id])->first();
        $financeGroup->users()->attach([$monika->id]);

        $managerGroup = \App\Group::where(['name' => \App\User::GROUP_NAME_APPROVER, 'instance_id' => $instance->id])->first();
        $managerGroup->users()->attach([$bartek->id, $ola->id, $damian->id]);

    }

    protected function seedUser($firstName, $lastName, $email, $mpkcode, $supervisor=null)
    {
        return factory(App\User::class)->create([
            'instance_id' => $this->instance->id,
            'mpk_id'      => $this->findMpk($mpkcode),
            'company_id'  => $this->company,
            'first_name'  => $firstName,
            'last_name'   => $lastName,
            'email'       => $email,
            'user_id'     => $supervisor,
            'slug'        => str_slug($firstName.' '.$lastName),
        ]);
    }

    protected function findMpk($code)
    {
        return $this->mpks->filter(function($mpk) use(&$code){
            return $mpk->code == $code;
        })->first();
    }
}