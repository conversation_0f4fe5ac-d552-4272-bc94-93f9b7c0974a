<?php

use Illuminate\Database\Seeder;

class AccomodationSeeder extends Seeder
{
    use \StartEndDateTrait;

    public function run()
    {
        \App\Request::with(['instance.users'])->get()->each(function (\App\Request $request) {
            $dates = $this->getElementDates($request->trip_starts, $request->trip_ends);

            $trip = factory(\App\Accomodation::class)->create([
                'instance_id' => $request->instance_id,
                'request_id' => $request,
                'arrival_at' => $dates['start'],
                'departure_at' => $dates['end'],
            ]);

            $trip->location()->save(factory(\App\Location::class)->create([
                'instance_id' => $trip->instance_id,
                'column' => 'location',
                'localizable_id' => $trip->id,
                'localizable_type' => \App\Accomodation::RELATION_NAME,
            ]));
        });
    }
}
