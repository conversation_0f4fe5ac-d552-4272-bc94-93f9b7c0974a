<?php

declare(strict_types=1);

class FeatureSettingsSeeder extends \Illuminate\Database\Seeder
{
    public function run()
    {

        //todo @ernest zamienić na foreach z arrayem w środku
        switch (App::environment()) {
            case 'testing':
                $instance = \App\Instance::where(['domain' => 'mindento-web.testing'])->first();
                $this->seedTesting($instance);
                $instance = \App\Instance::where(['domain' => '1-mindento-web.testing'])->first();
                $this->seedTesting($instance);
                break;
        }
    }

    public function seedTesting(\App\Instance $instance): void
    {
        /** @var \Modules\FeatureSwitcher\Priv\Services\FeatureService $service */
        $service = resolve(\Modules\FeatureSwitcher\Priv\Services\FeatureService::class);

        $feature = \Modules\FeatureSwitcher\Priv\Entities\Feature::where(['slug' => \Modules\FeatureSwitcher\Pub\Enums\FeatureEnum::FEATURE_INTEGRATION_MODE()])->first();

        $service->changeConfiguration($feature, $instance, \Modules\FeatureSwitcher\Pub\Enums\IntegrationModeEnum::API());

        $feature = \Modules\FeatureSwitcher\Priv\Entities\Feature::where(['slug' => \Modules\FeatureSwitcher\Pub\Enums\FeatureEnum::FEATURE_INTEGRATION_API_ERP_ACCOUNTING_ENABLED()])->first();
        $service->changeConfiguration($feature, $instance, 1);

    }

}