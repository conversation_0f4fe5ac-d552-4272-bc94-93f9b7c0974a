<?php

use Illuminate\Database\Seeder;

class RequestBorderCrossingSeeder extends Seeder
{
    use \StartEndDateTrait;

    public function run()
    {
        $requests = \App\Request::with([ 'instance' ])->get();

        $firstRequest = \App\Request::where('slug', 'aa')->first();


        for ($i = 0; $i < rand(2, 5); ++$i) {
            $dates = $this->getElementDates($firstRequest->trip_starts, $firstRequest->trip_ends);

            factory(\App\RequestBorderCrossing::class)->create([
                'request_id'  => $firstRequest,
                'instance_id' => $firstRequest->instance,
                'date' => $dates['start'],
            ]);
        }

        $requests->where('slug', '!=', 'aa')->each(function(\App\Request $request) {
            for($i=0; $i < rand(0, 5); ++$i) {
                $dates = $this->getElementDates($request->trip_starts, $request->trip_ends);

                factory(\App\RequestBorderCrossing::class)->create([
                    'request_id'  => $request,
                    'instance_id' => $request->instance,
                    'date' => $dates['start'],
                ]);
            }
        });
    }
}