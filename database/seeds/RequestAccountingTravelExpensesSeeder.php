<?php


class RequestAccountingTravelExpensesSeeder extends \Illuminate\Database\Seeder
{
    public function run()
    {
        \App\Request::with('instance')->get()->each(function(\App\Request $request) {
            if(rand(0, 1)) {
                factory(\App\RequestAccountingTravelExpenses::class, rand(0,2))->create([
                    'request_id' => $request->id,
                    'instance_id' => $request->instance_id,
                    'accounting_account_id' => $request->instance->accounting_account_id,
                    'mpk_id' => $request->mpk_id,
                ]);
            }
        });
    }
}