<?php

// Test walidacji wzorcem z Request'ów
require_once 'vendor/autoload.php';

echo "Test walidacji wzorcem z Request'ów\n";
echo "===================================\n";

// Test 1: Sprawdzenie czy używamy RequestHasAccountingDimensionsRule
$factorySource = file_get_contents('modules/Users/<USER>/Factory/NewUserDtoFactory.php');
if (strpos($factorySource, 'RequestHasAccountingDimensionsRule') !== false) {
    echo "✓ Używamy RequestHasAccountingDimensionsRule (wzorzec z Request'ów)\n";
} else {
    echo "✗ Nie używamy RequestHasAccountingDimensionsRule\n";
}

// Test 2: Sprawdzenie czy używamy wzorca account-dimensions.*
if (strpos($factorySource, "'account-dimensions.*'") !== false) {
    echo "✓ Używamy wzorca 'account-dimensions.*'\n";
} else {
    echo "✗ Nie używamy wzorca 'account-dimensions.*'\n";
}

// Test 3: Sprawdzenie czy przekształcamy dane na format account-dimensions.{slug}
if (strpos($factorySource, "'account-dimensions.' . \$slug") !== false) {
    echo "✓ Przekształcamy dane na format 'account-dimensions.{slug}'\n";
} else {
    echo "✗ Nie przekształcamy danych na format 'account-dimensions.{slug}'\n";
}

// Test 4: Sprawdzenie czy usunęliśmy niepotrzebną klasę
if (!file_exists('modules/Users/<USER>/Rules/RequiredAccountDimensionItemsRule.php')) {
    echo "✓ Usunęliśmy niepotrzebną klasę RequiredAccountDimensionItemsRule\n";
} else {
    echo "✗ Niepotrzebna klasa RequiredAccountDimensionItemsRule nadal istnieje\n";
}

// Test 5: Sprawdzenie czy RequestHasAccountingDimensionsRule istnieje
if (file_exists('app/Services/RulesService/Rules/RequestHasAccountingDimensionsRule.php')) {
    echo "✓ RequestHasAccountingDimensionsRule istnieje\n";
} else {
    echo "✗ RequestHasAccountingDimensionsRule nie istnieje\n";
}

echo "\n=== PODSUMOWANIE ===\n";
echo "🎯 Implementacja używa wzorca z Request'ów!\n\n";
echo "Jak to teraz działa:\n";
echo "1. Frontend wysyła: account_dimension_items: [{ account_dimension_slug: 'dept', account_dimension_item_id: 123 }]\n";
echo "2. Backend przekształca na: account-dimensions.dept = 123\n";
echo "3. Walidacja sprawdza każdy wymiar osobno używając RequestHasAccountingDimensionsRule\n";
echo "4. Jeśli wymiar jest wymagany i nie został podany, zwraca błąd dla pola 'account-dimensions.dept'\n";
echo "5. Frontend powinien wyświetlić błąd przy konkretnym polu wymiaru\n\n";
echo "To jest dokładnie ten sam wzorzec co w Request'ach!\n";
echo "Błędy będą zwracane dla konkretnych pól: account-dimensions.{slug}\n";
