{"private": true, "name": "vaterval", "version": "0.0.1", "description": "", "scripts": {"build": "node internals/scripts/build.js", "coveralls": "cat ./coverage/lcov.info | coveralls", "clear": "rimraf public/build", "dev": "npm run development", "development": "cross-env NODE_ENV=development npm run build", "e2e:run": "cypress run", "e2e:report": "node ./cypress/scripts/report.js", "front:start": "npm start", "front:build": "npm run production", "format": "prettier --write 'resources/assets/js/**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "format:check": "prettier --check 'resources/assets/js/**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc", "lint": "eslint 'resources/assets/js/**/*.{js,jsx,ts,tsx}' --fix", "lint:check": "eslint 'resources/assets/js/**/*.{js,jsx,ts,tsx}'", "lint-format": "npm run lint && npm run format", "lint-format:check": "npm run lint:check && npm run format:check", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "pretest": "npm run test:clean", "production": "cross-env NODE_ENV=production npm run build", "start": "cross-env NODE_ENV=development node internals/scripts/start.js", "test": "node internals/scripts/test.js --watchAll=false", "test:clean": "rimraf ./coverage", "test:coverage": "npm run test -- --coverage", "test:ci": "npm run test", "webpack": "node --max-old-space-size=8192 node_modules/webpack/bin/webpack.js", "watch": "npm start"}, "devDependencies": {"@babel/core": "7.16.12", "@babel/plugin-proposal-private-property-in-object": "^7.18.6", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.8", "@sentry/webpack-plugin": "^2.7.1", "@svgr/webpack": "^6.5.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.1", "@types/node": "^18.16.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.1", "@types/react-redux": "^7.1.25", "@types/react-router-dom": "5", "babel-jest": "^29.2.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "compression-webpack-plugin": "10.0.0", "coveralls": "3.0.3", "cross-env": "^5.1.6", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^4.2.2", "cypress": "^9.4.1", "cypress-cucumber-preprocessor": "^4.3.1", "cypress-dotenv": "^2.0.0", "dotenv": "^16.0.3", "dotenv-expand": "^9.0.0", "eslint": "^8.26.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-preferred-import": "^1.0.13", "eslint-plugin-unused-imports": "^4.1.4", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "flush-promises": "^1.0.2", "fs-extra": "^10.1.0", "html-webpack-harddisk-plugin": "^2.0.0", "html-webpack-plugin": "^5.5.0", "http-proxy-middleware": "^2.0.6", "identity-obj-proxy": "^3.0.0", "jest": "^29.2.2", "jest-environment-jsdom": "^29.2.2", "jest-resolve": "^29.2.2", "jest-watch-typeahead": "^2.2.0", "jsdom-worker": "^0.3.0", "mini-css-extract-plugin": "^2.6.1", "msw": "^0.49.2", "multiple-cucumber-html-reporter": "^3.0.1", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^7.0.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.8.2", "prettier": "^3.0.1", "progress-webpack-plugin": "^1.0.16", "prompts": "^2.4.2", "react-dev-utils": "^12.0.1", "react-refresh": "^0.14.0", "redux-immutable-state-invariant": "^2.1.0", "redux-logger": "^3.0.6", "resolve": "^1.20.0", "resolve-url-loader": "^5.0.0", "rimraf": "^3.0.2", "sass": "^1.55.0", "sass-loader": "^13.1.0", "semver": "^7.3.5", "source-map-loader": "^4.0.1", "style-loader": "^3.3.1", "terser-webpack-plugin": "^5.3.6", "ts-loader": "^9.4.2", "typescript": "^5.0.4", "url-loader": "^4.1.1", "webpack": "^5.74.0", "webpack-dev-server": "^4.11.0", "webpack-manifest-plugin": "^5.0.0", "workbox-webpack-plugin": "^6.4.1", "worker-loader": "^3.0.8"}, "dependencies": {"@material-ui/core": "^3.4.0", "@react-google-maps/api": "^2.20.5", "@react-input/mask": "^2.0.4", "@reduxjs/toolkit": "^1.9.5", "@sentry/react": "^7.68.0", "@vis.gl/react-google-maps": "^1.5.1", "ag-grid-community": "^32.0.2", "ag-grid-react": "^32.0.2", "array-move": "^1.0.0", "axios": "^0.16.2", "axios-cache-adapter": "github:RasCarlito/axios-cache-adapter#de3ddd88aabaccbfea57fb6f44ef14e032393325", "browser-locale": "^1.0.3", "classnames": "^2.2.5", "connected-react-router": "^6.9.3", "cypress-real-events": "^1.7.0", "d3": "^5.7.0", "deep-object-diff": "^1.1.0", "echarts": "^4.2.0-rc.2", "echarts-for-react": "^2.0.15-beta.0", "error-stack-parser": "^2.0.2", "formik": "^1.5.0", "history": "^4.7.2", "hoist-non-react-statics": "^2.3.1", "html-react-parser": "^3.0.4", "immutable": "^4.3.0", "invariant": "^2.2.4", "is-promise": "^4.0.0", "jquery": "^3.3.1", "laravel-echo": "^1.5.2", "lightbox-react": "^0.3.7", "lodash": "^4.17.4", "md5": "^2.2.1", "memoize-one": "^4.0.2", "mime": "2.6.0", "moment": "^2.29.4", "numeral": "^2.0.6", "object-diff": "0.0.4", "path-to-regexp": "^3.1.0", "pluralize": "^7.0.0", "prop-types": "^15.6.2", "query-string": "^6.5.0", "react": "^18.2.0", "react-addons-css-transition-group": "^15.6.2", "react-animate-height": "^3.0.4", "react-app-polyfill": "^3.0.0", "react-autocomplete": "^1.8.1", "react-autosize-textarea": "^3.0.2", "react-autosuggest": "^9.3.4", "react-beautiful-dnd": "^9.0.2", "react-collapse": "^4.0.3", "react-css-modules": "^4.7.3", "react-custom-scrollbars-2": "^4.4.0", "react-datepicker": "^3.8.0", "react-dnd": "^5.0.0", "react-dom": "^18.2.0", "react-dropzone": "^4.2.11", "react-ellipsis-pjs": "^0.0.9", "react-google-maps": "^9.4.5", "react-helmet": "^5.2.0", "react-iframe": "^1.1.0", "react-in-viewport": "^0.0.31", "react-input-range": "^1.3.0", "react-loadable": "^5.4.0", "react-loaders": "^3.0.1", "react-motion": "^0.5.2", "react-number-format": "^5.2.2", "react-places-autocomplete": "^7.1.2", "react-redux": "^8.0.4", "react-redux-notify": "^2.0.0", "react-responsive-carousel": "^3.1.43", "react-router-dom": "^5.3.4", "react-select": "^5.7.4", "react-select-async-paginate": "^0.7.2", "react-slick": "^0.23.2", "react-tabs": "^2.2.1", "react-tippy": "^1.2.2", "react-toastify": "^9.1.2", "react-transition-group": "^2.4.0", "react-trigger-change": "^1.0.2", "react-truncate-markup": "^5.0.1", "react-virtualized": "^9.22.3", "redux": "^4.2.1", "redux-form": "^7.4.3", "redux-immutable": "^4.0.0", "redux-thunk": "^2.4.2", "reselect": "^4.1.8", "slick-carousel": "^1.8.1", "store": "^2.0.12"}, "cypress-cucumber-preprocessor": {"nonGlobalStepDefinitions": true, "commonPath": "cypress/integration/common", "cucumberJson": {"generate": true, "outputFolder": "cypress/cucumber-json", "filePrefix": "", "fileSuffix": ".cucumber"}}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/resources/assets/js"], "collectCoverageFrom": ["resources/assets/js/**/*.{js,jsx,ts,tsx}", "!resources/assets/js/**/*.d.ts", "!resources/assets/js/**/*.snap.js"], "coverageDirectory": "<rootDir>/coverage", "coverageReporters": ["text-summary", "html"], "setupFiles": ["jsdom-worker", "react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/internals/setupTests.js"], "snapshotResolver": "<rootDir>/internals/config/jest/snapshotResolver.js", "testMatch": ["<rootDir>/resources/assets/js/**/__tests__/**/*.{test,spec}.{js,jsx,ts,tsx}", "<rootDir>/resources/assets/js/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/internals/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/internals/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/internals/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "moduleDirectories": ["node_modules", "internals/testing"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}