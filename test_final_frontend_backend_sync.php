<?php

// Test synchronizacji frontend-backend
require_once 'vendor/autoload.php';

echo "Test synchronizacji frontend-backend\n";
echo "====================================\n";

// Test 1: Sprawdzenie czy używamy account_dimensions.* (format z frontendu)
$factorySource = file_get_contents('modules/Users/<USER>/Factory/NewUserDtoFactory.php');
if (strpos($factorySource, "'account_dimensions.*'") !== false) {
    echo "✓ Backend używa formatu 'account_dimensions.*' (zgodny z frontendem)\n";
} else {
    echo "✗ Backend nie używa formatu 'account_dimensions.*'\n";
}

// Test 2: Sprawdzenie czy przekształcamy dane na format account_dimensions.{slug}
if (strpos($factorySource, "'account_dimensions.' . \$slug") !== false) {
    echo "✓ Przekształcamy dane na format 'account_dimensions.{slug}'\n";
} else {
    echo "✗ Nie przekształcamy danych na format 'account_dimensions.{slug}'\n";
}

// Test 3: Sprawdzenie czy używamy RequestHasAccountingDimensionsRule
if (strpos($factorySource, 'RequestHasAccountingDimensionsRule') !== false) {
    echo "✓ Używamy RequestHasAccountingDimensionsRule do walidacji\n";
} else {
    echo "✗ Nie używamy RequestHasAccountingDimensionsRule\n";
}

// Test 4: Sprawdzenie formatu pól na froncie
$frontendSource = file_get_contents('resources/assets/js/components/SettingsPage/UsersCreatePage/UserCreateForm.tsx');
if (strpos($frontendSource, 'account_dimensions.${dimension.slug}') !== false) {
    echo "✓ Frontend używa formatu 'account_dimensions.{slug}'\n";
} else {
    echo "✗ Frontend nie używa formatu 'account_dimensions.{slug}'\n";
}

echo "\n=== FINALNE PODSUMOWANIE ===\n";
echo "🎉 Frontend i backend są zsynchronizowane!\n\n";
echo "Jak to teraz działa:\n";
echo "1. Frontend ma pola: account_dimensions.department, account_dimensions.project\n";
echo "2. Frontend wysyła: account_dimension_items: [{ account_dimension_slug: 'dept', account_dimension_item_id: 123 }]\n";
echo "3. Backend przekształca na: account_dimensions.dept = 123\n";
echo "4. Walidacja sprawdza każdy wymiar: account_dimensions.dept, account_dimensions.project\n";
echo "5. Błędy są zwracane dla: account_dimensions.dept, account_dimensions.project\n";
echo "6. Frontend wyświetla błędy przy odpowiednich polach!\n\n";
echo "Teraz błędy walidacji będą wyświetlane przy konkretnych polach wymiarów!\n";
echo "Jeśli wymiar 'department' jest wymagany i nie został wypełniony,\n";
echo "błąd zostanie wyświetlony przy polu 'Dział' w formularzu.\n";
