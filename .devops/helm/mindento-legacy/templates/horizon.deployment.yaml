apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ .Release.Name }}-horizon"
  labels:
    app: "{{ .Release.Name }}-horizon"
    tier: backend
spec:
  replicas: {{ .Values.horizon.replicas }}
  selector:
    matchLabels:
      app: "{{ .Release.Name }}-horizon"
      tier: backend
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        timestamp: {{ now | quote }} # force re-deploy
      labels:
        app: "{{ .Release.Name }}-horizon"
        service: mindento-legacy
        tier: backend

    spec:
      imagePullSecrets:
        - name: nexus-registry

      nodeSelector:
        app: {{ .Release.Name }}

      tolerations:
        - key: "app"
          operator: "Equal"
          value: "{{ .Release.Name }}"
          effect: "NoSchedule"

      volumes:
        - name: passport
          secret:
            secretName: "{{ .Release.Name }}-passport"

        - name: data
          persistentVolumeClaim:
            claimName: "{{ .Release.Name }}-data"

      containers:
        - name: "{{ .Release.Name }}-horizon"
          image: "{{ .Values.image.repository }}:php-{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command:
            - php
          args:
            - /var/www/html/artisan
            - horizon
          envFrom:
            - configMapRef:
                name: "{{ .Release.Name }}-version"
            - configMapRef:
                name: "{{ .Release.Name }}-horizon"
            - configMapRef:
                name: "{{ .Release.Name }}"
            - secretRef:
                name: "{{ .Release.Name }}"
          volumeMounts:
            - name: data
              mountPath: /var/www/html/storage/app
            - name: passport
              subPath: oauth-private.key
              mountPath: /var/www/html/storage/oauth-private.key
            - name: passport
              subPath: oauth-public.key
              mountPath: /var/www/html/storage/oauth-public.key
          resources:
            requests:
              cpu: {{ .Values.horizon.resources.requests.cpu }}
              memory: {{ .Values.horizon.resources.requests.memory }}
            limits:
              cpu: {{ .Values.horizon.resources.limits.cpu }}
              memory: {{ .Values.horizon.resources.limits.memory }}
