apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ .Release.Name }}-redis-clear-cache"
  labels:
    app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
    app.kubernetes.io/instance: {{ .Release.Name | quote }}
    app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
    helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
  annotations:
    "helm.sh/hook": "post-install,post-upgrade"
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      name: "{{ .Release.Name }}-redis-clear-cache"
      labels:
        app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
        app.kubernetes.io/instance: {{ .Release.Name | quote }}
        helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    spec:
      restartPolicy: Never
      imagePullSecrets:
        - name: nexus-registry
      containers:
        - name: "{{ .Release.Name }}-redis-clear-cache"
          image: "{{ .Values.image.repository }}:php-{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/bin/sh", "-c"]
          args:
            - |
              php artisan redis:clear default
          envFrom:
            - configMapRef:
                name: "{{ .Release.Name }}"
            - secretRef:
                name: "{{ .Release.Name }}"
