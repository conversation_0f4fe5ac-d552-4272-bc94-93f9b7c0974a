<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\InvoiceOcr\Adapter;

use Tests\TestCase;

abstract class AbstractFileTest extends TestCase
{
    public function cb(string $extension, callable $cb): void
    {
        foreach (glob(__DIR__ . '/../_data/*.' . $extension) as $file) {
            $tempFile = tempnam(sys_get_temp_dir(), 'ocr_temp') . '.' . $extension;
            copy($file, $tempFile);

            $cb(new \SplFileInfo($tempFile), new \SplFileInfo($file));
        }
    }
}
