<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\InvoiceOcr\Adapter;

use Mindento\Shared\InvoiceOcr\Adapter\OpenAIOcr;
use Mindento\Shared\InvoiceOcr\Port\File;
use Psr\Log\NullLogger;
use Tests\SnapshotTrait;

class OpenAIInvoiceOCRTest extends AbstractFileTest
{
    use SnapshotTrait;

    public function test_ocr_model_mini(): void
    {
        self::markTestSkipped('For manual testing');

        $client = new OpenAIOcr(
            new NullLogger(),
            config('ocr.openai.key'),
            'o4-mini',
            config('ocr.openai.prompt')
        );

        $this->cb('jpg', function (\SplFileInfo $file, \SplFileInfo $original) use ($client) {
            $result = $client->recognize(new File($file));

            $this->assertMatchesObjectSnapshot($result);
        });
    }

    public function test_ocr_model_full(): void
    {
//        self::markTestSkipped('For manual testing');

        $client = new OpenAIOcr(
            new NullLogger(),
            config('ocr.openai.key'),
            'gpt-4.1',
            config('ocr.openai.prompt')
        );

        $this->cb('jpg', function (\SplFileInfo $file, \SplFileInfo $original) use ($client) {
            $result = $client->recognize(new File($file));

            $this->assertMatchesObjectSnapshot($result);
        });
    }
}
