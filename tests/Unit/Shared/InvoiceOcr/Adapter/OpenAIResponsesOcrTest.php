<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\InvoiceOcr\Adapter;

use Mindento\Shared\InvoiceOcr\Adapter\OpenAIResponsesOcr;
use Mindento\Shared\InvoiceOcr\Port\File;
use Psr\Log\NullLogger;
use Tests\SnapshotTrait;

class OpenAIResponsesOcrTest extends AbstractFileTest
{
    use SnapshotTrait;

    public function test_ocr_model_mini(): void
    {
        self::markTestSkipped('For manual testing');

        $client = new OpenAIResponsesOcr(
            new NullLogger(),
            config('ocr.openai.key'),
            'gpt-4o-mini',
            config('ocr.openai.prompt')
        );

        $this->cb('pdf', function (\SplFileInfo $file, \SplFileInfo $original) use ($client) {
            $result = $client->recognize(new File($file));

            $this->assertMatchesObjectSnapshot($result);
        });
    }

    public function test_ocr_model_full(): void
    {
        self::markTestSkipped('For manual testing');

        $client = new OpenAIResponsesOcr(
            new NullLogger(),
            config('ocr.openai.key'),
            'gpt-4.1',
            config('ocr.openai.prompt')
        );

        $this->cb('pdf', function (\SplFileInfo $file, \SplFileInfo $original) use ($client) {
            $result = $client->recognize(new File($file));

            $this->assertMatchesObjectSnapshot($result);
        });
    }
}