<?php

namespace Tests\Unit\App\Services\User;

use App\Services\User\HoverDataProvider;
use Modules\FeatureSwitcher\Priv\Services\FeatureService;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use ObjectMother\App\UserMother;
use Tests\TestCase;

class HoverDataProviderTest extends TestCase
{
    private $featureServiceMock;

    private $hoverDataProvider;

    protected function setUp(): void
    {
        parent::setUp();

        $this->featureServiceMock = $this->createMock(FeatureService::class);

        $this->hoverDataProvider = new HoverDataProvider($this->featureServiceMock);
    }

    public function testGetHoverDataForUserReturnsData(): void
    {
        $user = UserMother::aUser();
        $name = $user->first_name;
        $email = $user->email;

        $attributes = ['first_name', 'email'];
        $this->featureServiceMock
            ->expects($this->once())
            ->method('getTextParameters')
            ->with(FeatureEnum::FEATURE_USER_HOVER(), $user->company, $user->instance)
            ->willReturn($attributes);

        $hoverData = $this->hoverDataProvider->getHoverDataForUser($user);

        $this->assertCount(2, $hoverData);
        $this->assertEquals($name, $hoverData[0]['value']);
        $this->assertEquals($email, $hoverData[1]['value']);
    }

    public function testGetHoverDataForUserReturnsEmptyWhenNoAttributes(): void
    {
        $user = UserMother::aUser();

        $attributes = [];
        $this->featureServiceMock
            ->expects($this->once())
            ->method('getTextParameters')
            ->with(FeatureEnum::FEATURE_USER_HOVER(), $user->company, $user->instance)
            ->willReturn($attributes);

        $hoverData = $this->hoverDataProvider->getHoverDataForUser($user);

        $this->assertCount(0, $hoverData);
    }

    public function testGetRelatedAttributeValueReturnsNestedValue(): void
    {
        $user = UserMother::aUser();
        $name = $user->first_name;
        $email = $user->email;
        $companyName = $user->company->name;

        $attributes = ['first_name', 'email', 'company.name'];
        $this->featureServiceMock
            ->expects($this->once())
            ->method('getTextParameters')
            ->with(FeatureEnum::FEATURE_USER_HOVER(), $user->company, $user->instance)
            ->willReturn($attributes);

        $hoverData = $this->hoverDataProvider->getHoverDataForUser($user);

        $this->assertCount(3, $hoverData);
        $this->assertEquals($name, $hoverData[0]['value']);
        $this->assertEquals($email, $hoverData[1]['value']);
        $this->assertEquals($companyName, $hoverData[2]['value']);
    }
}
