<?php

declare(strict_types=1);

namespace Tests\Unit\App\Vendors\PointLocation;

use App\Vendors\PointLocation\BoundingBox;
use App\Vendors\PointLocation\Point;
use InvalidArgumentException;
use JsonException;
use Tests\TestCase;

class BoundingBoxTest extends TestCase
{
    public function testFromArrayCreatesBoundingBox(): void
    {
        // Given
        $data = [
            'southwest' => ['lat' => 34.052235, 'lng' => -118.243683],
            'northeast' => ['lat' => 34.052245, 'lng' => -118.243673]
        ];

        // When
        $boundingBox = BoundingBox::fromArray($data);

        // Then
        $this->assertInstanceOf(BoundingBox::class, $boundingBox);
        $this->assertEquals(34.052235, $boundingBox->getSouthwest()->getLat());
        $this->assertEquals(-118.243683, $boundingBox->getSouthwest()->getLng());
        $this->assertEquals(34.052245, $boundingBox->getNortheast()->getLat());
        $this->assertEquals(-118.243673, $boundingBox->getNortheast()->getLng());
    }

    public function testFromArrayThrowsExceptionOnInvalidStructure(): void
    {
        // Given
        $data = [
            'south' => ['lat' => 34.052235, 'lng' => -118.243683],
            'north' => ['lat' => 34.052245, 'lng' => -118.243673]
        ];

        // Expect: InvalidArgumentException
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("Invalid structure: 'southwest' and 'northeast' keys are required.");

        // When
        BoundingBox::fromArray($data);
    }

    public function testToJsonReturnsValidJson(): void
    {
        // Given
        $southwest = Point::create(34.052235, -118.243683);
        $northeast = Point::create(34.052245, -118.243673);
        $boundingBox = new BoundingBox($southwest, $northeast);

        // When
        $expectedJson = json_encode([
            'southwest' => ['lat' => 34.052235, 'lng' => -118.243683],
            'northeast' => ['lat' => 34.052245, 'lng' => -118.243673],
        ], JSON_THROW_ON_ERROR);

        // Then
        $this->assertJsonStringEqualsJsonString($expectedJson, $boundingBox->toJson());
    }

    public function testToJsonThrowsExceptionOnEncodingError(): void
    {
        // Given
        $southwest = Point::create(34.052235, -INF);
        $northeast = Point::create(34.052245, -118.243673);
        $boundingBox = new BoundingBox($southwest, $northeast);

        // Expect
        $this->expectException(JsonException::class);

        // When
        $boundingBox->toJson();
    }
}
