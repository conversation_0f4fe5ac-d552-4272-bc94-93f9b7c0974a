{"data": {"id": "********-0000-0000-0000-********0000", "type": "users", "attributes": {"first_name": "Test", "last_name": "User", "sex": "m", "company_id": "********-0000-0000-0000-********0000", "supervisor_id": null, "assistant_id": null, "cost_center_id": "********-0000-0000-0000-********0000", "cost_centers": [{"id": "********-0000-0000-0000-********0000", "percentage": 100, "main": 1}], "email": "<EMAIL>", "birth_date": "1970-01-01", "phone_number": "+***********", "citizenship": "PL", "erp_id": null, "hr_id": null, "grade": "1", "language": "pl", "work_location": null, "groups": [], "account_dimensions": [{"account_dimension_id": "********-0000-0000-0000-********0000", "account_dimension_item_id": "********-0000-0000-0000-********0000", "type": "DEFAULT"}, {"account_dimension_id": "********-0000-0000-0000-********0000", "account_dimension_item_id": "********-0000-0000-0000-********0000", "type": "DEFAULT"}], "employee_unique_identifier": "<EMAIL>", "bank_account_number": "", "blocked": false}}, "included": {"account-dimensions": [{"id": "********-0000-0000-0000-********0000", "type": "account-dimensions", "attributes": {"company_id": null, "code": "AD1", "name": "Mindento-web general 1 required", "order": 1, "visibility": "general", "is_active": 1}}, {"id": "********-0000-0000-0000-********0000", "type": "account-dimensions", "attributes": {"company_id": null, "code": "AD2", "name": "Mindento-web general 2 not required", "order": 1, "visibility": "general", "is_active": 1}}], "account-dimension-items": [{"id": "********-0000-0000-0000-********0000", "type": "account-dimension-items", "attributes": {"account_dimension_id": "********-0000-0000-0000-********0000", "company_id": "********-0000-0000-0000-********0000", "code": "AD13C1", "name": "General 3 for Company Mindento Testing Company 1A", "order": 1, "is_active": 1}}, {"id": "********-0000-0000-0000-********0000", "type": "account-dimension-items", "attributes": {"account_dimension_id": "********-0000-0000-0000-********0000", "company_id": "********-0000-0000-0000-********0000", "code": "AD13C21", "name": "General 3 for Company Mindento Testing Company 1A", "order": 1, "is_active": 1}}]}}