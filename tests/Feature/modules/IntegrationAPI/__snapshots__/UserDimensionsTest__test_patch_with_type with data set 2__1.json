{"data": {"id": "************0000", "type": "users", "attributes": {"first_name": "Test", "last_name": "User", "sex": "m", "company_id": "********-0000-0000-0000-************", "supervisor_id": null, "assistant_id": null, "cost_center_id": "********-0000-0000-0000-************", "cost_centers": [{"id": "********-0000-0000-0000-************", "percentage": 100, "main": 1}], "email": "<EMAIL>", "birth_date": "1970-01-01", "phone_number": "+************", "citizenship": "AF", "erp_id": "123", "hr_id": null, "grade": "0", "language": "pl", "work_location": null, "groups": [], "employee_unique_identifier": "123", "bank_account_number": "", "account_dimensions": [{"account_dimension_id": "********-0000-0000-0000-************", "account_dimension_item_id": "********-0000-0000-0000-************", "type": "DEFAULT"}, {"account_dimension_id": "********-0000-0000-0000-************", "account_dimension_item_id": "********-0000-0000-0000-************", "type": "DEFAULT"}], "blocked": false}}, "included": {"account-dimensions": [{"id": "********-0000-0000-0000-************", "type": "account-dimensions", "attributes": {"company_id": null, "code": "AD1", "name": "Mindento-web general 1 required", "order": 1, "visibility": "general", "is_active": 1}}], "account-dimension-items": [{"id": "********-0000-0000-0000-************", "type": "account-dimension-items", "attributes": {"account_dimension_id": "********-0000-0000-0000-************", "company_id": null, "code": "AD11", "name": "General 1", "order": 1, "is_active": 1}}, {"id": "********-0000-0000-0000-************", "type": "account-dimension-items", "attributes": {"account_dimension_id": "********-0000-0000-0000-************", "company_id": null, "code": "AD12", "name": "General 2", "order": 1, "is_active": 1}}]}}