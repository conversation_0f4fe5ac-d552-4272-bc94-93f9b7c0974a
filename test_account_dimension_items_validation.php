<?php

// Test walidacji account_dimension_items
require_once 'vendor/autoload.php';

echo "Test walidacji account_dimension_items\n";
echo "======================================\n";

// Test 1: Sprawdzenie czy prepareValidationData zawiera account_dimension_items
$factorySource = file_get_contents('modules/Users/<USER>/Factory/NewUserDtoFactory.php');
if (strpos($factorySource, 'account_dimension_items') !== false) {
    echo "✓ NewUserDtoFactory obsługuje account_dimension_items w prepareValidationData\n";
} else {
    echo "✗ NewUserDtoFactory nie obsługuje account_dimension_items w prepareValidationData\n";
}

// Test 2: Sprawdzenie czy RequiredAccountDimensionItemsRule istnieje
if (class_exists('Modules\Users\Priv\Rules\RequiredAccountDimensionItemsRule')) {
    echo "✓ RequiredAccountDimensionItemsRule istnieje\n";
} else {
    echo "✗ RequiredAccountDimensionItemsRule nie istnieje\n";
}

// Test 3: Sprawdzenie czy walidacja account_dimension_items jest w rules()
if (strpos($factorySource, "'account_dimension_items' => [") !== false) {
    echo "✓ Walidacja account_dimension_items jest dodana do rules()\n";
} else {
    echo "✗ Walidacja account_dimension_items nie jest dodana do rules()\n";
}

// Test 4: Sprawdzenie czy NewUserRequest obsługuje account_dimension_items
$requestSource = file_get_contents('modules/Users/<USER>/Http/Requests/NewUserRequest.php');
if (strpos($requestSource, 'account_dimension_items') !== false) {
    echo "✓ NewUserRequest obsługuje account_dimension_items\n";
} else {
    echo "✗ NewUserRequest nie obsługuje account_dimension_items\n";
}

echo "\n=== PODSUMOWANIE ===\n";
echo "🎯 Walidacja account_dimension_items została zaimplementowana!\n\n";
echo "Teraz gdy frontend wyśle dane w formacie:\n";
echo "{\n";
echo "  \"account_dimension_items\": [\n";
echo "    { \"account_dimension_slug\": \"department\", \"account_dimension_item_id\": 123 },\n";
echo "    { \"account_dimension_slug\": \"project\", \"account_dimension_item_id\": 456 }\n";
echo "  ]\n";
echo "}\n\n";
echo "Backend:\n";
echo "1. Pobierze te dane w NewUserRequest\n";
echo "2. Przekaże je do NewUserDtoFactory w prepareValidationData\n";
echo "3. Sprawdzi czy wszystkie wymagane wymiary zostały przesłane\n";
echo "4. Zwróci błąd walidacji dla pola 'account_dimension_items' jeśli brakuje wymaganych wymiarów\n";
echo "5. Frontend wyświetli komunikat błędu przy sekcji wymiarów analitycznych\n\n";
echo "Komunikat błędu będzie zawierał nazwy brakujących wymiarów!\n";
