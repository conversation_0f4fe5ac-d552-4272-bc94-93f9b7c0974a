## Development

### First run

Do only this step from this manual - take care of project structure
https://gitlab.com/mindento/internal/development-environment#projects-structure-setup

Then:

```
make setup
```

### Run the application

```
make app/run
```

### How to work with

Have a look into `Makefile` for more helpful commands

For example

```
make sh - gives access to bash in the container
```

# Statusy i pliki powiązane

| PL                | EN                   | Trip                                         | Expense                                         |
|-------------------|----------------------|----------------------------------------------|-------------------------------------------------|
| Wersja robocza    | Draft                | TripRequestPageDraft                         | ExpenseRequestPageDraft                         |
| Do zaakceptowania | Waiting for approval | TripRequestPageWaitingForAcceptanceComponent | ExpenseRequestPageWaitingForAcceptanceComponent |
| Aktualna podr<PERSON>   | Ongoing trip         | TripRequestPageTripComponent                 |                                                 |
| Do rozliczenia    | Settlement           | TripRequestPageSettlementComponent           | ExpenseRequestPageSettlementComponent           |
| Do zatwierdzenia  | Claim in approval    | TripRequestPageAcceptanceSettlement          | ExpenseRequestAcceptanceSettlement              |
| Dekretacja        | Journal entries      | TripRequestPageAccounting                    | ExpenseRequestPageAccounting                    |
| Wysłany           | Transferred          | TripRequestPageTransferred                   | ExpenseRequestPageTransferred                   |
| Transfer nieudany | Transfer error       | TripRequestPageTransferError                 | ExpenseRequestPageTransferError                 |
| Zaksięgowany      | Posted               | TripRequestPageFinish                        | ExpenseRequestPageFinish                        |

