<?php

declare(strict_types=1);

namespace <PERSON>ento\Shared\Img2pdf\Adapter;

use Mindento\Shared\Img2pdf\Port\Img2pdfException;
use Mindento\Shared\Img2pdf\Port\Img2pdfInterface;
use Mindento\Shared\Img2pdf\Port\ImgFile;
use Mindento\Shared\Img2pdf\Port\PdfFile;

class ImagicImg2Pdf implements Img2pdfInterface
{
    private const FORMAT = 'pdf';

    private const A4_SIZE_COLUMNS = 595;
    private const A4_SIZE_ROWS = 842;

    private string $tmpPath;

    public function __construct(string $tmpPath = null)
    {
        $this->tmpPath = $tmpPath ?? sys_get_temp_dir();
    }

    public function convert(ImgFile $file): PdfFile
    {
        $name = uniqid('pdf-converted-');
        $outputName = sprintf('%s/%s.pdf', $this->tmpPath, $name);

        $result = $this->exec($outputName, $file->getPathname());

        if (false === file_exists($outputName) && null === $result) {
            throw new Img2pdfException('Error while merging pdf files ' . $file->getPathname());
        }

        return new PdfFile($outputName);
    }

    private function exec(string $outputName, string $file): bool
    {
        $imagick = new \Imagick($file);

        $imagick->setImageUnits(\Imagick::RESOLUTION_PIXELSPERINCH);
        $imagick->setImageFormat(self::FORMAT);
        $imagick->setImageCompression(\Imagick::COMPRESSION_JPEG);
        $imagick->setImageCompressionQuality(70);
        $imagick->resizeImage(1024, 1024, \Imagick::FILTER_SINC, 1, true);
        $imagick->autoOrient();
        $imagick->stripImage();
        $imagick->setOption('density', '150');

        return $imagick->writeImages($outputName, true);
    }
}
