<?php

declare(strict_types=1);

namespace <PERSON>ento\Shared\InvoiceOcr\Adapter;

use Guz<PERSON>Http\Client;
use GuzzleHttp\Exception\GuzzleException;
use Mindento\Shared\InvoiceOcr\Adapter\File\FileToBase64;
use Mindento\Shared\InvoiceOcr\Port\File;
use Mindento\Shared\InvoiceOcr\Port\OcrInterface;
use Mindento\Shared\InvoiceOcr\Port\RecognizeResult;
use Psr\Log\LoggerInterface;

class OpenAIResponsesOcr implements OcrInterface
{
    private string $apiKey;
    private string $prompt;
    private LoggerInterface $logger;
    private string $model;
    private Client $httpClient;

    public function __construct(
        LoggerInterface $logger,
        string $apiKey,
        string $model,
        string $prompt
    ) {
        $this->apiKey = $apiKey;
        $this->logger = $logger;
        $this->prompt = $prompt;
        $this->model = $model;
        $this->httpClient = new Client([
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $this->apiKey,
            ],
        ]);
    }

    public function recognize(File $file): RecognizeResult
    {
        $file = new FileToBase64($file->prepare());

        try {
            $stringResult = $this->callOpenAI($file);
            $result = json_decode($stringResult);
            
            // Extract invoice data from the response based on the actual response structure
            $invoiceData = null;
            
            // Check for output array with messages containing output_text
            if (isset($result->output) && is_array($result->output)) {
                foreach ($result->output as $message) {
                    if (isset($message->type) && $message->type === 'message' && 
                        isset($message->content) && is_array($message->content)) {
                        foreach ($message->content as $content) {
                            if (isset($content->type) && $content->type === 'output_text' && isset($content->text)) {
                                $invoiceData = json_decode($content->text, true);
                                break 2;
                            }
                        }
                    }
                }
            }
            
            // Fallback to older response structure
            if (!$invoiceData && isset($result->text) && isset($result->text->value)) {
                $invoiceData = json_decode($result->text->value, true);
            }
            
            if (!$invoiceData) {
                $this->logger->error('Failed to extract invoice data from OpenAI response', ['result' => (array)$result]);
                return RecognizeResult::createFromArray([], []);
            }

            $this->logger->debug('OpenAI Responses OCR result', ['result' => (array)$result]);
            
            // Create metadata from available response fields
            $metadata = [
                'id' => $result->id ?? '',
                'object' => $result->object ?? '',
                'created' => $result->created_at ?? time(),
                'model' => $result->model ?? $this->model,
            ];
            
            if (isset($result->usage)) {
                $metadata['usage'] = (array)$result->usage;
            }
            
            if (isset($result->system_fingerprint)) {
                $metadata['system_fingerprint'] = $result->system_fingerprint;
            }

            return RecognizeResult::createFromArray($invoiceData, $metadata);
        } catch (\Exception $e) {
            report($e);
            return RecognizeResult::createFromArray([], []);
        }
    }

    private function callOpenAI(File $file): string
    {
        try {
            $payload = [
                'model' => $this->model,
                'input' => [
                    [
                        'role' => 'system',
                        'content' => [
                            [
                                'type' => 'input_text',
                                'text' => $this->prompt,
                            ],
                        ],
                    ],
                    [
                        'role' => 'user',
                        'content' => [
                            [
                                'type' => 'input_file',
                                'filename' => $file->getFilename(),
                                'file_data' => 'data:application/pdf;base64,' . file_get_contents($file->prepare()->getRealPath()),
                            ],
                        ],
                    ],
                ],
                'text' => [
                    'format' => [
                        'type' => 'json_object',
                    ],
                ],
                'reasoning' => new \stdClass(),
                'tools' => [],
                'temperature' => 0,
                'max_output_tokens' => 2048,
                'top_p' => 0,
                'store' => false,
            ];

            $response = $this->httpClient->post('https://api.openai.com/v1/responses', [
                'json' => $payload,
            ]);

            return $response->getBody()->getContents();
        } catch (GuzzleException $e) {
            report($e);
            return json_encode([
                'error' => [
                    'message' => 'Failed to call OpenAI API: ' . $e->getMessage()
                ]
            ]);
        } catch (\Exception $e) {
            report($e);
            return json_encode([
                'error' => [
                    'message' => 'Error preparing OpenAI request: ' . $e->getMessage()
                ]
            ]);
        }
    }
}