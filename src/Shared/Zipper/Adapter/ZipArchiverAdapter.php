<?php

namespace <PERSON><PERSON><PERSON>\Shared\Zipper\Adapter;

use App\Services\SlugGeneratorService;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Support\Collection;
use Mindento\Shared\Zipper\Port\ContentFile;
use Mindento\Shared\Zipper\Port\File;
use Mindento\Shared\Zipper\Port\FileTypeEnum;
use Mindento\Shared\Zipper\Port\StorageFile;
use Mindento\Shared\Zipper\Port\ArchiverInterface;
use ZipArchive;

final class ZipArchiverAdapter implements ArchiverInterface
{
    private Filesystem $filesystem;
    private SlugGeneratorService $slugGeneratorService;
    private Collection $files;
    private ZipArchive $archiver;
    private string $path;
    private string $absoluteArchivePath;
    private string $relativeArchivePath;
    private bool $initialized;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
        $this->initialized = false;
    }

    public function add(File $file): void
    {
        if ($this->initialized === false) {
            $this->init();
        }

        $this->files->push($file);
    }

    public function compress(): void
    {
        $this->addFilesToCompress();

        $this->archiver->close();
    }

    public function clean(): void
    {
        $this->filesystem->deleteDirectory($this->path);
        $this->initialized = false;
    }

    public function getArchive(): StorageFile
    {
        return new StorageFile(
            basename($this->relativeArchivePath),
            FileTypeEnum::ZIP(),
            $this->relativeArchivePath
        );
    }

    public function getArchiveAsContent(): ContentFile
    {
        return new ContentFile(
            basename($this->relativeArchivePath),
            FileTypeEnum::ZIP(),
            $this->filesystem->get($this->relativeArchivePath)
        );
    }

    private function init(): void
    {
        $this->initializePathsAndVariables();

        if (!$this->archiver->open($this->absoluteArchivePath, ZipArchive::OVERWRITE | ZipArchive::CREATE)) {
            throw new \Exception('Unable to open archive.');
        }

        $this->initialized = true;
    }

    private function initializePathsAndVariables(): void
    {
        $this->path = $this->generatePath('tmp');

        $this->prepareDirectory($this->path);

        $this->files = collect();

        $this->archiver = new ZipArchive();

        $archiveSlug = $this->slugGeneratorService->generate();

        $this->relativeArchivePath = $this->generatePath($this->path, $archiveSlug, FileTypeEnum::ZIP());
        $this->absoluteArchivePath = $this->generatePath(storage_path('app/' . $this->path), $archiveSlug, FileTypeEnum::ZIP());
    }

    private function generatePath(string $path, string $name = null, string $extension = null): string
    {
        $generatedSlug = $name ?? $this->slugGeneratorService->generate();

        $pathSegments = array_filter([$path, $generatedSlug]);
        $generatedPath = join('/', $pathSegments);

        if ($extension) {
            $generatedPath .= '.' . $extension;
        }

        return $generatedPath;
    }

    private function addFilesToCompress(): void
    {
        $this->files->each(function (File $file) {
            $fileContent = '';

            if ($file instanceof StorageFile) {
                $fileContent = $this->filesystem->get($file->getFilePath());
            } elseif ($file instanceof ContentFile) {
                $fileContent = $file->getContent();
            }

            $this->archiver->addFromString($file->getFileName(), $fileContent);
        });
    }

    private function prepareDirectory(string $path): void
    {
        if ($this->filesystem->exists($path) === false) {
            $this->filesystem->makeDirectory($path);
        }
    }
}