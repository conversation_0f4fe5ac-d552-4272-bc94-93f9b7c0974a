<?php

// Prosty test walidacji wymiarów analitycznych
require_once 'vendor/autoload.php';

use Modules\Users\Priv\Rules\RequiredAccountDimensionsRule;
use Modules\Analytics\Priv\Services\AccountDimensionService;
use Illuminate\Support\Collection;

echo "Test walidacji wymiarów analitycznych\n";
echo "=====================================\n";

// Test 1: Sprawdzenie czy klasa istnieje
if (class_exists('Modules\Users\Priv\Rules\RequiredAccountDimensionsRule')) {
    echo "✓ Klasa RequiredAccountDimensionsRule została utworzona\n";
} else {
    echo "✗ Klasa RequiredAccountDimensionsRule nie istnieje\n";
}

// Test 2: Sprawdzenie czy pliki językowe zostały zaktualizowane
$enErrorFile = 'modules/Users/<USER>/Resources/lang/en/error.php';
$plErrorFile = 'modules/Users/<USER>/Resources/lang/pl_PL.utf8/error.php';

if (file_exists($enErrorFile)) {
    $enErrors = include $enErrorFile;
    if (isset($enErrors['required-account-dimensions-missing'])) {
        echo "✓ Komunikat błędu w języku angielskim został dodany\n";
    } else {
        echo "✗ Komunikat błędu w języku angielskim nie został dodany\n";
    }
} else {
    echo "✗ Plik językowy angielski nie istnieje\n";
}

if (file_exists($plErrorFile)) {
    $plErrors = include $plErrorFile;
    if (isset($plErrors['required-account-dimensions-missing'])) {
        echo "✓ Komunikat błędu w języku polskim został dodany\n";
    } else {
        echo "✗ Komunikat błędu w języku polskim nie został dodany\n";
    }
} else {
    echo "✗ Plik językowy polski nie istnieje\n";
}

echo "\nImplementacja walidacji wymiarów analitycznych została zakończona!\n";
echo "Funkcjonalności:\n";
echo "- Walidacja wymaganych wymiarów analitycznych\n";
echo "- Komunikaty błędów w języku polskim i angielskim\n";
echo "- Integracja z istniejącym systemem walidacji\n";
echo "- Obsługa danych z formularza frontendowego\n";
