<?php


namespace App;


use App\Helpers\NameTranslation;
use App\Interfaces\Models\LocalizableTripElementInterface;
use App\Interfaces\RequestElementAcceptanceSourceInterface;
use App\Interfaces\RequestElementDateableInterface;
use App\Interfaces\RequestElementInterface;
use App\Interfaces\RequestElementObservableInterface;
use App\Traits\CountryTrait;
use App\Traits\InstanceTrait;
use App\Traits\Models\RequestElementAcceptanceSourceTrait;
use App\Traits\RequestTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Modules\TripPlanner\Pub\Enums\RequestElementAcceptanceSourceEnum;

/**
 * App\TargetPoint
 *
 * @property int country_id
 * @property Location|null location
 * @property Carbon|null date
 * @property mixed id
 * @property string uuid
 * @property RequestElementAcceptanceSourceEnum request_element_acceptance_source
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $instance_id
 * @property int $request_id
 * @property string $uuid
 * @property RequestElementAcceptanceSourceEnum $request_element_acceptance_source
 * @property int $country_id
 * @property \Illuminate\Support\Carbon $date
 * @property int $weight
 * @property int $return_weight
 * @property-read \App\Country $country
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $documents
 * @property-read int|null $documents_count
 * @property-read \App\Instance $instance
 * @property-read \App\Location|null $location
 * @property-read \App\Request $request
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint query()
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereRequestElementAcceptanceSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereReturnWeight($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TargetPoint whereWeight($value)
 * @mixin \Eloquent
 */
class TargetPoint extends AbstractRequestElement implements
    RequestElementObservableInterface,
    RequestElementInterface,
    RequestElementDateableInterface,
    LocalizableTripElementInterface,
    RequestElementAcceptanceSourceInterface
{
    use RequestElementAcceptanceSourceTrait;
    use RequestTrait;
    use InstanceTrait;
    use CountryTrait;

    const RELATION_NAME = 'target_point';

    protected $touches = ['request'];

    protected $fillable = [
        'uuid',
    	'weight',
        'date',
        'country_id',
        'request_id',
        'instance_id'
    ];

    protected $dates = [
        'date'
    ];

    public function location()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'location']);
    }

    public function documents()
    {
        return $this->morphToMany(Document::class, 'element', 'request_element_document')->whereRaw('0=1');
    }

    public function getConvertedAmount(): string
    {
        return '';
    }

    public function getConvertedAmountCurrency(): Currency
    {
        return $this->instance->currency;
    }

    /**
     * Returns array of the original amounts
     *
     * Example response:
     * [
     *      'USD' => '20.64',
     *      'PLN' => '10.30',
     *      'EUR' => '1003.10',
     * ]
     *
     * @return array
     */
    public function getOriginalAmounts(): array
    {
        return [];
    }

    public function getAccountedAmount(): string
    {
        return '';
    }

    public function getName($separator = ' → '): ?string
    {
        return $this->getNameTranslation($separator)->translate();
    }

    public function getNameTranslation($separator = ' → '): NameTranslation
    {
        return new NameTranslation($this->location->city);
    }

    public function getStartDate()
    {
        return $this->date;
    }

    public function getType(): ?string
    {
        return static::RELATION_NAME;
    }

    public function isNational(): bool
    {
        return $this->instance->country_id === $this->country_id;
    }

    public function getStartDateAttributeName(): string
    {
        return 'date';
    }

    public function getEndDateAttributeName(): string
    {
        return 'date';
    }
}
