<?php

declare(strict_types=1);

namespace App\Exceptions;

class MpkFilesNotFoundForTodayException extends \Exception
{
    public const DEFAULT_MESSAGE = "There is no file for today on server for MPK, but whe latest one is: %s";

    public $lastProcessedFileName;

    /**
     * Create instance.
     *
     * @param string $message
     *
     * @param string|null $lastProcessedFileName
     * @return MpkFilesNotFoundForTodayException
     */
    public static function create(
        string $message = null,
        string $lastProcessedFileName = null
    ): MpkFilesNotFoundForTodayException
    {
        $message
            ? $exception = new self(sprintf($message, $lastProcessedFileName))
            : $exception = new self(sprintf(self::DEFAULT_MESSAGE, $lastProcessedFileName));
        $lastProcessedFileName ? $exception->lastProcessedFileName : null;

        return $exception;
    }
}