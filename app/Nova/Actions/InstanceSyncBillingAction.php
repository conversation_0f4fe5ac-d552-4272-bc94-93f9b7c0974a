<?php

declare(strict_types=1);

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class InstanceSyncBillingAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $showOnTableRow = true;
    public $showOnIndex = false;

    public $name = 'Sync Instance in Billing';

    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each(function ($model) {
            \Artisan::queue('mindento:sync', ['--additionalDomain' => $model->domain]);
        });
    }
}
