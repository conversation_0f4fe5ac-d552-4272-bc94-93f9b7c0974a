<?php

namespace App\Policies;

use App\Permission;
use App\Traits\Policies\SuperAdminCanDoAnything;
use App\User;
use App\UserAssistant;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserAssistantPolicy
{
    use HandlesAuthorization;
    use SuperAdminCanDoAnything;

    public function view(User $authUser, UserAssistant $userAssistant)
    {
        if($authUser->isBillingUser()) {
            return true;
        }
        
        $subjectUser = $userAssistant->user;

        if ($authUser->instance_id !== $subjectUser->instance_id) {
            throw new \Exception('Cannot view user deputy');
        }

        return true;
    }

    public function create(User $authUser, UserAssistant $userAssistant)
    {
        $subjectUser = $userAssistant->user;

        if (($subjectUser->id !== $authUser->id
                && (
                    $authUser->hasAbility(Permission::MULTI_INSTANCE_VIEW) === false
                    && $authUser->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS) === false
                )
            )
            || $authUser->instance_id !== $subjectUser->instance_id
        ){
            throw new \Exception('Cannot create user deputy');
        }

        return true;
    }

    /**
     * Use for update all user's attributes
     *
     * @param User $user
     * @param User $editedUser
     * @return bool
     */
    public function update(User $user, User $editedUser)
    {

        return false;
    }

    public function delete(User $authUser, UserAssistant $userAssistant)
    {
        $subjectUser = $userAssistant->user;

        if (($subjectUser->id !== $authUser->id
                && (
                    $authUser->hasAbility(Permission::MULTI_INSTANCE_VIEW) === false
                    && $authUser->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS) === false
                )
            )
            || $authUser->instance_id !== $subjectUser->instance_id
        ){
            throw new \Exception('Cannot delete user deputy');
        }

        return true;
    }
}
