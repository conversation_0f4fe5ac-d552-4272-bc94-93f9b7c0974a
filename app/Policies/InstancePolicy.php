<?php

namespace App\Policies;

use App\Instance;
use App\Permission;
use App\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class InstancePolicy
{
    use HandlesAuthorization;

    public function create(User $user)
    {
        return $user->isSuperAdmin();
    }

    public function update(User $user)
    {
        return $user->isSuperAdmin();
    }

    public function delete(User $user)
    {
        return $user->isSuperAdmin();
    }

    public function view(User $user)
    {
        return
            $user->hasAbility(Permission::MULTI_INSTANCE_VIEW) ||
            $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS);
    }

    public function clearInstance(User $user, Instance $instance)
    {
        return $user->isAdmin() && $user->internal && $user->instance_id = $instance->id;
    }
}
