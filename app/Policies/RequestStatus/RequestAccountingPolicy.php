<?php


namespace App\Policies\RequestStatus;


use App\Interfaces\RequestStatusPolicyInterface;
use App\Request;
use App\Traits\Policies\CanShowUnrequestedDocumentsTrait;
use App\Traits\Policies\DirectSubordinateRequestTrait;
use App\Traits\Policies\RequestInstallmentsEditableTrait;
use App\Traits\Policies\RequestPolicyTrait;
use App\User;
use Carbon\Carbon;

class RequestAccountingPolicy implements RequestStatusPolicyInterface
{
    use RequestPolicyTrait;
    use RequestInstallmentsEditableTrait;
    use CanShowUnrequestedDocumentsTrait;
    use DirectSubordinateRequestTrait;

    /**
     * @inheritdoc
     */
    public function view(User $user, Request $request): bool
    {
        if ($this->isOwner($user, $request)) {
            return true;
        }

        if ($this->isAcceptor($user, $request)) {
            return true;
        }

        if ($this->isSettlementAcceptor($user, $request)) {
            return true;
        }

        if($this->isAccountant($user, $request)) {
            return true;
        }

        if ($this->isDirectSubordinateRequest($user, $request) === true) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function edit(User $user, Request $request): bool
    {
        return false;
    }

    public function update(User $user, Request $request): bool
    {
        if ($this->isRequestAccountant($user, $request)) {
            return true;
        }
        return false;

    }

    /**
     * @inheritdoc
     */
    public function delete(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function attachAcceptor(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function detachAcceptor(User $user, User $acceptor, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function cancel(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToAcceptance(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function accept(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function returnToImprovement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function reject(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function settle(User $user, Request $request): bool
    {
        if($this->isRequestAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function addAndDeleteDocuments(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function editDocuments(User $user, Request $request): bool
    {
        if($this->isRequestAccountant($user, $request)) {
            return true;
        }

        if($this->isBillingUser($user)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function accountDocuments(User $user, Request $request): bool
    {
        if($this->isRequestAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToAcceptanceOfSettlement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function attachSettlementAcceptor(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function detachSettlementAcceptor(User $user, User $acceptor, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function acceptSettlement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function returnToSettlementImprovement(User $user, Request $request): bool
    {
        if($this->isRequestAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    public function cancelAssignmentToAccountant(User $user, Request $request): bool
    {
        return self::returnToSettlementImprovement($user, $request);
    }

    /**
     * @inheritdoc
     */
    public function assignAccountant(User $user, Request $request): bool
    {
        if($request->accounting_user_id !== null) {
            return false;
        }

        if($this->isAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function account(User $user, Request $request): bool
    {
        if($this->isRequestAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToERP(User $user, Request $request): bool
    {
        if($this->isRequestAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function viewSettlementSummary(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function sendToSettlement(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * Set installment paid date nad installment status
     * @param User $user
     * @param Request $request
     * @return bool
     */
    public function accountantEditInstallments(User $user, Request $request): bool
    {
        if($this->isAccountant($user, $request)) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function searchOffers(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function bookOffers(User $user, Request $request): bool
    {
        return false;
    }

    /**
     * @inheritdoc
     */
    public function setAsUnrealized(User $user, Request $request): bool
    {
        if($this->isOwner($user, $request)) {
            return true;
        }

        return false;
    }

    public function tripDidNotStarted(User $user, Request $request): bool
    {
        return false;
    }

    public function canEditPurpose(User $user, Request $request): bool
    {
        return false;
    }

    public function addDocuments(User $user, Request $request): bool
    {
        return $this->editDocuments($user, $request);
    }

    public function addUnrequestedReservation(User $user, Request $request): bool
    {
        return false;
    }

    public function canEditBasicSection(User $user, Request $request): bool
    {
        return $this->edit($user, $request);
    }

    public function returnToDecreeWhenTransferError(User $user, Request $request): bool
    {
        return false;
    }
}
