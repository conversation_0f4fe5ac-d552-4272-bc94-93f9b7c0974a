<?php

namespace App;

use App\Traits\CompanyTrait;
use App\Traits\InstanceTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * Class Card
 *
 * @package App
 * @property int id
 * @property int instance_id
 * @property int owner_id
 * @property int company_id
 * @property int priority
 * @property string slug
 * @property string type
 * @property Collection users
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property string $type
 * @property string $slug
 * @property int $owner_id
 * @property int|null $company_id
 * @property-read \App\Company|null $company
 * @property-read \App\Instance $instance
 * @property-read \App\User $owner
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder|Card newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Card newQuery()
 * @method static \Illuminate\Database\Query\Builder|Card onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Card query()
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereOwnerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Card withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Card withoutTrashed()
 * @mixin \Eloquent
 * @property int|null $instance_id
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereInstanceId($value)
 */
class Card extends Model
{
    const CORPORATION_TYPE = 'corpo';
    const INDIVIDUAL_TYPE = 'individual';

    use InstanceTrait;
    use SoftDeletes;
    use CompanyTrait;

    protected $fillable = [
        'slug',
        'instance_id',
        'type',
        'owner_id',
        'company_id'
    ];

    public static function types(): Collection
    {
        return collect([
            static::CORPORATION_TYPE,
            static::INDIVIDUAL_TYPE
        ]);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'card_users')
            ->withPivot([ 'priority']);
    }

    public function owner()
    {
        return $this->belongsTo(User::class);
    }

    public function isCorporate(): bool
    {
        return $this->type === self::CORPORATION_TYPE;
    }

    public function isIndividual(): bool
    {
        return $this->type === self::INDIVIDUAL_TYPE;
    }
}
