<?php

declare(strict_types=1);

namespace App\Compliance\Instances\Amrest\Rules;

use App\AccommodationLimit;
use App\Repositories\ExchangeRateRepository;
use App\Services\AccommodationLumpSum\AccommodationLimitService;
use App\Services\Country\CountryService;

class WarningAccommodationByLimitsRule extends AbstractWarningAccommodationRule
{

    public const NAME = 'warning_accommodation_by_limit_rule';

    protected function getMaxAmount(): ?float
    {
        /** @var AccommodationLimitService $accommodationLimitService */
        $accommodationLimitService = resolve(AccommodationLimitService::class);
        /** @var CountryService $countryService */
        $countryService = resolve(CountryService::class);
        $country = $countryService->getCountryByCode($this->model->getCountryCode());

        $instance = $this->model->getInstance();
        $accommodationLimit = $accommodationLimitService->getByCountryIdForDate(
            $country,
            $instance->id,
            $this->model->getFromDate(),
        );

        if (!$accommodationLimit instanceof AccommodationLimit) {
            return null;
        }

        if ($instance->currency->code === $accommodationLimit->currency->code) {
            return (float)$accommodationLimit->accommodation_limit;
        }

        $amount = resolve(ExchangeRateRepository::class)->convertAmountToInstanceCurrency(
            $accommodationLimit->currency,
            $accommodationLimit->accommodation_limit,
            null,
            $instance,
        );

        return (float)$amount;
    }
}
