<?php

declare(strict_types=1);

namespace App\Events\Ocr;

use App\Document;
use App\Vendors\OCR\AuthConfig;
use App\Vendors\OCR\OCRAsync;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Mindento\Tracer\Traits\Traceable;

class OcrProcessAsync implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels, Traceable;

    public $file;
    public $ocr;

    public function __construct(string $providerName, Document $file, AuthConfig $auth)
    {
        $this->file = $file;
        $this->ocr = new OCRAsync($providerName, $file, $auth);

    }
}
