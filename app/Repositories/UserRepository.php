<?php

namespace App\Repositories;

use App\BillingAPI\DTOs\UsersWithActivityOffsetPageDto;
use App\BillingAPI\ValueObjects\Email;
use App\Company;
use App\Exceptions\User\TechnicalUserNotConfiguredException;
use App\Http\Responses\UserResponse;
use App\Instance;
use App\Interfaces\StorageServiceInterface;
use App\Location;
use App\Repositories\Criteria\ActiveUserCriterion;
use App\Repositories\Criteria\CompanyCriterion;
use App\Repositories\Criteria\ExcludeAgentGroupCriterion;
use App\Repositories\Criteria\ExcludeBillingGroupCriterion;
use App\Repositories\Criteria\GradeCriterion;
use App\Repositories\Criteria\GroupNameCriterion;
use App\Repositories\Criteria\InstanceV2Criterion;
use App\Repositories\Criteria\LevelCriterion;
use App\Repositories\Criteria\MpkCriterion;
use App\Repositories\Criteria\NonInternalUserCriterion;
use App\Repositories\Criteria\RequestPaginationCriterion;
use App\Repositories\Criteria\SearchPhraseCriterion;
use App\Repositories\Criteria\UserOrderByFullNameCriterion;
use App\Repositories\Pagination\Paginator;
use App\Repositories\Pagination\PaginatorInterface;
use App\Request;
use App\Services\Language\LanguageService;
use App\Services\Storage\StorageServiceDto;
use App\Services\User\FeatureRequiredFields;
use App\Services\UserImport\ImportService;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Common\Dtos\PageInterface;
use Modules\Common\ValueObjects\Slug;
use Modules\Common\ValueObjects\Sorter;
use Modules\Users\Priv\Exceptions\UserNotFoundException;
use Modules\Users\Priv\Rules\IbanRule;
use Modules\Users\Priv\Rules\UserEmailRule;
use Modules\Users\Priv\Rules\UserErpIdRule;
use Modules\Users\Priv\Rules\UserHrIdRule;

/**
 * @method Model create($data)
 * @method User|null findBySlug($data)
 * @method User|null findBySlugAndInstanceId(string $slug, int $instanceId)
 *
 * Class UserRepository
 * @package App\Repositories
 */
class UserRepository extends Repository
{
    protected $idColumnName = 'slug';

    protected $uploadDirName = 'avatars';

    const USER_FIRST_NAME_INDEX = 0;
    const USER_LAST_NAME_INDEX = 1;
    const USER_EMAIL_INDEX = 2;
    const USER_SUPERVISOR_EMAIL_INDEX = 3;
    const USER_PHONE_INDEX = 4;
    const USER_PHONE_ICE_INDEX = 5;
    const USER_COMPANY_ID_INDEX = 6;
    const USER_GRADE_INDEX = 7;
    const USER_LEVEL_INDEX = 8;
    const USER_MPK_ID_INDEX = 9;
    const USER_INSTANCE_ID_INDEX = 10;
    const USER_ERP_ID_INDEX = 11;
    const USER_NATIONALITY_CODE_INDEX = 12;
    const USER_CITIZENSHIP_CODE_INDEX = 13;
    const USER_GROUP_INDEX = 14;

    static function model()
    {
        return User::class;
    }

    protected function createRules()
    {
        return [
            'slug' => ['required', 'string', 'max:36'],
            'first_name' => 'required|max:255',
            'last_name' => 'required|max:255',
            'email' => 'required|email',
            'password' => 'sometimes|min:8|max:255',
            'phone' => 'sometimes|max:255',
            'phone_ice' => 'sometimes|max:255',
            'instance_id' => 'integer|exists:instances,id',
            'company_id' => 'required|integer|exists:companies,id',
            'user_id' => 'sometimes|nullable|integer|exists:users,id',
            'supervisor_email' => 'sometimes|nullable|email|max:255',
            'blocked_at' => 'nullable|carbonDateTime',
            'sex' => 'in:' . User::getAvailableSexes()->implode(','),
            'locale' => 'string|max:255',
            'grade' => 'integer',
            'level' => 'required|string|max:255',
            'mpk_id' => 'nullable|integer|exists:mpks,id',
            'erp_id' => 'nullable|string|max:255',
            'hr_id' => 'nullable|string|max:255',
            'nationality_code' => 'sometimes|string|exists:countries,country_code',
            'nationality_id' => 'sometimes|integer|exists:countries,id',
            'citizenship_code' => 'sometimes|string|exists:countries,country_code|max:3',
            'citizenship_id' => 'sometimes|integer|exists:countries,id',
            'group' => 'sometimes|nullable|string|max:255',
            'license_plate' => 'nullable|string|max:255',
            'company_license_plate' => 'nullable|string|max:255',
            'iban' => 'sometimes|nullable|string|max:255',
        ];
    }

    public function addBadPinCodeAttempt(User $user): void
    {
        $user->increment('bad_pin_code_attempts');
    }

    public function resetBadPinCodeAttempts(User $user): void
    {
        $user->bad_pin_code_attempts = 0;
        $user->save();
    }

    public function updateIdentityProviderIdByEmployeeIdentifier(
        string $employeeUniqueIdentifier,
        string $identityProviderId,
        int $instanceId
    ): void {
        User::query()->where([
            'instance_id' => $instanceId,
            'employee_unique_identifier' => $employeeUniqueIdentifier,
        ])->update(['identity_provider_id' => $identityProviderId]);
    }

    public function removeIdentityProviderIdFromAll(Instance $instance): void
    {
        User::query()->where([
            'instance_id' => $instance->id,
        ])->update(['identity_provider_id' => null]);
    }

    public function findAllWithoutIdentityProviderId(int $instanceId, ?int $limit = null): Collection
    {
        $builder = User::query()->where([
            'instance_id' => $instanceId,
            'identity_provider_id' => null,
        ]);

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->get();
    }

    public function findUserByIdentityProviderIdAndInstanceId(string $identityProviderId, int $instanceId): ?User
    {
        return User::select(['users.*'])
            ->where([['identity_provider_id', '=', $identityProviderId], ['instance_id', '=', $instanceId]])
            ->orderBy('blocked_at')
            ->first();
    }

    protected function updateRules($data = []): array
    {
        $user = $this->findBySlug($data['slug']);

        $rules = [
            'first_name' => 'required|max:255',
            'last_name' => 'required|max:255',
            'phone' => 'required|max:255',
            'phone_ice' => 'sometimes|max:255',
            'instance_id' => 'integer|exists:instances,id',
            'company_id' => 'required|integer|exists:companies,id',
            'user_id' => 'nullable|integer|exists:users,id',
            'grade' => 'integer',
            'level' => 'nullable|integer',
            'mpk_id' => 'nullable|integer|exists:mpks,id',
            'blocked_at' => 'nullable|carbonDateTime',
            'sex' => 'in:' . User::getAvailableSexes()->implode(','),
            'nationality_id' => 'nullable|integer|exists:countries,id',
            'citizenship_id' => 'required|integer|exists:countries,id',
            'avatar' => 'max:255',
            'birth_date' => 'carbonDate',
            'passport_number' => 'nullable|string',
            'passport_issue_date' => 'nullable|carbonDate',
            'passport_valid_date' => 'nullable|carbonDate',
            'identity_card_number' => 'nullable|string|max:255',
            'policy' => 'string|max:255',
            'locale' => 'string|max:255',
            'iban' => ['nullable', 'string', 'max:255', new IbanRule()],
            'license_plate' => 'nullable|string|max:255',
            'company_license_plate' => 'nullable|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                new UserEmailRule(
                    $data['employee_unique_identifier'] ?? $user->employee_unique_identifier,
                    $user->instance_id,
                    $user
                )
            ],
            'hr_id' => [
                'nullable',
                'max:255',
                new UserHrIdRule(
                    $data['employee_unique_identifier'] ?? $user->employee_unique_identifier,
                    $user->instance_id,
                    $user->company_id,
                    $user
                )
            ],
            'erp_id' => [
                'nullable',
                'max:255',
                new UserErpIdRule(
                    $data['employee_unique_identifier'] ?? $user->employee_unique_identifier,
                    $user->instance_id,
                    $user->company_id,
                    $user
                )
            ],
        ];

        /** @var FeatureRequiredFields $userRequiredFieldsService */
        $userRequiredFieldsService = resolve(FeatureRequiredFields::class);
        $requiredFields = $userRequiredFieldsService->getRequiredFields($user) ?? [];

        foreach ($requiredFields as $requiredField) {
            if (is_array($rules[$requiredField])) {
                $rules[$requiredField] = array_filter(
                    $rules[$requiredField],
                    fn($rule) => $rule !== 'nullable'
                );
                array_unshift($rules[$requiredField], 'required');
            } else {
                $rules[$requiredField] = str_replace('nullable|', '', $rules[$requiredField]);
                $rules[$requiredField] = 'required|' . $rules[$requiredField];
            }
        }

        return $rules;
    }


    protected function allowedWith()
    {
        return [
            'mpk',
            'hasMpks',
            'instance',
            'company',
            'nationality',
            'citizenship',
            'loyaltyCards',
            'workLocation',
            'groups',
            'supervisor',
            'availableIdentities',
        ];
    }

    protected function beforeCreate(User $modelUser, $data)
    {
        do {
            $slug = str_random(16);
        } while (Request::where('slug', $slug)
            ->count() > 0);

        if (!isset($data['citizenship_id'])) {
            $modelUser->citizenship_id = $modelUser->instance->country_id;
        }

        if (isset($data['lang'])) {
            $modelUser = $this->setLocale($modelUser, $data['lang']);
        }

        $modelUser->slug = $slug;
        $modelUser->is_new = true;

        return $modelUser;
    }

    protected function beforeUpdate(User $modelUser, $data)
    {
        if (Auth::user()->can('updateProfile', $modelUser) === false) {
            abort(403, trans('global.access-denied'));
        }

        if (isset($data['lang'])) {
            $modelUser = $this->setLocale($modelUser, $data['lang']);
        }

        return $modelUser;
    }

    protected function afterUpdate(User $modelUser, $data)
    {
        if (isset($data['work_location'])) {
            $locationRepo = resolve(LocationRepository::class);
            $workLocation = $modelUser->workLocation()
                ->first();

            if ($workLocation) {
                $locationRepo->update($workLocation->id ?? null, $data['work_location']);
            } else {
                $locationRepo->createLocationForRequestElement(
                    $data['work_location'],
                    $modelUser->id,
                    'user',
                    'work_location',
                    $modelUser->instance_id
                );
            }
        } elseif (array_key_exists('work_location', $data) && $data['work_location'] === null) {
            $modelUser->workLocation()
                ->delete();
        }

        return $modelUser;
    }

    protected function setLocale(User $user, string $language)
    {
        $service = new LanguageService();

        $user->locale = $service->getLocaleFromLanguage($language) ?? $user->instance->locale;

        return $user;
    }

    public function changeAvatar(User $user, UploadedFile $file)
    {
        $this->authorize('update', $user);

        $this->uploadDirName .= "/" . $user->slug;

        $filepath = (\Storage::disk('public')
            ->put($this->uploadDirName, $file));
        \Storage::disk('public')
            ->delete($this->uploadDirName . '/' . $user->avatar);

        $user->avatar = pathinfo($filepath, PATHINFO_BASENAME);
        $user->save();

        return $user;
    }

    public static function filterActiveUsers(Collection $users, Collection $activeUsers): Collection
    {
        return $users->filter(function (User $user) use (&$activeUsers) {
            return $activeUsers->contains($user->slug);
        })
            ->values();
    }

    public function importParseUsers(UploadedFile $file, ImportService $importService)
    {
        return UserResponse::collection($importService->parseWithRules($file, $this->createRules()));
    }

    public function importProcessUsers(array $users, ImportService $importService)
    {
        return UserResponse::collection($importService->importWithRules($users, $this->createRules()));
    }

    public function getUsersThatAdminCanModify(Instance $instance)
    {
        $instanceUsers = $this->getByCriteria([
            new InstanceV2Criterion($instance),
            new SearchPhraseCriterion(),
            new CompanyCriterion(),
            new GroupNameCriterion(),
            new ActiveUserCriterion(),
            new ExcludeBillingGroupCriterion(),
            new ExcludeAgentGroupCriterion(),
            new UserOrderByFullNameCriterion(),
            new LevelCriterion(),
            new MpkCriterion(),
        ]);

        /** @var User $userAuth */
        $userAuth = Auth::user();

        $users = $instanceUsers->where('id', '!=', $userAuth->id);

        if ($userAuth->isSuperAdmin()) {
            return $users;
        }

        return $this->filterNotSuperAdminUsers($users);
    }

    public function paginateByCriteriaWhichOutGrouping(
        $criteria,
        callable $indexQuery,
        string $countBy,
        PageInterface $page
    ): Paginator {
        $this->addCriteria($criteria);
        $this->prepare();
        $queryTotal = $indexQuery()->getQuery()->count($countBy);
        $this->addCriteria([new RequestPaginationCriterion($page)]);
        $this->prepare();
        $items = $indexQuery()->get();

        $paginator = Paginator::create($items, $queryTotal, $page);
        $this->clearCriteria();

        return $paginator;
    }

    public function getLoggableUsers(User $user, ?User $proxyUser, PageInterface $page): Paginator
    {
        return $this->paginateByCriteriaWhichOutGrouping(
            [
                new SearchPhraseCriterion('', User::CROSS_FIELDS_USER_SEARCH),
                new InstanceV2Criterion($user->instance),
                new UserOrderByFullNameCriterion(),
                new CompanyCriterion(),
                new ActiveUserCriterion(ActiveUserCriterion::STATUS_ACTIVE),
                new ExcludeBillingGroupCriterion(),
                new ExcludeAgentGroupCriterion(),
                new NonInternalUserCriterion($user->internal)
            ],
            function () use ($user, $proxyUser) {
                return $this->usersLoggableUsersQuery($user, $proxyUser);
            },
            'id',
            $page
        );
    }

    public function usersLoggableUsersQuery(User $user, ?User $proxyUser = null)
    {
        $asAssistant = $this->assistantsQuery($user, $proxyUser);
        $asDeputy = $this->deputiesQuery($user, $proxyUser);
        $all = $asAssistant->union($asDeputy->getQuery(), true);

        if ($user->isAdmin() === true) {
            $asAdmin = $this->allManageableUsers($user, $proxyUser);
            $all->union($asAdmin->getQuery());
        }

        return $all;
    }

    public function findInstanceUsersWithGroup(Instance $instance, string $groupName): Collection
    {
        $this->prepare();

        return $this
            ->builder
            ->select('users.*')
            ->join('group_user', 'group_user.user_id', '=', 'users.id')
            ->join('groups', 'group_user.group_id', '=', 'groups.id')
            ->where([
                'users.instance_id' => $instance->id,
                'groups.instance_id' => $instance->id,
                'groups.name' => $groupName
            ])
            ->get();
    }

    public function filterNotSuperAdminUsers(Collection $users)
    {
        $superAdminIds = config('vaterval.super_admin_ids');

        return $users->whereNotIn('id', $superAdminIds);
    }

    public function findAllFinanceUsersForCompany(int $companyId): Collection
    {
        $query = DB::raw(
            "SELECT DISTINCT users.* FROM users 
                    LEFT JOIN `group_user` gu ON users.id = gu.user_id
                    LEFT JOIN `groups` g ON gu.group_id = g.id 
                    WHERE company_id = ?
                    AND g.name = ?"
        );

        return User::fromQuery($query, [$companyId, User::GROUP_NAME_FINANCE]);
    }

    public function findAllFinanceUsersForInstance(int $instanceId): Collection
    {
        $query = DB::raw(
            "SELECT DISTINCT users.* FROM users 
                    LEFT JOIN `group_user` gu ON users.id = gu.user_id
                    LEFT JOIN `groups` g ON gu.group_id = g.id 
                    WHERE users.instance_id = ?
                    AND g.name = ?"
        );

        return User::fromQuery($query, [$instanceId, User::GROUP_NAME_FINANCE]);
    }

    /**
     * @return Collection
     */
    public function findAllWithoutWorkLocation(): Collection
    {
        $users =
            (new User())
                ->newQuery()
                ->leftJoin((new Location())->getTable(), function (JoinClause $join) {
                    $join
                        ->on('users.id', '=', 'locations.localizable_id')
                        ->where('locations.localizable_type', '=', 'user')
                        ->where('locations.column', '=', 'work_location');
                })
                ->whereNull('locations.id')
                ->select('users.*')
                ->get();

        return $users;
    }

    public function findUserBySlug(string $slug): ?User
    {
        $authUser = Auth::user();

        $user = User::where('slug', $slug)->first();
        if ($authUser->can('canManageOtherUser', [$authUser, $user])) {
            return $user;
        }

        abort(403, trans('global.access-denied'));
    }

    public function findByRealIdAndInstanceId(int $id, int $instanceId): Model
    {
        $this->prepare();

        $element = $this->builder->where(['id' => $id, 'instance_id' => $instanceId])->first();

        abort_if($element === null, 404, trans('global.item-does-not-exists'));

        $this->authorize('view', $element);
        $this->builder = $this->model::query();
        $this->select = collect();

        return $element;
    }

    public function findUserById(int $id): ?User
    {
        return User::where('id', '=', $id)->first();
    }

    public function findUserByEmail(string $email): ?User
    {
        return User::where('email', $email)->first();
    }

    public function findUserByEmailAndInstanceId(string $email, int $instanceId): ?User
    {
        return User::select(['users.*'])
            ->where([['email', '=', $email], ['instance_id', '=', $instanceId]])
            ->first();
    }

    public function findUserByEmailAndCompanyId(string $email, int $companyId): ?User
    {
        return User::select(['users.*'])
            ->where([['email', '=', $email], ['company_id', '=', $companyId]])
            ->first();
    }

    public function saveWithoutEvents(User $user): User
    {
        User::withoutEvents(function () use ($user) {
            $user->save();
        });

        return $user;
    }

    public function findUserByErpIdAndInstanceId(string $erpId, int $instanceId): ?User
    {
        $this->prepare();

        return $this->builder->where([
            'erp_id' => $erpId,
            'instance_id' => $instanceId
        ])->first();
    }

    public function findUserBySlugAndInstanceSlug(Slug $userSlug, Slug $instanceSlug): ?User
    {
        return User::select(['users.*'])
            ->where([
                    ['users.slug', '=', (string)$userSlug],
                    ['instances.slug', '=', (string)$instanceSlug]
                ]
            )
            ->leftJoin('instances', function (JoinClause $join) {
                $join->on('instances.id', '=', 'users.instance_id');
            })
            ->first();
    }

    public function findByErpIdWithDifferentEmployeeUniqueIdentifier(
        string $erpId,
        array $employeUniqueIdentifier,
        int $instanceId,
        ?int $companyId,
        ?int $userId
    ): ?Collection {
        $where = [
            ['erp_id', '=', $erpId],
            ['instance_id', '=', $instanceId]
        ];

        if ($companyId) {
            $where['company_id'] = $companyId;
        }

        $builder = User::query()
            ->where($where)->whereNotIn('employee_unique_identifier', $employeUniqueIdentifier);

        if ($userId !== null) {
            $builder->where('id', '!=', $userId);
        }

        return $builder->get();
    }

    public function findByHrIdWithDifferentEmployeeUniqueIdentifier(
        string $hrId,
        array $employeeUniqueIdentifier,
        int $instanceId,
        ?int $companyId,
        ?int $userId
    ): ?Collection {
        $where = [
            ['hr_id', '=', $hrId],
            ['instance_id', '=', $instanceId]
        ];

        if ($companyId) {
            $where['company_id'] = $companyId;
        }

        $builder = User::query()
            ->where($where)->whereNotIn('employee_unique_identifier', $employeeUniqueIdentifier);

        if ($userId !== null) {
            $builder->where('id', '!=', $userId);
        }

        return $builder->get();
    }

    public function findByEmailWithDifferentEmployeeUniqueIdentifier(
        string $email,
        array $employeUniqueIdentifier,
        int $instanceId,
        ?int $companyId,
        ?int $userId
    ): ?Collection {
        $where = [
            ['email', '=', $email],
            ['instance_id', '=', $instanceId],
        ];

        if ($companyId) {
            $where['company_id'] = $companyId;
        }

        $builder = User::query()
            ->where($where)
            ->whereNotIn('employee_unique_identifier', $employeUniqueIdentifier);

        if ($userId !== null) {
            $builder->where('id', '!=', $userId);
        }

        return $builder->get();
    }

    public function findUserByEmailAndInstanceSlug(Email $email, Slug $instanceSlug): ?User
    {
        return User::select(['users.*'])
            ->where([
                    ['users.email', '=', (string)$email],
                    ['instances.slug', '=', (string)$instanceSlug]
                ]
            )
            ->leftJoin('instances', function (JoinClause $join) {
                $join->on('instances.id', '=', 'users.instance_id');
            })
            ->first();
    }

    public function checkIsUserExistsAnywhere(Email $email, int $instanceId): bool
    {
        return User::where([
            ['users.email', '=', (string)$email],
            ['users.instance_id', '!=', $instanceId]
        ])
            ->exists();
    }

    public function changePinCode(User $user, string $newPinCode): void
    {
        $user->pin_code = bcrypt($newPinCode);
        $user->bad_pin_code_attempts = 0;
        $user->save();
    }

    public function getTechnicalUser(Instance $instance): User
    {
        $settingsInInstance = $instance->getModuleSettings('technical_user');
        $technicalUserSettings = empty($settingsInInstance) === true
            ? $technicalUserSettings = (array)config('vaterval.technical_user')
            : $settingsInInstance;


        $user = User::where(['email' => $technicalUserSettings['email']])->first();

        if ($user instanceof User) {
            return $user;
        }

        throw TechnicalUserNotConfiguredException::create();
    }

    public function persist(Model $model): Model
    {
        $model->save();

        return $model;
    }

    public function paginateByCriteriaWithActiveInRequests(
        $criteria,
        callable $indexQuery,
        string $countAndGroupBy,
        UsersWithActivityOffsetPageDto $page
    ): Paginator {
        $this->addCriteria($criteria);
        $this->prepare();
        $queryTotal = $indexQuery()->getQuery()->count($countAndGroupBy);
        $this->addCriteria([new RequestPaginationCriterion($page)]);
        $this->prepare();
        $items = $indexQuery()->getQuery()->groupBy([$countAndGroupBy])->get();
        /** @var $items Collection */
        $items->transform(function (\stdClass $item) use ($page) {
            /** @var User $user */
            $user = User::where('id', $item->id)->first();
            $item->active_in_requests = $user
                ->getActiveInRequestsForPeriod($page->getActivityDateFrom(), $page->getActivityDateTo())
                ->transform(function (Request $request) {
                    return [
                        'id' => $request->id,
                        'number' => $request->number,
                        'type' => $request->type,
                        'mpk_code' => $request->mpk_code,
                        'activity_type' => $request->activity_type
                    ];
                });
            return $item;
        });
        $paginator = Paginator::create($items, $queryTotal, $page);
        $this->clearCriteria();

        return $paginator;
    }

    public function getUsersForSync(
        Instance $instance,
        UsersWithActivityOffsetPageDto $page,
        array $extraCriteria = []
    ): Paginator {
        return $this->paginateByCriteriaWithActiveInRequests(
            array_merge_recursive(
                [
                    new InstanceV2Criterion($instance)
                ],
                $extraCriteria
            ),
            function () {
                return $this->usersForSyncQuery();
            },
            'users.id',
            $page
        );
    }

    protected function usersForSyncQuery()
    {
        return $this->builder
            ->selectRaw(
                '
                users.id,
                users.slug,
                users.erp_id,
                users.instance_id,
                users.company_id,
                users.avatar,
                users.email,
                users.first_name,
                users.last_name,
                users.passport_number,
                users.passport_issue_date,
                users.passport_valid_date,
                users.birth_date,
                users.phone,
                users.sex,
                users.internal,
                GROUP_CONCAT(DISTINCT groups.id SEPARATOR \', \') as group_list,
                countries.country_code as citizenship_country_code,
                users.level,
                companies.policy_file as company_policy_file,
                companies.policy_file_token as company_policy_file_token,
                users.blocked_at,
                mpks.code as mpk_code
               '
            )
            ->leftJoin('instances', function ($join) {
                $join->on('instances.id', '=', 'users.instance_id');
            })
            ->leftJoin('countries', function ($join) {
                $join->on('countries.id', '=', 'users.citizenship_id');
            })
            ->leftJoin('companies', function ($join) {
                $join->on('companies.id', '=', 'users.company_id');
            })
            ->leftJoin('mpks', function ($join) {
                $join->on('mpks.id', '=', 'users.mpk_id');
            })
            ->leftJoin('group_user', function ($join) {
                $join->on('group_user.user_id', '=', 'users.id');
            })
            ->leftJoin('groups', function ($join) {
                $join->on('groups.id', '=', 'group_user.group_id');
            })
            ->where('users.deleted_at', null)
            ->where('instances.deleted_at', null);
    }

    public function usersIndexQuery()
    {
        return $this->builder->distinct()->selectRaw(
            '
                    users.id,
                    users.company_id,
                    users.first_name,
                    users.last_name,
                    users.avatar,
                    users.slug,
                    users.email,
                    CONCAT(users.first_name, \' \', users.last_name) as full_name
               '
        );
    }

    public function getUsersForSelect(PageInterface $page, User $user, array $extraCriteria = []): Paginator
    {
        return $this->paginateByCriteriaWhichOutGrouping(
            array_merge_recursive(
                [
                    new SearchPhraseCriterion('', User::CROSS_FIELDS_USER_SEARCH),
                    new InstanceV2Criterion($user->instance),
                    new UserOrderByFullNameCriterion(),
                    new CompanyCriterion(),
                    new ActiveUserCriterion(),
                    new NonInternalUserCriterion($user->internal)
                ],
                $extraCriteria
            ),
            function () {
                return $this->usersIndexQuery();
            },
            'users.id',
            $page
        );
    }

    public function usersCurrentInstanceIndexQuery($page)
    {
        $query = $this->builder->distinct()->selectRaw(
            '
                        users.id,
                        users.first_name,
                        users.last_name,
                        users.avatar,
                        users.slug,
                        users.email,
                        users.blocked_at,
                        CONCAT(users.first_name, \' \', users.last_name) as full_name,
                        supervisors.first_name as supervisor_first_name,
                        supervisors.last_name as supervisor_last_name,
                        CONCAT(supervisors.first_name, \' \', supervisors.last_name) as supervisor_full_name,
                        companies.code as company_code 
                   '
        )
            ->leftJoin('users as supervisors', function ($join) {
                $join->on('users.user_id', '=', 'supervisors.id');
            })
            ->leftJoin('companies', function ($join) {
                $join->on('companies.id', '=', 'users.company_id');
            })
            ->where([
                ['users.id', '!=', Auth::user()->id]
            ])
            ->whereNotIn('users.id', config('vaterval.super_admin_ids'));

        if ($page->getSorter() instanceof Sorter) {
            $query->getQuery()->orders = [];
            $query->orderBy(
                $page->getSorter()->getSortBy(),
                $page->getSorter()->getSortDirection()
            );
        }

        return $query;
    }

    public function getUsersForCurrentInstance(
        PageInterface $page,
        Instance $instance,
        bool $withInternals
    ): PaginatorInterface {
        return $this->paginateEloquentModelsByCriteria(
            [
                new InstanceV2Criterion($instance),
                new ExcludeBillingGroupCriterion(),
                new ExcludeAgentGroupCriterion(),
                new UserOrderByFullNameCriterion(),
                new NonInternalUserCriterion($withInternals)
            ],
            $page
        );
    }

    public function getUsersForCurrentInstanceIndex(
        PageInterface $page,
        Instance $instance,
        bool $withInternals
    ): Paginator {
        $criteria = [
            new InstanceV2Criterion($instance),
            new SearchPhraseCriterion(),
            new CompanyCriterion(),
            new GroupNameCriterion(),
            new ActiveUserCriterion(),
            new ExcludeBillingGroupCriterion(),
            new UserOrderByFullNameCriterion(),
            new LevelCriterion(),
            new GradeCriterion(),
            new MpkCriterion(),
            new NonInternalUserCriterion($withInternals)
        ];

        if ($withInternals === false) {
            $criteria[] = new ExcludeAgentGroupCriterion();
        }

        return $this->paginateByCriteria(
            $criteria,
            function () use ($page) {
                return $this->usersCurrentInstanceIndexQuery($page);
            },
            'users.id',
            $page
        );
    }

    public function existUserWithMpk(Mpk $mpk): bool
    {
        $this->prepare();

        $builder = $this->builder->where(['mpk_id' => $mpk->id]);

        return $builder->count() > 0;
    }

    public function findBySupervisorId(int $id): Collection
    {
        $this->prepare();

        $builder = $this->builder->where(['user_id' => $id]);

        return $builder->get();
    }

    public function getLowResolutionAvatarForUser(User $user, ?bool $cropToCircle = false): StorageServiceDto
    {
        try {
            return resolve(StorageServiceInterface::class)->getRawFileContentByAvatarFileNameAndFileType(
                $user->slug,
                $user->getAvatar(),
                'thumb',
                80,
                80,
                $cropToCircle
            );
        } catch (\Throwable $exception) {
            \Log::error($exception);
            return new StorageServiceDto('file-not-found', 'jpg', '');
        }
    }

    public function getUsersActivitiesForInstanceQuery(Carbon $month, int $instanceId, PageInterface $page): Paginator
    {
        return $this->paginateByCriteria(
            [
                new ExcludeBillingGroupCriterion(),
                new ExcludeAgentGroupCriterion(),
                new NonInternalUserCriterion()
            ],
            function () {
                return $this->builder->distinct()->selectRaw(
                    '
                        users.id,
                        users.slug,
                        users.email,
                        (100) as activeTripRequests,
                        (200) as activeExpenseRequests
                   '
                )
                    ->leftJoin('request as request_active_trips', function ($join) {
                        $join->on('request_acceptors_user.request_id', '=', 'requests.id');
                    })
                    ->whereNotIn('users.id', config('vaterval.super_admin_ids'));
            },
            'users.id',
            $page
        );
    }

    protected function assistantsQuery(User $user, ?User $proxyUser = null): Builder
    {
        $asAssistant = clone $this->builder;
        $asAssistant->distinct()->select([
            'users.id',
            'users.company_id',
            'users.instance_id',
            'users.first_name',
            'users.last_name',
            'users.avatar',
            'users.slug',
            'users.email',
            DB::raw('CONCAT(users.first_name, \' \', users.last_name) as full_name'),
            DB::raw('\'assistant\' as type'),
            DB::raw(
                sprintf(
                    '(CASE WHEN users.id = %s THEN 1 ELSE 0 END) as current',
                    $proxyUser instanceof User ? $proxyUser->id : -1
                )
            )
        ])
            ->from('users')
            ->leftJoin('user_assistants', function ($join) {
                $join->on('user_assistants.user_id', '=', 'users.id');
            })
            ->where('user_assistants.assistant_id', '=', $user->id)
            ->where('users.id', '!=', $user->id);

        return $asAssistant;
    }

    protected function deputiesQuery(User $user, ?User $proxyUser = null): Builder
    {
        $asDeputy = clone $this->builder;
        $asDeputy->distinct()->select([
            'users.id',
            'users.company_id',
            'users.instance_id',
            'users.first_name',
            'users.last_name',
            'users.avatar',
            'users.slug',
            'users.email',
            DB::raw('CONCAT(users.first_name, \' \', users.last_name) as full_name'),
            DB::raw('\'deputy\' as type'),
            DB::raw(
                sprintf(
                    '(CASE WHEN users.id = %s THEN 1 ELSE 0 END) as current',
                    $proxyUser instanceof User ? $proxyUser->id : -1
                )
            )
        ])
            ->from('users')
            ->leftJoin('user_deputies', function ($join) {
                $join->on('user_deputies.user_id', '=', 'users.id');
            })
            ->where('user_deputies.deputy_id', '=', $user->id)
            ->where('user_deputies.from', '<=', Carbon::now()->toDateTimeString())
            ->where('user_deputies.to', '>=', Carbon::now()->toDateTimeString())
            ->where('users.id', '!=', $user->id);

        return $asDeputy;
    }

    protected function allManageableUsers(User $user, ?User $proxyUser = null): Builder
    {
        $asAdmin = clone $this->builder;
        $asAdmin = $asAdmin->select([
            'users.id',
            'users.company_id',
            'users.instance_id',
            'users.first_name',
            'users.last_name',
            'users.avatar',
            'users.slug',
            'users.email',
            DB::raw('CONCAT(users.first_name, \' \', users.last_name) as full_name'),
            DB::raw('\'admin\' as type'),
            DB::raw(
                sprintf(
                    '(CASE WHEN users.id = %s THEN 1 ELSE 0 END) as current',
                    $proxyUser instanceof User ? $proxyUser->id : -1
                )
            )
        ])
            ->where('users.id', '!=', $user->id)
            ->whereNotIn('users.id', config('vaterval.super_admin_ids'));

        return $asAdmin;
    }

    public function getUserBySlug(string $slug): User
    {
        $user = User::where('slug', $slug)->first();

        if ($user instanceof User) {
            return $user;
        }

        throw UserNotFoundException::create();
    }

    public function getUsersBySlugsAndInstanceId(array $slugs, int $instanceId): Collection
    {
        return User::whereIn('slug', $slugs)
            ->where('instance_id', $instanceId)
            ->get();
    }

    public function markUserAsSyncedBySlugAndInstanceId(string $slug, int $instanceId): void
    {
        $user = User::where([
            ['slug', '=', $slug],
            ['instance_id', $instanceId]
        ])->first();

        if (($user instanceof User) === false) {
            throw UserNotFoundException::create();
        }

        $user->last_synced_at = Carbon::now();
        //Prevent infinity loop in UserObserver.
        $user->saveWithoutEvents();
    }

    public function findUserWithPermission(string $permissionName, Company $company): ?User
    {
        $this->prepare();

        $instanceId = $company->instance_id;

        $users = $this->builder
            ->select('users.*')
            ->leftJoin('group_user', 'users.id', '=', 'group_user.user_id')
            ->leftJoin('permissions', function (JoinClause $join) use ($instanceId) {
                $join->on('group_user.group_id', '=', 'permissions.group_id');
                $join->where('permissions.instance_id', '=', $instanceId);
            })
            ->where([
                'users.instance_id' => $instanceId,
                'permissions.ability' => $permissionName,
                'permissions.can' => 1,
            ])
            ->get();

        $theSameCompanyUsers = $users->where('company_id', '=', $company->id);

        if ($theSameCompanyUsers->isNotEmpty()) {
            $users = $theSameCompanyUsers;
        }

        if ($users->isEmpty()) {
            return null;
        }

        return $users->random(1)->first();
    }

    public function findBySlugsAndInstanceId(array $slugs, int $instanceId): Collection
    {
        return $this->builder->whereIn('slug', $slugs)
            ->where('instance_id', $instanceId)
            ->get();
    }
}
