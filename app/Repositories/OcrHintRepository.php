<?php


namespace App\Repositories;


use App\Document;
use App\Ocr\DTO\OcrPurchaserDto;
use App\Ocr\Enum\ColumnNameEnum;
use App\Ocr\OcrHint;
use App\Rules\ProviderNipUniqueInCurrentInstance;
use Carbon\Carbon;
use Modules\Accounting\Priv\Modules\Provider\Exceptions\InvalidNipException;
use Modules\Accounting\Priv\Modules\Provider\ValueObjects\Slug;
use Modules\Accounting\Pub\Facades\ProviderRecognitionFacade;
use Modules\Accounting\Pub\Modules\Provider\Dtos\ProviderCollectionDto;
use Modules\Accounting\Pub\Modules\Provider\Dtos\ProviderDto;
use Modules\Accounting\Pub\Modules\Provider\Dtos\ProviderSearchDto;
use Modules\Accounting\Pub\Modules\Provider\Enums\SourceEnum;

class OcrHintRepository extends Repository
{
    private const OCR_CONFIG_KEY = 'ocr.%s.responseStructure.%s';

    private $ocrResponse;

    private Document $document;


    private ProviderRepository $providerRepo;

    private CountryRepository $countryRepository;

    private ProviderRecognitionFacade $providerRecognitionFacade;

    private array $basicDocumentFields = [];
    private array $sellerFields = [];
    private array $purchaserFields = [];

    private array $basicInfo = [];
    private array $sellerInfo = [];
    private ?OcrPurchaserDto $ocrPurchaserDto;

    private bool $retryOcrProcess = true;

    private string $driver;

    static function model(): string
    {
        return OcrHint::class;
    }

    public function __construct()
    {
        $this->driver = config('ocr.driver');

        parent::__construct();

        $this->providerRepo = resolve(ProviderRepository::class);
        $this->basicDocumentFields = config(sprintf(self::OCR_CONFIG_KEY, $this->driver, 'basic'), []);
        $this->sellerFields = config(sprintf(self::OCR_CONFIG_KEY, $this->driver, 'seller'), []);
        $this->purchaserFields = config(sprintf(self::OCR_CONFIG_KEY, $this->driver, 'purchaser'), []);

        $this->countryRepository = resolve(CountryRepository::class);
        $this->providerRecognitionFacade = resolve(ProviderRecognitionFacade::class);
    }

    public function fieldsFor(string $driver): self
    {
        $repo = clone $this;
        $repo->driver = $driver;

        $repo->basicDocumentFields = config(sprintf(self::OCR_CONFIG_KEY, $driver, 'basic'), []);
        $repo->sellerFields = config(sprintf(self::OCR_CONFIG_KEY, $driver, 'seller'), []);
        $repo->purchaserFields = config(sprintf(self::OCR_CONFIG_KEY, $driver, 'purchaser'), []);

        return $repo;
    }

    public function setOcrResponse($ocrResponse): void
    {
        if (is_string($ocrResponse)) {
            $this->ocrResponse = json_decode($ocrResponse, true);
        } else {
            $this->ocrResponse = $ocrResponse;
        }
    }

    public function parseOcrResponse(Document $document, $response): self
    {
        $this->setOcrResponse($response);
        $this->setDocument($document);
        $this->setBasicInfo();
        $this->setSellerInfo();
        $this->setPurchaserInfo();
        $this->setRetryOcrProcess();

        return $this;
    }

    public function getOcrRetry(): bool
    {
        return $this->retryOcrProcess;
    }

    public function getIssueDate(): ?string
    {
        $date = $this->findInOcrResponse($this->basicDocumentFields['issue_date']);

        if (empty($date) === false && preg_match('/\d{4}\-\d{2}-\d{2}/', $date)) {
            try {
                $date = new Carbon($date);
                return $date->toDateString();
            } catch (\Throwable $exception) {
                return null;
            }
        }

        return null;
    }

    public function saveHints(): void
    {
        if ($this->document->type === Document::TYPE_ACCOUNTING) {
            $this->saveBasicInfoHints();
            //HINTS
            $nip = preg_replace('/[^0-9]/', "", strtoupper($this->sellerInfo['seller_nip'] ?? ''));

            if (empty($nip) === false) {
                $this->advancedProviderHints($nip);
            }

            $this->savePurchaserHints();
        }
    }

    public function hintBelongsToDocument(OcrHint $hint, $document): bool
    {
        return $hint->document_id == $document->id;
    }

    public function acceptProvidedSuggestionForDocument(Document $document): void
    {
        $this->prepare();

        $this->builder->where([
            'document_id' => $document->id,
            'instance_id' => $document->instance_id,
            'column' => ColumnNameEnum::PROVIDER_SUGGESTED()->getValue()
        ])->update(['accepted' => true]);
    }

    protected function createRules(): array
    {
        return [
            'column' => 'required|string|max:255',
            'value' => 'required|string|max:255',
            'label' => 'required|string|max:255',
            'params' => '',
            'accepted' => 'boolean',
        ];
    }

    protected function updateRules($data = []): array
    {
        return [
            'accepted' => 'boolean',
        ];
    }

    protected function allowedWith(): array
    {
        return [
            'document',
        ];
    }

    protected function beforeCreate($model, $data)
    {
        $model->instance_id = $this->document->instance_id;
        $model->document_id = $this->document->id;

        return $model;
    }

    private function setRetryOcrProcess(): void
    {
        foreach ($this->basicInfo as $info) {
            if ($info) {
                $this->retryOcrProcess = false;
                break;
            }
        }
    }

    private function saveBasicInfoHints(): void
    {
        foreach ($this->basicInfo as $column => $value) {
            if (!empty($value)) {
                $this->create(['column' => $column, 'value' => $value, 'label' => $value]);
            }
        }
    }

    private function advancedProviderHints(string $nip): void
    {
        /** @var ProviderCollectionDto $providersCollectionDto */
        $providersCollectionDto = $this->providerRecognitionFacade->resolve(
            new ProviderSearchDto($nip, $this->document->instance->slug)
        );

        if ($providersCollectionDto->isNotEmpty() === true) {
            $this->saveProviderHints($providersCollectionDto);
        }
    }

    private function saveProviderHints(ProviderCollectionDto $providerCollectionDto): void
    {
        /** @var ProviderDto $providerDto */
        foreach ($providerCollectionDto as $providerDto) {
            $label = $this->createLabelFromProviderDto($providerDto);

            if ($providerDto->getSlug() instanceof Slug) {
                $provider = $this->providerRepo->findBySlug((string)$providerDto->getSlug());
                $this->create([
                    'column' => 'provider_id',
                    'value' => (string)$provider->id,
                    'label' => $label,
                    'params' => [
                        'name' => $providerDto->getName()
                    ],
                    'accepted' => true
                ]);

                $this->document->provider_id = $provider->id;
                $this->document->save();

                return;
            } elseif ($providerDto->getSource()->equals(SourceEnum::GUS())) {
                $validator = \Validator::make(
                    [
                        'instance_id' => $this->document->instance_id,
                        'registry_number' => (string)$providerDto->getNip(),
                        'name' => $providerDto->getName(),
                    ],
                    $this->providerRepo->minimalRulesToCreateProvider(
                        resolve(ProviderNipUniqueInCurrentInstance::class)->setCurrentInstance(
                            $this->document->instance
                        )
                    )
                );

                if ($validator->fails() === false) {
                    $this->createFromProviderDto($providerDto);

                    return;
                }
            }
        }
    }

    private function savePurchaserHints(): void
    {
        if ($this->ocrPurchaserDto === null) {
            return;
        }

        try {
            $this->create([
                'column' => ColumnNameEnum::PURCHASER_SUGGESTED()->getValue(),
                'value' => '0',
                'label' => $this->ocrPurchaserDto->generateLabelForPurchaser(),
                'params' => [
                    'name' => $this->ocrPurchaserDto->getName(),
                    'registry_number' => $this->ocrPurchaserDto->getRegistryNumber(),
                    'address' => $this->ocrPurchaserDto->getAddress(),
                    'postcode' => $this->ocrPurchaserDto->getPostCode(),
                    'country_id' => $this->countryRepository->getCountryByCode($this->ocrPurchaserDto->getCountryCode())->id,
                    'city' => $this->ocrPurchaserDto->getCity()
                ],
                'accepted' => true
            ]);
        } catch (\ErrorException $e) {
            report($e);
        }
    }

    private function setBasicInfo(): void
    {
        foreach ($this->basicDocumentFields as $field => $ocrPath) {
            $fnName = 'get' . studly_case($field);
            if (method_exists($this, $fnName)) {
                $this->basicInfo[$field] = $this->$fnName();
            } else {
                $this->basicInfo[$field] = $this->findInOcrResponse($ocrPath);
            }
        }
    }

    private function setSellerInfo(): void
    {
        foreach ($this->sellerFields as $field => $ocrPath) {
            $this->sellerInfo['seller_' . $field] = $this->findInOcrResponse($ocrPath);
        }
    }

    private function setPurchaserInfo(): void
    {
        $purchaserData = [];

        foreach ($this->purchaserFields as $field => $ocrPath) {
            $purchaserData[$field] = $this->findInOcrResponse($ocrPath);
        }

        try {
            $this->ocrPurchaserDto = OcrPurchaserDto::fromArray($purchaserData);
        } catch (InvalidNipException $exception) {
            $this->ocrPurchaserDto = null;
        }
    }

    private function setDocument(Document $document): void
    {
        $this->document = $document;
    }

    private function findInOcrResponse(string $name): ?string
    {
        $path = explode('.', $name);

        return $this->isValid($name) ? $this->findInPath($path, null) : null;
    }

    private function findInPath(array $path, ?bool $defaultVal)
    {
        $result = $this->ocrResponse;

        foreach ($path as $index) {
            if (!isset($result[$index])) {
                return $defaultVal;
            }

            $result = $result[$index];
        }

        return $result;
    }

    private function isValid(string $path): bool
    {
        if ($this->isLegacyDriver() === false) {
            return true;
        }

        if (($path === config(sprintf('ocr.%s.responseStructure.basic.gross', config('ocr.driver')))
                && is_numeric($this->findInPath(explode('.', $path), false)) === true
            )
            || config('ocr.driver') == 'fake'
        ) {
            return true;
        }

        $path = explode('.', $path);
        array_pop($path);
        $path[] = 'is_valid';

        return $this->findInPath($path, false);
    }

    private function createFromProviderDto(ProviderDto $providerDto): void
    {
        $label = $this->createLabelFromProviderDto($providerDto);

        $this->create([
            'column' => ColumnNameEnum::PROVIDER_SUGGESTED()->getValue(),
            'value' => '0',
            'label' => $label,
            'params' => [
                'name' => $providerDto->getName(),
                'registry_number' => (string)$providerDto->getNip(),
                'address' => $providerDto->getAddress(),
                'postcode' => $providerDto->getPostCode(),
                'country_id' => $this->countryRepository->getCountryByCode($providerDto->getCountryCode())->id,
                'source' => (string)$providerDto->getSource(),
                'city' => (string)$providerDto->getCity()
            ]
        ]);
    }

    private function createLabelFromProviderDto(ProviderDto $providerDto): string
    {
        return sprintf('%s (%s)', $providerDto->getName(), (string)$providerDto->getNip());
    }

    private function isLegacyDriver(): bool
    {
        return $this->document->useLegacyOcr();
    }
}
