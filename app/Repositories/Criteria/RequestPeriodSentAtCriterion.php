<?php

namespace App\Repositories\Criteria;

use App\Repositories\Repository;
use App\Repositories\RequestRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class RequestPeriodSentAtCriterion extends Criterion
{
    protected $startDate;

    protected $endDate;

    public function __construct($periodStart = null, $periodEnd = null)
    {
        $this->startDate = $periodStart;
        $this->endDate = $periodEnd;
    }

    public function supportedRepositories()
    {
        return [
            RequestRepository::class,
        ];
    }

    public function make(Builder $builder, Repository $repository)
    {
        if (request()->has('period_start')) {
            $this->startDate = request()->get('period_start');
        } else {
            $this->startDate = '0000-00-00 00:00:00';
        }

        if (request()->has('period_end')) {
            $this->endDate = request()->get('period_end');
        } else {
            $this->endDate = '9999-01-01 00:00:00';
        }

        if (! $this->startDate && ! $this->endDate) {
            return $builder;
        }

        if (! ($this->startDate && $this->endDate)) {
            abort(400, trans('error.incorrect-period-two-parameters-required'));
        }

        try {
            $this->startDate = (new Carbon($this->startDate))->startOfDay();
            $this->endDate = (new Carbon($this->endDate))->endOfDay();
        } catch (\Exception $e) {
            abort(400, trans('error.incorrect-date-format'));
        }

        abort_unless(
            $this->startDate->lessThan($this->endDate),
            422,
            trans('error.end-date-must-be-greater-than-start-date')
        );

        $builder->where(function (Builder $query) {
            $query->whereBetween('sent_at', [$this->startDate, $this->endDate]);
        });

        return $builder;
    }
}
