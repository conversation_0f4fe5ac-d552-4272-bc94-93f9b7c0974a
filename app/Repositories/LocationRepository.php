<?php


namespace App\Repositories;


use App\Country;
use App\Exceptions\LocationValidationException;
use App\Location;
use App\Services\Country\CountryService;
use Illuminate\Validation\ValidationException as DefaultValidationException;

/**
 * @method Location create($data)
 *
 * Class LocationRepository
 * @package App\Repositories
 */
class LocationRepository extends Repository
{

    static function model()
    {
        return Location::class;
    }

    public $eventsEnabled = false;

    protected function createRules()
    {
        return [
            'country' => 'string|max:255',
            'instance_id' => 'integer',
            'city' => 'string|max:255',
            'province' => 'string|nullable|max:255',
            'country_code' => 'string|max:255',
            'address' => 'string|nullable|max:255',
            'long' => 'numeric|nullable',
            'lat' => 'numeric|nullable',
            'name' => 'string|nullable|max:255',
            'formatted_address' => 'required|string',
            'additional_data' => 'array|nullable',
            'additional_data.formatted' => 'nullable|string',
            'column' => 'string|nullable|max:255',
            'localizable_id' => 'integer|nullable',
            'localizable_type' => 'string|nullable',
        ];
    }

    protected function updateRules($data = [])
    {
        return [
            'country' => 'string|max:255',
            'city' => 'string|max:255',
            'province' => 'string|nullable|max:255',
            'country_code' => 'string|max:255',
            'address' => 'string|nullable|max:255',
            'long' => 'numeric|nullable',
            'lat' => 'numeric|nullable',
            'name' => 'string|nullable|max:255',
            'formatted_address' => 'required|string|max:255',
            'additional_data' => 'array|nullable',
            'additional_data.formatted' => 'nullable|string',
        ];
    }

    protected function beforeCreate(Location $model, $data)
    {
        $model->instance_id = $data['instance_id'];

        return $model;
    }


    public function createLocationForRequestElement($data, $id, $type, $column, int $instanceId)
    {
        $data['localizable_id'] = $id;
        $data['localizable_type'] = $type;
        $data['column'] = $column;
        $data['instance_id'] = $instanceId;
        $location = $this->create($data);

        return $location;
    }

    public function makeLocationForRequestElement($data, $id, $type, $column, int $instanceId)
    {
        $data['localizable_id'] = $id;
        $data['localizable_type'] = $type;
        $data['column'] = $column;
        $data['instance_id'] = $instanceId;

        return new Location($data);
    }

    public static function getCountryFromLocation(?Location $location, ?string $defaultCountryCode = 'PL'): Country
    {
        return resolve(CountryService::class)->getCountryByCode($location !== null ? $location->country_code : $defaultCountryCode);
    }

    public function validate(array $input, array $rules,
        array $messages = [], array $customAttributes = [])
    {
        $validator = $this->getValidationFactory()
            ->make($input, $rules, $messages, $customAttributes);
        try {
            $validator->validate();

            return $this->extractInputFromRules(collect($input), $rules)->toArray();
        } catch (DefaultValidationException $exception) {
            throw new LocationValidationException($validator);
        }
    }
}
