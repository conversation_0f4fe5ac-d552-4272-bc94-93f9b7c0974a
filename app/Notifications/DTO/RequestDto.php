<?php

declare(strict_types=1);

namespace App\Notifications\DTO;

use App\Request;

class RequestDto
{
    /** @var int */
    public $id;
    /**
     * @var string
     */
    public $userFullName;
    /**
     * @var string
     */
    public $name;
    /**
     * @var \Carbon\Carbon
     */
    public $tripStarts;
    /**
     * @var \Carbon\Carbon
     */
    public $tripEnds;
    /**
     * @var string
     */
    public $mpkCode;
    /**
     * @var string
     */
    public $requestSlug;
    /**
     * @var string
     */
    public $requestType;
    /**
     * @var string
     */
    public $purpose;
    /**
     * @var string
     */
    public $route;
    /**
     * @var \Carbon\Carbon
     */
    public $date;

    public ?string $number;
    public ?string $requestFrontendUrl;

    public function __construct(Request $request)
    {
        $this->id = $request->slug;
        $this->userFullName = $request->user->full_name;
        $this->name = $request->getRequestNameShort();
        $this->tripStarts = $request->getTripStartsFormatted();
        $this->tripEnds = $request->getTripEndsFormatted();
        $this->mpkCode = $request->mpk->code;
        $this->requestSlug = $request->slug;
        $this->requestType = $request->type;
        $this->purpose = $request->purpose;
        $this->route = $request->name_short;
        $this->date = $request->trip_date;
        $this->number = $request->number;
        $this->requestFrontendUrl = $request->getFrontendUrl();
    }
}