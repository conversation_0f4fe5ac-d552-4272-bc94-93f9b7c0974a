<?php

namespace App\Notifications\Mail;

use App\Repositories\RequestRepository;
use App\Request;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RequestAutoReservationFailedForSupportMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * @var string $slug
     */
    private $requestSlug;

    public function __construct(string $requestSlug)
    {
        $this->requestSlug = $requestSlug;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function build()
    {
        /** @var Request $request */
        $request = resolve(RequestRepository::class)->findById($this->requestSlug);
        return $this->view('mail.notification.request-auto-reservation-failed-for-support-mail',
            [
                'user' => $request->user->getFullNameAttribute(),
                'email' => $request->user->email,
                'url' =>  $request->getFrontendUrl(),
            ]);
    }
}
