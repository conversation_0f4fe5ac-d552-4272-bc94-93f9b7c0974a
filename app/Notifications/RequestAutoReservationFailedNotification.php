<?php


namespace App\Notifications;


use App\Mail\NotificationMail;
use App\Repositories\RequestRepository;
use App\Request;
use App\Traits\Notifications\NotifiablePreferences;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\SlackAttachment;
use Illuminate\Notifications\Messages\SlackMessage;
use Illuminate\Queue\SerializesModels;

class RequestAutoReservationFailedNotification extends Notification implements ShouldQueue
{
    use Queueable;
    use NotifiablePreferences;
    use SerializesModels;

    const NAME = 'notification.request-auto-reservation-failed-notification';

    const TYPE = 'RequestAutoReservationFailedNotification';

    const NOTIFICATION_LEVEL = 'info';

    /**
     * @var string $slug
     */
    private $slug;

    public function __construct(string $slug)
    {
        $this->slug = $slug;
        $this->onQueue(config('vaterval.notifications.async_notification_queue'));
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage($this->prepareArray($notifiable));
    }

    public function toSMS($notifiable)
    {
        return [];
    }

    public function toMail($notifiable)
    {
        /** @var Request $request */
        $request = resolve(RequestRepository::class)->findById($this->slug);
        $requestName = trim(str_replace(trans('request.trip-to'), '', $request->getNameAttribute($request->user->locale)));
        return (new NotificationMail($notifiable))->greeting(trans('notification-email.hello'))
            ->line(
                trans(
                    'notification-email.notification.request-auto-reservation-failed-message',
                    [
                        'name' => $requestName,
                        'user' => $request->user->full_name
                    ]
                )
            )
            ->action(trans('notification-email.action'), $request->getFrontendUrl())
            ->subject(trans('notification-email.notification.request-auto-reservation-failed-subject', ['name' => $requestName]));
    }

    public function toSlack(User $user): SlackMessage
    {
        $request = resolve(RequestRepository::class)->findById($this->slug);
        $requestName = trim(str_replace(trans('request.trip-to'), '', $request->getNameAttribute($request->user->locale)));
        return $this->createSlackMessage($user)
            ->attachment(function (SlackAttachment $attachment) use ($request, $requestName) {
                $attachment->title(trans('notification-email.notification.request-auto-reservation-failed-subject', ['name' => $requestName]))
                    ->fields(MindentoSlackMessage::fieldsForRequest($request))
                    ->action(trans('notification-email.action'), $request->getFrontendUrl())
                ->content(trans(
                    'notification-email.notification.request-auto-reservation-failed-message',
                    [
                        'name' => $requestName,
                        'user' => $request->user->full_name
                    ]
                ));
            });
    }

    public function toDatabase($notifiable)
    {
        return $this->prepareArray($notifiable);
    }

    private function prepareArray($notifiable)
    {
        $request = resolve(RequestRepository::class)->findById($this->slug);
        return [
            'data' => [
                'name' => static::getName(),
                'notificationType' => static::TYPE,
                'request_id' => $request->slug,
                'request_type' => $request->type,
                'purpose' => $request->purpose,
                'level' => static::NOTIFICATION_LEVEL,
                'translationParams' => [
                    'name_short' => $request->name_short,
                    'date' => $request->trip_date,
                ],
            ],
            'read_at' => null,
            'request_id_slug' => $request->slug
        ];
    }
}