<?php

namespace App;

use App\ACL\Services\AuthorizationCheckerService;
use App\Collections\LocalizableTripElementsCollection;
use App\ElasticSearchConfigurators\RequestIndexConfigurator;
use App\ElasticSearchConfigurators\Rules\RequestAccountingMultiMatchRule;
use App\ElasticSearchConfigurators\Rules\RequestExpenseMultiMatchRule;
use App\ElasticSearchConfigurators\Rules\RequestInvoiceMultiMatchRule;
use App\ElasticSearchConfigurators\Rules\RequestTripMultiMatchRule;
use App\Enum\AvailableLanguagesEnum;
use App\Interfaces\Models\SearchWithRulesInterface;
use App\Interfaces\RequestElementInterface;
use App\Repositories\InstallmentRepository;
use App\Services\AccountingBalance\AccountingBalanceService;
use App\Services\Balance\BalanceService;
use App\Services\Cache;
use App\Services\RequestCourse\Location\LocationElement;
use App\Services\RequestCourse\RequestCourseService;
use App\Services\Subsummary\SubsummaryService;
use App\Services\Summary\SummaryService;
use App\Traits\AccountingUserTrait;
use App\Traits\CompanyTrait;
use App\Traits\InstanceTrait;
use App\Traits\UserTrait;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Modules\Accounting\Priv\Dtos\ErpAccountedSettlementDto;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Entities\Traits\MpkTrait;
use Modules\Accounting\Priv\Enum\DeductibilityEnum;
use Modules\Analytics\Priv\Entities\Project;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountingAccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountingMileageAllowanceAccountDimensionItem;
use Modules\Analytics\Priv\Entities\Traits\ProjectTrait;
use Modules\Common\Entities\BlameableInterface;
use Modules\Common\Entities\Traits\BlameableTrait;
use Modules\Common\ValueObjects\Slug;
use Modules\TripPlanner\Priv\Entities\RequestTraveler;
use ScoutElastic\Builders\FilterBuilder;
use ScoutElastic\Builders\SearchBuilder;
use ScoutElastic\Searchable;

/**
 * App\Request
 *
 * @property int $id
 * @property string|null $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int $instance_id
 * @property int $company_id
 * @property int|null $mpk_id
 * @property int|null $project_id
 * @property int $user_id
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $accounting_user_id
 * @property string|null $annotation
 * @property string $slug
 * @property string $type
 * @property bool $private
 * @property string $status
 * @property string|null $purpose
 * @property string|null $name
 * @property bool $use_prepaid
 * @property \Illuminate\Support\Carbon|null $status_settlement_at
 * @property \Illuminate\Support\Carbon|null $status_transferred_at
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property \Illuminate\Support\Carbon|null $applicated_at
 * @property \Illuminate\Support\Carbon|null $settled_at
 * @property \Illuminate\Support\Carbon|null $accounted_at
 * @property \Illuminate\Support\Carbon|null $erp_accounted_at
 * @property \Illuminate\Support\Carbon|null $erp_vat_at
 * @property string|null $erp_id
 * @property string $erp_message
 * @property \Illuminate\Support\Carbon|null $finished_at
 * @property \Illuminate\Support\Carbon|null $trip_starts
 * @property \Illuminate\Support\Carbon|null $trip_ends
 * @property \Illuminate\Support\Carbon|null $unrealized_at
 * @property string $request_element_sum_amount
 * @property string|null $request_element_accounted_sum_amount
 * @property string|null $travel_expense_cache
 * @property bool $lump_sum_settled
 * @property bool $lump_sum_accounted
 * @property string|null $settled_amount_before_accounting
 * @property int|null $mileage_allowance_debit_account_id
 * @property int|null $mileage_allowance_credit_account_id
 * @property int|null $mileage_allowance_mpk_id
 * @property int|null $mileage_allowance_project_id
 * @property bool $mileage_allowance_cost_of_earning
 * @property DeductibilityEnum $mileage_allowance_deductibility
 * @property bool $national_trip
 * @property bool $national_target
 * @property string $target_points_text
 * @property \Illuminate\Support\Collection|null $requested_travel_expenses
 * @property string|null $mileage_allowance_amount
 * @property bool $border_crossing_state
 * @property bool $autoaccepted
 * @property bool $auto_reserved
 * @property bool $settlement_auto_accepted
 * @property int $has_broken_rules
 * @property bool $delegation
 * @property bool $show_other_costs
 * @property bool $show_installment
 * @property string|null $delegation_changed_at
 * @property string|null $requested_lump_sums
 * @property \Illuminate\Support\Carbon|null $completion_date
 * @property \Illuminate\Support\Carbon|null $report_trip_starts
 * @property \Illuminate\Support\Carbon|null $report_trip_ends
 * @property bool $periodic
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $acceptors
 * @property-read int|null $acceptors_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\AccessLumpSum[] $accessLumpSums
 * @property-read int|null $access_lump_sums_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestAccommodationDriveLumpSum[] $accommodationDriveLumpSums
 * @property-read int|null $accommodation_drive_lump_sums_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Accomodation[] $accomodations
 * @property-read int|null $accomodations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|RequestAccountDimensionItem[] $accountDimensionItems
 * @property-read int|null $account_dimension_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|RequestAccountingAccountDimensionItem[] $accountingAccountDimensionItems
 * @property-read int|null $accounting_account_dimension_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $accountingDocuments
 * @property-read int|null $accounting_documents_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestAccountingMileageAllowance[] $accountingMileageAllowances
 * @property-read int|null $accounting_mileage_allowances_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestAccountingTravelExpenses[] $accountingTravelExpenses
 * @property-read int|null $accounting_travel_expenses_count
 * @property-read \App\User|null $accountingUser
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestBorderCrossing[] $borderCrossings
 * @property-read int|null $border_crossings_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\BusTrip[] $busTrips
 * @property-read int|null $bus_trips_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Comment[] $comments
 * @property-read int|null $comments_count
 * @property-read \App\Company $company
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\CompanyCarTrip[] $companyCarTrips
 * @property-read int|null $company_car_trips_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Cost[] $costs
 * @property-read int|null $costs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $countableDocuments
 * @property-read int|null $countable_documents_count
 * @property-read \App\User|null $createdBy
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $documents
 * @property-read int|null $documents_count
 * @property-read \App\Location|null $endLocation
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FerryBoatTrip[] $ferryBoatTrips
 * @property-read int|null $ferry_boat_trips_count
 * @property \ScoutElastic\Highlight|null $highlight
 * @property-read mixed $name_short
 * @property-read string $state
 * @property-read string $trip_date
 * @property-read mixed $uid
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Installment[] $installments
 * @property-read int|null $installments_count
 * @property-read \App\Instance $instance
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestMealDeduction[] $mealDeductions
 * @property-read int|null $meal_deductions_count
 * @property-read AccountingAccount|null $mileageAllowanceAccountingAccount
 * @property-read Mpk|null $mileageAllowanceMpk
 * @property-read Project|null $mileageAllowanceProject
 * @property-read \Illuminate\Database\Eloquent\Collection|RequestAccountingMileageAllowanceAccountDimensionItem[] $mileageAllowanceSummaryAccountDimensionItems
 * @property-read int|null $mileage_allowance_summary_account_dimension_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestMileageAllowance[] $mileageAllowances
 * @property-read int|null $mileage_allowances_count
 * @property-read AccountingAccount|null $mileageAllowancesAccountingAccount
 * @property-read AccountingAccount|null $mileageAllowancesCreditAccount
 * @property-read Mpk|null $mileageAllowancesMpk
 * @property-read Mpk|null $mpk
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Offer[] $offers
 * @property-read int|null $offers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\PlaneTrip[] $planeTrips
 * @property-read int|null $plane_trips_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\PrivateAccomodation[] $privateAccomodations
 * @property-read int|null $private_accomodations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\PrivateCarTrip[] $privateCarTrips
 * @property-read int|null $private_car_trips_count
 * @property-read Project|null $project
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RentedCarTrip[] $rentedCarTrips
 * @property-read int|null $rented_car_trips_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestCache[] $requestCaches
 * @property-read int|null $request_caches_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestChange[] $requestChanges
 * @property-read int|null $request_changes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $settlementAcceptors
 * @property-read int|null $settlement_acceptors_count
 * @property-read \App\Location|null $startLocation
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\TargetPoint[] $targetPoints
 * @property-read int|null $target_points_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\TrainTrip[] $trainTrips
 * @property-read int|null $train_trips_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestMealDeduction[] $travelExpenses
 * @property-read int|null $travel_expenses_count
 * @property-read \Illuminate\Database\Eloquent\Collection|RequestTraveler[] $travelers
 * @property-read int|null $travelers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $unmarkedDocuments
 * @property-read int|null $unmarked_documents_count
 * @property-read \App\UnrequestedElement|null $unrequestedElement
 * @property-read \App\User|null $updatedBy
 * @property-read \App\User $user
 * @method static Builder|Request newModelQuery()
 * @method static Builder|Request newQuery()
 * @method static \Illuminate\Database\Query\Builder|Request onlyTrashed()
 * @method static Builder|Request query()
 * @method static Builder|Request userCanRead(\App\User $user)
 * @method static Builder|Request whereAccountedAt($value)
 * @method static Builder|Request whereAccountingUserId($value)
 * @method static Builder|Request whereAnnotation($value)
 * @method static Builder|Request whereApplicatedAt($value)
 * @method static Builder|Request whereAutoReserved($value)
 * @method static Builder|Request whereAutoaccepted($value)
 * @method static Builder|Request whereBorderCrossingState($value)
 * @method static Builder|Request whereCanceledAt($value)
 * @method static Builder|Request whereCompanyId($value)
 * @method static Builder|Request whereCompletionDate($value)
 * @method static Builder|Request whereCreatedAt($value)
 * @method static Builder|Request whereCreatedBy($value)
 * @method static Builder|Request whereDelegation($value)
 * @method static Builder|Request whereDelegationChangedAt($value)
 * @method static Builder|Request whereDeletedAt($value)
 * @method static Builder|Request whereErpAccountedAt($value)
 * @method static Builder|Request whereErpId($value)
 * @method static Builder|Request whereErpMessage($value)
 * @method static Builder|Request whereErpVatAt($value)
 * @method static Builder|Request whereFinishedAt($value)
 * @method static Builder|Request whereHasBrokenRules($value)
 * @method static Builder|Request whereId($value)
 * @method static Builder|Request whereInstanceId($value)
 * @method static Builder|Request whereLumpSumAccounted($value)
 * @method static Builder|Request whereLumpSumSettled($value)
 * @method static Builder|Request whereMileageAllowanceAmount($value)
 * @method static Builder|Request whereMileageAllowanceCostOfEarning($value)
 * @method static Builder|Request whereMileageAllowanceCreditAccountId($value)
 * @method static Builder|Request whereMileageAllowanceDebitAccountId($value)
 * @method static Builder|Request whereMileageAllowanceDeductibility($value)
 * @method static Builder|Request whereMileageAllowanceMpkId($value)
 * @method static Builder|Request whereMileageAllowanceProjectId($value)
 * @method static Builder|Request whereMpkId($value)
 * @method static Builder|Request whereName($value)
 * @method static Builder|Request whereNationalTarget($value)
 * @method static Builder|Request whereNationalTrip($value)
 * @method static Builder|Request whereNumber($value)
 * @method static Builder|Request wherePeriodic($value)
 * @method static Builder|Request wherePrivate($value)
 * @method static Builder|Request whereProjectId($value)
 * @method static Builder|Request wherePurpose($value)
 * @method static Builder|Request whereReportTripEnds($value)
 * @method static Builder|Request whereReportTripStarts($value)
 * @method static Builder|Request whereRequestElementAccountedSumAmount($value)
 * @method static Builder|Request whereRequestElementSumAmount($value)
 * @method static Builder|Request whereRequestedLumpSums($value)
 * @method static Builder|Request whereRequestedTravelExpenses($value)
 * @method static Builder|Request whereSentAt($value)
 * @method static Builder|Request whereSettledAmountBeforeAccounting($value)
 * @method static Builder|Request whereSettledAt($value)
 * @method static Builder|Request whereSettlementAutoAccepted($value)
 * @method static Builder|Request whereSlug($value)
 * @method static Builder|Request whereStatus($value)
 * @method static Builder|Request whereStatusSettlementAt($value)
 * @method static Builder|Request whereStatusTransferredAt($value)
 * @method static Builder|Request whereTargetPointsText($value)
 * @method static Builder|Request whereTravelExpenseCache($value)
 * @method static Builder|Request whereTripEnds($value)
 * @method static Builder|Request whereTripStarts($value)
 * @method static Builder|Request whereType($value)
 * @method static Builder|Request whereUnrealizedAt($value)
 * @method static Builder|Request whereUpdatedAt($value)
 * @method static Builder|Request whereUpdatedBy($value)
 * @method static Builder|Request whereUsePrepaid($value)
 * @method static Builder|Request whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|Request withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Request withoutTrashed()
 * @mixin \Eloquent
 * @property int|null $delegation_initial
 * @method static Builder|Request whereDelegationInitial($value)
 * @method static Builder|Request notDraft()
 * @property int $is_re_acceptance
 * @property-read \Illuminate\Database\Eloquent\Collection|Mpk[] $hasMpks
 * @property-read int|null $has_mpks_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\PassengerCarTrip[] $passengerCarTrips
 * @property-read int|null $passenger_car_trips_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\ProvidedAccommodation[] $providedAccommodations
 * @property-read int|null $provided_accommodations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\ReplacementCarTrip[] $replacementCarTrips
 * @property-read int|null $replacement_car_trips_count
 * @method static Builder|Request whereIsReAcceptance($value)
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\RequestCache[] $caches
 * @property-read int|null $caches_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Document[] $notRequestedDocuments
 * @property-read int|null $not_requested_documents_count
 * @method static Builder|Request whereShowInstallment($value)
 * @method static Builder|Request whereShowOtherCosts($value)
 */
class Request extends Model implements SearchWithRulesInterface, BlameableInterface, HasManyMpkModelInterface
{
    use InstanceTrait;
    use MpkTrait;
    use ProjectTrait;
    use UserTrait;
    use AccountingUserTrait;
    use Notifiable;
    use CompanyTrait;
    use HasManyMpkModelTrait;

    use SoftDeletes;
    use Searchable;
    use BlameableTrait;

    public const RELATION_NAME = 'request';
    public const REQUESTS_CACHE_TAG = 'requests';
    public const TYPE_TRIP = 'trip';
    public const TYPE_EXPENSE = 'expense';
    public const TYPE_INVOICE = 'invoice';
    public const STATUS_REJECTED = 'rejected';
    public const STATUS_CANCELED = 'canceled';
    public const STATUS_DELETED = 'deleted';
    public const STATUS_DRAFT = 'draft';
    public const STATUS_WAITING_FOR_ACCEPTANCE = 'waiting_for_acceptance';
    public const STATUS_UPCOMING_TRIP = 'upcoming_trip';
    public const STATUS_TRIP = 'trip';
    public const STATUS_SETTLEMENT = 'settlement';
    public const STATUS_ACCEPTANCE_OF_SETTLEMENT = 'acceptance_of_settlement';
    public const STATUS_ACCOUNTING = 'accounting'; // Dekretacja
    public const STATUS_TRANSFER_ERROR = 'transfer_error'; // Transfer nieudany
    public const STATUS_TRANSFERRED = 'transferred_to_erp'; // Przesłany
    public const STATUS_FINISH = 'finish'; // Zaksięgowany
    public const TRIP_SEARCH = 'trip_search';
    public const EXPENSE_SEARCH = 'expense_search';
    public const INVOICE_SEARCH = 'invoice_search';
    public const ACCOUNTING_SEARCH = 'accounting_search';
    public const USER_TYPE_ACCEPTOR = 'acceptor';
    public const USER_TYPE_SETTLEMENT_ACCEPTOR = 'settlement_acceptor';
    public const ACCEPTOR_ACCEPTED = 1;
    //Domyslnie, gdy nie zostala podjeta jeszcze zadna akcja
    public const ACCEPTOR_NO_ACTION = 0;
    public const ACCEPTOR_REJECTED = -1;
    public const ACCEPTOR_STATUS_WAITING_FOR_STEP = 'waiting_for_step';
    public const ACCEPTOR_STATUS_PENDING = 'pending';
    public const ACCEPTOR_STATUS_ACCEPTED = 'accepted';
    public const ACCEPTOR_STATUS_REJECTED = 'rejected';
    public const ACCEPTOR_STATUS_NEED_CORRECT = 'need_correct';
    public const STATE_SETTLEMENT = 'state_settlement';
    public const STATE_REQUEST = 'state_request';
    public const TRIP_COURSE_STATE_CROSSINGS = 0;
    public const TRIP_COURSE_STATE_DEDUCTIONS = 1;

    protected $indexConfigurator = RequestIndexConfigurator::class;

    // For elastic searches only.
    protected $searchRules = [];

    private $lazy_trip_starts = null;

    private $lazy_trip_ends = null;

    protected $fillable = [
        'project_id',
        'mpk_id',
        'purpose',
        'annotation',
        'type',
        'sent_at',
        'canceled_at',
        'use_prepaid',
        'mileage_allowance_cost_of_earning',
        'mileage_allowance_mpk_id',
        'mileage_allowance_debit_account_id',
        'mileage_allowance_credit_account_id',
        'mileage_allowance_amount',
        'lump_sum_settled',
        'lump_sum_accounted',
        'national_target',
        'national_trip',
        'border_crossing_state',
        'target_points_text',
        'delegation',
        'finished_at',
        'completion_date',
        'company_id',
        'periodic',
        'mileage_allowance_project_id',
        'private',
        'mileage_allowance_deductibility',
        'show_other_costs',
        'show_installment',
    ];

    protected $dates = [
        'applicated_at',
        'accounted_at',
        'sent_at',
        'trip_starts',
        'trip_ends',
        'status_settlement_at',
        'status_transferred_at',
        'settled_at',
        'canceled_at',
        'unrealized_at',
        'finished_at',
        'erp_accounted_at',
        'erp_vat_at',
        'completion_date',
        'updated_at',
        'report_trip_starts',
        'report_trip_ends',
    ];

    protected $casts = [
        'use_prepaid' => 'boolean',
        'mileage_allowance_cost_of_earning' => 'boolean',
        'lump_sum_settled' => 'boolean',
        'lump_sum_accounted' => 'boolean',
        'national_trip' => 'boolean',
        'national_target' => 'boolean',
        'border_crossing_state' => 'boolean',
        'autoaccepted' => 'boolean',
        'auto_reserved' => 'boolean',
        'settlement_auto_accepted' => 'boolean',
        'delegation' => 'boolean',
        'delegation_initial' => 'boolean',
        'periodic' => 'boolean',
        'private' => 'boolean',
        'show_other_costs' => 'boolean',
        'show_installment' => 'boolean',
//        'requested_travel_expenses' => 'array',
    ];

    public const SORTABLE_COLUMNS = [
        'sent_at',
        'number',
        'name_translated',
        'company_code',
        'request_element_accounted_sum_amount',
        'request_element_sum_amount',
        'user_last_name',
        'request_acceptors_users_last_names',
        'status_translated',
        'settled_at',
        'request_accounting_user_last_name',
        'changed_status_at',
        'accounted_at',
    ];
    // For elastic searches only.
    protected $mapping = [
        'properties' => [
            'request_type' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_number' => ['type' => 'keyword'],
            'request_name_pl' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_name_en' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_elements_accounted_amount' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_elements_sum_amount' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_applicant_user_full_name' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_acceptors_full_names' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_status_pl' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
            'request_status_en' => ['type' => 'text', 'fields' => ['raw' => ['type' => 'keyword']]],
        ]
    ];

    protected $settlementDateCache = null;

    protected function setSearchRule(string $searchType): void
    {
        switch ($searchType) {
            case self::TRIP_SEARCH:
                $this->searchRules = [RequestTripMultiMatchRule::class];
                break;
            case self::ACCOUNTING_SEARCH:
                $this->searchRules = [RequestAccountingMultiMatchRule::class];
                break;
            case self::EXPENSE_SEARCH:
                $this->searchRules = [RequestExpenseMultiMatchRule::class];
                break;
            case self::INVOICE_SEARCH:
                $this->searchRules = [RequestInvoiceMultiMatchRule::class];
                break;
        }
    }

    public static function searchWithRules(string $query, ?string $searchType = null, ?callable $callback = null)
    {
        $model = new static;

        if (is_string($searchType) === true
            && in_array(
                $searchType,
                [Request::TRIP_SEARCH, Request::ACCOUNTING_SEARCH, Request::EXPENSE_SEARCH, Request::INVOICE_SEARCH]
            ) === true
        ) {
            $model->setSearchRule($searchType);
        }

        $softDelete = static::usesSoftDelete() && config('scout.soft_delete', false);

        if ($query == '*') {
            return new FilterBuilder($model, $callback, $softDelete);
        } else {
            return new SearchBuilder($model, $query, $callback, $softDelete);
        }
    }

    public function toSearchableArray()
    {
        $searchableArray = [
            'request_type' => $this->type,
            'request_number' => (string)$this->number,
            'request_name_pl' => $this->getTranslatedRequestName('pl_PL.utf8'),
            'request_name_en' => $this->getTranslatedRequestName('en'),
            'request_elements_accounted_amount' => $this->request_element_accounted_sum_amount,
            'request_elements_sum_amount' => $this->request_element_sum_amount,
            'request_applicant_user_full_name' => $this->user instanceof User ? $this->user->full_name : null,
            'request_acceptors_full_names' => $this->getRequestAcceptorsFullNames(),
            'request_status_pl' => $this->getTranslatedStatus('pl_PL.utf8'),
            'request_status_en' => $this->getTranslatedStatus('en'),
            "request_accounting_user_full_name" => $this->accountingUser instanceof User ? $this->accountingUser->full_name : null,
            "request_mpk_name" => $this->mpk instanceof Mpk ? $this->mpk->name : null,
        ];

        foreach ($searchableArray as $index => $value) {
            $searchableArray[$index] = strtolower($value);
        }

        return $searchableArray;
    }

    protected function getRequestAcceptorsFullNames(): ?string
    {
        $names = '';

        if ($this->acceptors instanceof Collection && $this->acceptors->count() > 0) {
            $this->acceptors->each(function (User $acceptor) use (&$names) {
                $names = $names . ' ' . $acceptor->full_name;
            });
        }

        return $names;
    }

    public function getTranslatedStatusInCurrentUserLanguage(): ?string
    {
        return $this->getTranslatedStatus(
            Auth::user() instanceof User
                ? Auth::user()->locale
                : AvailableLanguagesEnum::PL()
        );
    }

    protected function getTranslatedStatus(string $lang): ?string
    {
        $statusTranslationMap = static::getStatusMapForLang($lang);

        if (isset($statusTranslationMap[$this->status]) === true) {
            return $statusTranslationMap[$this->status];
        }

        return null;
    }

    protected function getTranslatedRequestName(string $lang): ?string
    {
        if ($this->type === Request::TYPE_EXPENSE) {
            return $this->purpose;
        } else {
            $service = RequestCourseService::instance()->setRequest($this);
            return $service->getRequestName(false, $lang);
        }
    }

    public static function claimStatuses(): array
    {
        return [
            self::STATUS_TRANSFERRED,
            self::STATUS_FINISH
        ];
    }

    public static function allRequestStatuses(): array
    {
        return [
            self::STATUS_REJECTED,
            self::STATUS_CANCELED,
            self::STATUS_DELETED,
            self::STATUS_DRAFT,
            self::STATUS_WAITING_FOR_ACCEPTANCE,
            self::STATUS_UPCOMING_TRIP,
            self::STATUS_TRIP,
            self::STATUS_SETTLEMENT,
            self::STATUS_ACCEPTANCE_OF_SETTLEMENT,
            self::STATUS_ACCOUNTING,
            self::STATUS_TRANSFER_ERROR,
            self::STATUS_TRANSFERRED,
            self::STATUS_FINISH
        ];
    }

    public static function statusesList(): array
    {
        return array_combine(
            self::allRequestStatuses(),
            self::statusesTranslatedList()
        );
    }

    public static function statusesTranslatedList(): array
    {
        return array_map(fn($s) => __('global.request-status-' . $s), self::allRequestStatuses());
    }

    public static function getAcceptorStatuses()
    {
        return collect([
            Request::ACCEPTOR_STATUS_ACCEPTED,
            Request::ACCEPTOR_NO_ACTION,
            Request::ACCEPTOR_STATUS_REJECTED,
        ]);
    }

    public static function getStatusesAvailableForAccountant()
    {
        return collect([
            Request::STATUS_ACCOUNTING,
            Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
            Request::STATUS_SETTLEMENT,
            Request::STATUS_TRANSFERRED,
            Request::STATUS_TRANSFER_ERROR,
            Request::STATUS_FINISH,
        ]);
    }

    public static function getStatusesAvailableForAgent()
    {
        return collect([
            Request::STATUS_REJECTED,
            Request::STATUS_CANCELED,
            Request::STATUS_DELETED,
            Request::STATUS_DRAFT,
            Request::STATUS_WAITING_FOR_ACCEPTANCE,
            Request::STATUS_UPCOMING_TRIP,
            Request::STATUS_TRIP,
            Request::STATUS_SETTLEMENT,
            Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
            Request::STATUS_ACCOUNTING,
            Request::STATUS_TRANSFER_ERROR,
            Request::STATUS_TRANSFERRED,
            Request::STATUS_FINISH,
        ]);
    }

    public static function getStatusesAvailableUnrealizedTrip(): array
    {
        return [
            Request::STATUS_UPCOMING_TRIP,
            Request::STATUS_TRIP,
            Request::STATUS_SETTLEMENT,
        ];
    }

    public static function getTypes()
    {
        return collect([
            Request::TYPE_TRIP,
            Request::TYPE_EXPENSE,
            Request::TYPE_INVOICE,
        ]);
    }

    public static function getStatuses()
    {
        return collect([
            Request::STATUS_REJECTED,
            Request::STATUS_CANCELED,
            Request::STATUS_DELETED,
            Request::STATUS_DRAFT,
            Request::STATUS_WAITING_FOR_ACCEPTANCE,
            Request::STATUS_UPCOMING_TRIP,
            Request::STATUS_TRIP,
            Request::STATUS_SETTLEMENT,
            Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
            Request::STATUS_ACCOUNTING,
            Request::STATUS_TRANSFER_ERROR,
            Request::STATUS_TRANSFERRED,
            Request::STATUS_FINISH,
        ]);
    }

    public static function getUserTypes()
    {
        return collect([
            Request::USER_TYPE_ACCEPTOR,
            Request::USER_TYPE_SETTLEMENT_ACCEPTOR,
        ]);
    }

    public static function getAcceptorStatus()
    {
        return collect([
            Request::ACCEPTOR_STATUS_PENDING,
            Request::ACCEPTOR_STATUS_ACCEPTED,
            Request::ACCEPTOR_STATUS_REJECTED,
            Request::ACCEPTOR_STATUS_NEED_CORRECT,
        ]);
    }

    public static function getElementsRelationName()
    {
        return collect([
            Accomodation::RELATION_NAME,
            PlaneTrip::RELATION_NAME,
            PrivateCarTrip::RELATION_NAME,
            CompanyCarTrip::RELATION_NAME,
            RentedCarTrip::RELATION_NAME,
            ReplacementCarTrip::RELATION_NAME,
            PassengerCarTrip::RELATION_NAME,
            Cost::RELATION_NAME,
            TrainTrip::RELATION_NAME,
            FerryBoatTrip::RELATION_NAME,
            BusTrip::RELATION_NAME,
        ]);
    }

    public function accountDimensionItems()
    {
        return $this->hasMany(RequestAccountDimensionItem::class);
    }

    public function hasAccountDimensionItem(string $accountDimensionSlug, array $accountDimensionItemSlugs): bool
    {
        return $this->accountDimensionItems()
            ->whereHas('accountDimension', function ($query) use ($accountDimensionSlug) {
                $query->where('slug', $accountDimensionSlug);
            })
            ->whereHas('accountDimensionItem', function ($query) use ($accountDimensionItemSlugs) {
                $query->whereIn('slug', $accountDimensionItemSlugs);
            })
            ->exists();
    }

    public function instance(): BelongsTo
    {
        return $this->belongsTo(Instance::class);
    }

    public function accountingAccountDimensionItems()
    {
        return $this->hasMany(RequestAccountingAccountDimensionItem::class);
    }

    public function mileageAllowanceSummaryAccountDimensionItems()
    {
        return $this->hasMany(RequestAccountingMileageAllowanceAccountDimensionItem::class);
    }

    public function accomodations()
    {
        return $this->hasMany(Accomodation::class);
    }

    public function privateAccomodations()
    {
        return $this->hasMany(PrivateAccomodation::class);
    }

    public function providedAccommodations()
    {
        return $this->hasMany(ProvidedAccommodation::class);
    }

    public function companyCarTrips()
    {
        return $this->hasMany(CompanyCarTrip::class);
    }

    public function replacementCarTrips()
    {
        return $this->hasMany(ReplacementCarTrip::class);
    }

    public function passengerCarTrips()
    {
        return $this->hasMany(PassengerCarTrip::class);
    }

    public function planeTrips()
    {
        return $this->hasMany(PlaneTrip::class);
    }

    public function privateCarTrips()
    {
        return $this->hasMany(PrivateCarTrip::class);
    }

    public function rentedCarTrips()
    {
        return $this->hasMany(RentedCarTrip::class);
    }

    public function trainTrips()
    {
        return $this->hasMany(TrainTrip::class);
    }

    public function ferryBoatTrips()
    {
        return $this->hasMany(FerryBoatTrip::class);
    }

    public function busTrips()
    {
        return $this->hasMany(BusTrip::class);
    }

    public function costs()
    {
        return $this->hasMany(Cost::class);
    }

    public function acceptors()
    {
        return $this->belongsToMany(User::class, 'request_acceptors_user')
            ->withPivot(
                ['accepted', 'default', 'added_by', 'acceptance_date', 'token', 'comment_id', 'step', 'notificated_at']
            )
            ->orderBy('step', 'asc');
    }

    public function accountingUser()
    {
        return $this->belongsTo(User::class, 'accounting_user_id');
    }

    public function settlementAcceptors()
    {
        return $this->belongsToMany(User::class, 'request_settlement_acceptors_user')
            ->withPivot(
                ['accepted', 'default', 'added_by', 'acceptance_date', 'token', 'comment_id', 'step', 'notificated_at']
            )
            ->orderBy('step', 'asc');
    }

    public function comments()
    {
        return $this->hasMany(Comment::class)->orderByDesc('created_at')->orderByDesc('id');
    }

    public function getCommentLastProxyReturned(): ?Comment
    {
        return $this->comments()
            ->whereIn('autocontent', Comment::getReturnedContentFlags())
            ->first();
    }

    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    public function unassignedDocuments()
    {
        return $this->notRequestedDocuments();
    }

    public function accountingDocuments()
    {
        return $this->hasMany(Document::class)->where(['type' => Document::TYPE_ACCOUNTING]);
    }

    public function countableDocuments()
    {
        return $this->hasMany(Document::class)
            ->whereIn('type', [Document::TYPE_ACCOUNTING, Document::TYPE_CONFIRMATION]);
    }

    public function unmarkedDocuments()
    {
        return $this->hasMany(Document::class)->whereNull('type');
    }

    public function notRequestedDocuments()
    {
        return $this->hasMany(Document::class)
            ->where(function (Builder $builder) {
                $builder->whereHas(Document::relationships()->get(UnrequestedElement::RELATION_NAME));
            });
    }

    public function installments()
    {
        return $this->hasMany(Installment::class);
    }

    public function borderCrossings()
    {
        return $this->hasMany(RequestBorderCrossing::class);
    }

    public function accommodationDriveLumpSums()
    {
        return $this->hasMany(RequestAccommodationDriveLumpSum::class);
    }

    public function mealDeductions()
    {
        return $this->hasMany(RequestMealDeduction::class);
    }

    public function travelExpenses()
    {
        return $this->hasMany(RequestMealDeduction::class);
    }

    public function mileageAllowances()
    {
        return $this->hasMany(RequestMileageAllowance::class);
    }

    public function mileageAllowancesMpk()
    {
        return $this->hasOne(Mpk::class, 'id', 'mileage_allowance_mpk_id');
    }

    public function requestCaches()
    {
        return $this->hasMany(RequestCache::class, 'request_id', 'id');
    }

    public function mileageAllowancesAccountingAccount(): HasOne
    {
        return $this->hasOne(AccountingAccount::class, 'id', 'mileage_allowance_debit_account_id');
    }

    public function mileageAllowancesCreditAccount(): HasOne
    {
        return $this->hasOne(AccountingAccount::class, 'id', 'mileage_allowance_credit_account_id');
    }

    public function accountingTravelExpenses()
    {
        return $this->hasMany(RequestAccountingTravelExpenses::class);
    }

    public function accountingMileageAllowances(): HasMany
    {
        return $this->hasMany(RequestAccountingMileageAllowance::class);
    }

    public function targetPoints()
    {
        return $this->hasMany(TargetPoint::class);
    }

    public function unrequestedElement()
    {
        return $this->hasOne(UnrequestedElement::class);
    }

    public function mileageAllowanceAccountingAccount()
    {
        return $this->belongsTo(AccountingAccount::class, 'mileage_allowance_debit_account_id');
    }

    public function mileageAllowanceMpk()
    {
        return $this->belongsTo(Mpk::class, 'mileage_allowance_mpk_id');
    }

    public function mileageAllowanceProject()
    {
        return $this->belongsTo(Project::class, 'mileage_allowance_project_id');
    }

    public function startLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'start_location']);
    }

    public function endLocation()
    {
        return $this->morphOne(Location::class, 'localizable')->where(['column' => 'end_location']);
    }

    public function accessLumpSums()
    {
        return $this->hasMany(AccessLumpSum::class);
    }

    public function offers()
    {
        return $this->hasMany(Offer::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function travelers()
    {
        return $this->hasMany(RequestTraveler::class, 'request_id', 'id');
    }

    public function requestChanges()
    {
        return $this->hasMany(RequestChange::class);
    }

    public function getUidAttribute()
    {
        return $this->number ?? '';
    }

    public function getFrontendUrl()
    {
        $protocol = "http://";
        $instanceDomain = trim($this->instance->domain, '/');

        return $protocol . $instanceDomain . '/requests/' . $this->type . '/' . $this->slug;
    }

    public function getAcceptanceUrl($token, $type, $action, $lang = null)
    {
        $lang = $lang ?? $this->instance->lang;
        $protocol = "http://";
        $instanceDomain = trim($this->instance->domain, '/');

        $type = $type === 'settlement' ? 'settlement-acceptance' : 'acceptance';
        return $protocol . $instanceDomain . '/request/' . $this->slug . '/' . $type . '/' . $action . '/' . $token . '?lang=' . $lang;
    }

    /**
     * @param bool $forceSettled - should be used only for testing
     * @return array
     */
    public function getBasicSummary(
        $separator = ' → ',
        $forceSettled = false,
        ?bool $canAccountDelegation = null
    ): array {
        $countSettled = $forceSettled || $this->getStateAttribute() === static::STATE_SETTLEMENT;
        $result = app(SummaryService::class, [
            'request' => $this,
        ])->init($this, $countSettled, $canAccountDelegation)->setElementNameSeparator($separator)->getSummary(
        )->toArray();
        return $result;
    }

    public function getSubsummary(?bool $canAccountDelegation = null): array
    {
        return (new SubsummaryService($this, $canAccountDelegation))->getSummary()->toArray();
    }

    public function getRequestElementSumAmountAttribute($sum): string
    {
        return Math::round($sum, 2);
    }

    public function clearCache(): Request
    {
        Cache::deleteTags([$this->cacheTag(), 'requests', 'subsummary' . $this->id]);

        return $this;
    }

    public function updateRequestElementSumAmount(): string
    {
        $summary = $this->getBasicSummary();
        $sum = $summary['requestedConvertedAmount']['amount'] ?? '0';
        $accountedSum = $summary['settledConvertedAmount']['amount'] ?? '0';

        $this->request_element_sum_amount = $sum;
        $this->request_element_accounted_sum_amount = $this->state === Request::STATE_SETTLEMENT ? $accountedSum : 0;
        $this->travel_expense_cache = $this->type == Request::TYPE_TRIP
            ? Math::round((new SubsummaryService($this))->getAmount() ?? 0, 2)
            : 0;

        $this->has_broken_rules = (bool)count($summary['rules']);

        $this->mileage_allowance_amount = $this->sumMileageAllowances();

        $this->report_trip_starts = $this->getTripStartsAttribute();
        $this->report_trip_ends = $this->getTripEndsAttribute();

        $this->save();

        return $sum;
    }

    public function getNameAttribute(string $locale = null)
    {
        $data = $this->attributes['name'];

        try {
            $decodedData = is_string($data) === true ? json_decode($data) : null;
            $lang = in_array($locale, AvailableLanguagesEnum::toArray())
                ? $locale
                : (Auth::user() instanceof User
                    ? Auth::user()->locale
                    : AvailableLanguagesEnum::PL()
                );

            if ($decodedData instanceof \stdClass && property_exists($decodedData, $lang)) {
                return $decodedData->{$lang};
            }
        } catch (\Throwable $exception) {
            Log::info($exception);
        }

        return null;
    }

    public function getRequestNameShort()
    {
        if ($this->type === Request::TYPE_EXPENSE) {
            return $this->purpose;
        } else {
            $service = RequestCourseService::instance()->setRequest($this);
            return $service->getRequestName(false, null, true);
        }
    }

    public function getNameShortAttribute()
    {
        if ($this->type === Request::TYPE_EXPENSE) {
            return $this->purpose;
        } else {
            $service = RequestCourseService::instance()->setRequest($this);
            return $service->getRequestName(true);
        }
    }

    public function getTripDateAttribute(): string
    {
        if ($this->trip_starts && $this->trip_ends) {
            return $this->trip_starts->format('d.m.Y') . " - " . $this->trip_ends->format('d.m.Y');
        } else {
            if ($this->trip_starts) {
                return $this->trip_starts->format('d.m.Y');
            } else {
                if ($this->trip_ends) {
                    return $this->trip_ends->format('d.m.Y');
                } else {
                    return '';
                }
            }
        }
    }

    public function getTripStartsFormatted()
    {
        return $this->trip_starts->format('d.m.Y');
    }

    public function getTripEndsFormatted()
    {
        return $this->trip_ends->format('d.m.Y');
    }

    public function updatedRequestElementSumAmount(): string
    {
        $requestElements = collect()
            ->merge($this->accomodations)
            ->merge($this->companyCarTrips)
            ->merge($this->planeTrips)
            ->merge($this->privateCarTrips)
            ->merge($this->rentedCarTrips)
            ->merge($this->trainTrips)
            ->merge($this->costs)
            ->merge($this->ferryBoatTrips)
            ->merge($this->busTrips);

        $sum = '0';

        $requestElements->each(function (RequestElementInterface $model) use (&$sum) {
            $sum = Math::add($sum, $model->getConvertedAmount());
        });

        return $sum;
    }

    public function getAccountingBalance(
        ?bool $shouldAddTravelAndLumpSums = null
    ): array {
        return (new AccountingBalanceService($this, $shouldAddTravelAndLumpSums))->getAccountingBalance()->toArray();
    }

    public function getBalance(
        ?bool $shouldAddTravelAndLumpSums = null
    ): array {
        return (new BalanceService($this, $shouldAddTravelAndLumpSums))->getBalance()->toArray();
    }

    public function getSummaryWithBalance(?bool $canAccountDelegation = null): array
    {
        return [
            'basicSummary' => $this->getBasicSummary(' → ', false, $canAccountDelegation),
            'balance' => $this->getBalance()
        ];
    }

    public function getTripStartsAttribute()
    {
        if ($this->lazy_trip_starts === null) {
            if ($this->type === Request::TYPE_EXPENSE) {
                $this->lazy_trip_starts = $this->created_at;

                return $this->lazy_trip_starts->copy();
            }

            if (isset($this->attributes['trip_starts']) && $this->attributes['trip_starts']) {
                $this->lazy_trip_starts = new Carbon($this->attributes['trip_starts']);

                return $this->lazy_trip_starts->copy();
            }

            $this->lazy_trip_starts = $this->getExtremeElementDate('min');
        }
        return $this->lazy_trip_starts->copy();
    }

    public function getTripEndsAttribute()
    {
        if ($this->lazy_trip_ends === null) {
            if ($this->type === Request::TYPE_EXPENSE) {
                $this->lazy_trip_ends = $this->created_at;

                return $this->lazy_trip_ends->copy();
            }

            if (isset($this->attributes['trip_ends']) && $this->attributes['trip_ends']) {
                $this->lazy_trip_ends = new Carbon($this->attributes['trip_ends']);

                return $this->lazy_trip_ends->copy();
            }

            $this->lazy_trip_ends = $this->getExtremeElementDate('max');
        }

        return $this->lazy_trip_ends->copy();
    }

    public function setTripStartsAttribute($value)
    {
        $this->attributes['trip_starts'] = $value;
        $this->lazy_trip_starts = null;
    }

    public function setTripEndsAttribute($value)
    {
        $this->attributes['trip_ends'] = $value;
        $this->lazy_trip_ends = null;
    }

    /**
     * @return mixed
     */
    public function getTripDuration()
    {
        if (!$this->trip_ends || !$this->trip_starts) {
            return null;
        }

        return $this->trip_ends->diffInDays($this->trip_starts);
    }

    public function getTripDurationInHours()
    {
        if (!$this->trip_ends || !$this->trip_starts) {
            return 0; //todo exception?
        }

        return $this->trip_ends->diffInHours($this->trip_starts);
    }

    public function getExtremeElementDate($extreme = 'min')
    {
        abort_unless(in_array($extreme, ['min', 'max']), 500, 'Wrong extreme type');

        $service = RequestCourseService::instance()->setRequest($this);

        $locations = $service->getLocations(true);

        if ($locations->isEmpty()) {
            $date = Carbon::today();
        } else {
            $date = $extreme === 'min' ? $locations->first()->getDate() : $locations->last()->getDate();
        }

        return $extreme == 'max' && $locations->last() instanceof LocationElement ? $locations->last()->getDate(
        )->endOfDay() : $date->startOfDay();
    }

    public function getSettlementDate()
    {
        //No installment date in system yet
        //todo: change after installment date will be available
        $this->settlementDateCache = $this->settled_at;

        if ($this->settlementDateCache === null) {
            $firstInstallment = resolve(InstallmentRepository::class)->getInstallmentsPaidForRequest($this)
                ->sortBy('date')
                ->first();

            if ($firstInstallment && $firstInstallment->date !== null) {
                $this->settlementDateCache = $firstInstallment->date;
            } else {
                $this->settlementDateCache = $this->settled_at;
            }
        }

        return $this->settlementDateCache;
    }

    public function getApplicatedAt(): ?Carbon
    {
        return $this->applicated_at;
    }

    public function getDateForRequestedRequestCalculation()
    {
        // sent_at date should be use, other dates for fallback only.
        if ($this->sent_at instanceof Carbon) {
            return $this->sent_at;
        } elseif ($this->getApplicatedAt() instanceof Carbon) {
            return $this->applicated_at;
        }

        return $this->getSettlementDate();
    }

    /**
     * Check read permission for requests
     *
     * @return null
     */
    public function scopeUserCanRead(Builder $query, User $user)
    {
        if ($user->hasAbility(Permission::REQUEST_READ_INSTANCE_SCOPE)) {
            $query->where(['requests.instance_id' => $user->instance->id]);
        } else {
            $query->where(function (Builder $q) use (&$user) {
                $q->where('requests.user_id', $user->id)
                    ->orWhere(function (Builder $statusQuery) use (&$user) {
                        $statusQuery
                            ->orWhereRaw(
                                '
                                        (status = ?
                                            and requests.id in (select request_id from request_acceptors_user where accepted = ?)
                                            and requests.id in (select request_id from request_acceptors_user where user_id = ?)
                                        )',
                                [Request::STATUS_DRAFT, Request::ACCEPTOR_STATUS_NEED_CORRECT, $user->id]
                            )
                            ->orWhereRaw(
                                '
                                                    (requests.status in (
                                                        \'' . Request::STATUS_WAITING_FOR_ACCEPTANCE . '\',
                                                        \'' . Request::STATUS_REJECTED . '\',
                                                        \'' . Request::STATUS_CANCELED . '\',
                                                        \'' . Request::STATUS_UPCOMING_TRIP . '\',
                                                        \'' . Request::STATUS_TRIP . '\'
                                                        )
                                                        and requests.id in (select request_id from request_acceptors_user where user_id = ?)
                                                    )',
                                [$user->id]
                            )
                            ->orWhereRaw(
                                '
                                                    (requests.status in (
                                                        \'' . Request::STATUS_SETTLEMENT . '\',
                                                        \'' . Request::STATUS_ACCEPTANCE_OF_SETTLEMENT . '\',
                                                        \'' . Request::STATUS_ACCOUNTING . '\',
                                                        \'' . Request::STATUS_TRANSFERRED . '\',
                                                        \'' . Request::STATUS_TRANSFER_ERROR . '\',
                                                        \'' . Request::STATUS_FINISH . '\'
                                                        )
                                                        and (requests.id in (select request_id from request_acceptors_user where user_id = ?)
                                                        or requests.id in (select request_id from request_settlement_acceptors_user where user_id = ?))
                                                    )',
                                [$user->id, $user->id]
                            )
                            ->orWhereRaw(
                                '
                                        (requests.status in (
                                            \'' . Request::STATUS_WAITING_FOR_ACCEPTANCE . '\',
                                            \'' . Request::STATUS_TRIP . '\',
                                            \'' . Request::STATUS_UPCOMING_TRIP . '\',
                                            \'' . Request::STATUS_SETTLEMENT . '\',
                                            \'' . Request::STATUS_ACCEPTANCE_OF_SETTLEMENT . '\',
                                            \'' . Request::STATUS_ACCOUNTING . '\',
                                            \'' . Request::STATUS_TRANSFERRED . '\',
                                            \'' . Request::STATUS_TRANSFER_ERROR . '\',
                                            \'' . Request::STATUS_FINISH . '\'
                                            )
                                            and requests.user_id in (select id from users where user_id = ?)
                                        )',
                                [$user->id]
                            );
                    });

                if ($user->isAccountant()) {
                    $q = $q->orWhere(function (Builder $statusQuery) use ($user) {
                        $statusQuery->whereIn('requests.status', [
                            Request::STATUS_UPCOMING_TRIP,
                            Request::STATUS_TRIP,
                            Request::STATUS_SETTLEMENT,
                            Request::STATUS_ACCEPTANCE_OF_SETTLEMENT,
                            Request::STATUS_ACCOUNTING,
                            Request::STATUS_TRANSFERRED,
                            Request::STATUS_TRANSFER_ERROR,
                            Request::STATUS_FINISH,
                        ]);

                        /** @var AuthorizationCheckerService $authorizationCheckerService */
                        $authorizationCheckerService = resolve(AuthorizationCheckerService::class);
                        $authorizationCheckerService->addPermissionScopeForQuery(
                            $statusQuery,
                            Permission::REQUEST_ACCOUNTING,
                            $user
                        );
                    });
                }
            });
        }
        return $query;
    }

    public function getStateAttribute(): string
    {
        if (in_array(
            $this->status,
            [
                Request::STATUS_REJECTED,
                Request::STATUS_CANCELED,
                Request::STATUS_DRAFT,
                Request::STATUS_WAITING_FOR_ACCEPTANCE
            ]
        )) {
            return Request::STATE_REQUEST;
        }

        return Request::STATE_SETTLEMENT;
    }

    public function daysInSettlement()
    {
        return self::daysInSettlementInternal($this->type, $this->trip_ends, $this->status_settlement_at);
    }

    public static function daysInSettlementInternal(
        string $requestType,
        ?Carbon $requestTripEnds,
        ?Carbon $requestStatusInSettlement
    ): int {
        if ($requestTripEnds instanceof Carbon && $requestStatusInSettlement instanceof Carbon) {
            try {
                $date = $requestType === static::TYPE_TRIP ? $requestTripEnds->copy()->startOfDay(
                ) : $requestStatusInSettlement->copy()->startOfDay();
                $diff = $date->diffInDays(Carbon::now(), false);

                return $diff > 0 ? $diff : 0;
            } catch (\Throwable $exception) {
                return 0;
            }
        }

        return 0;
    }

    public function sumMileageAllowances(): string
    {
        if ($this->isUnrealized()) {
            return Math::round(0);
        }

        return $this->mileageAllowances->reduce(function ($acc, RequestMileageAllowance $allowance) {
            return Math::add($acc, $allowance->amount());
        }, "0");
    }

    public function sumAccountingMileageAllowances(): string
    {
        return $this->accountingMileageAllowances->reduce(
            function ($acc, RequestAccountingMileageAllowance $allowance) {
                return Math::add($acc, $allowance->getAmount());
            },
            "0"
        );
    }

    public function setNationalTripAttribute($value)
    {
        $this->attributes['national_trip'] = $value;

        if ($this->attributes['national_trip']) {
            $this->attributes['national_target'] = true;
        }
    }

    public function cacheTag(): string
    {
        return Request::class . ':' . $this->slug;
    }

    public function isNationalTrip()
    {
        return $this->type == Request::TYPE_TRIP && $this->national_trip;
    }

    public function isTrip(): bool
    {
        return $this->type == Request::TYPE_TRIP;
    }

    public function isAbroadTrip()
    {
        return $this->type == Request::TYPE_TRIP && !$this->national_trip;
    }

    public function getRequestedTravelExpensesAttribute(): ?Collection
    {
        return !empty($this->attributes['requested_travel_expenses'])
            ? \Opis\Closure\unserialize($this->attributes['requested_travel_expenses'])
            : null;
    }

    public function setRequestedTravelExpensesAttribute(Collection $value)
    {
        $this->attributes['requested_travel_expenses'] = \Opis\Closure\serialize($value);
    }

    public function isDelegation(): bool
    {
        return $this->type === static::TYPE_TRIP && $this->delegation;
    }

    public function canAccountDelegation(): bool
    {
        if (false === $this->isTrip()) {
            return false;
        }

        return $this->user->hasAbility(Permission::ACCOUNT_DELEGATION);
    }

    public function canAccountDriveLumpSum(): bool
    {
        if (false === $this->isTrip()) {
            return false;
        }

        return $this->user->hasAbility(Permission::ACCOUNT_DELEGATION);
    }

    public function isUnrealized(): bool
    {
        return is_null($this->unrealized_at) ? false : true;
    }

    public function isAccepted(): bool
    {
        return $this->acceptors->count() === $this->acceptors->where(
                'pivot.accepted',
                Request::ACCEPTOR_STATUS_ACCEPTED
            )->count();
    }

    public function isTripStartLessThanNow()
    {
        $tripStarts = $this->getTripStartsAttribute();

        return $tripStarts instanceof Carbon && $tripStarts->greaterThan(Carbon::now()) === true;
    }

    public function isCanceled(): bool
    {
        return is_null($this->canceled_at) ? false : true;
    }

    public function isInAccounting(): bool
    {
        switch ($this->status) {
            case Request::STATUS_ACCOUNTING:
            case Request::STATUS_TRANSFER_ERROR:
            case Request::STATUS_TRANSFERRED:
            case Request::STATUS_FINISH:
                return true;
        }

        return false;
    }

    public function isPeriodicExpense(): bool
    {
        return $this->type === self::TYPE_EXPENSE && $this->periodic === true;
    }

    public function markAsPeriodic($skipDefaultPurpose = false): void
    {
        if (in_array($this->type, [self::TYPE_EXPENSE, self::TYPE_INVOICE]) === false) {
            return;
        }

        $this->periodic = true;

        if (!$skipDefaultPurpose) {
            $this->purpose = trans(
                $this->type === self::TYPE_EXPENSE ?
                    'main-menu.periodic-expense-request-purpose' : 'main-menu.periodic-invoice-request-purpose',
                ['date' => Carbon::now()->format('d.m.Y')],
            );
        }
    }

    public function isPurposeEdited(): bool
    {
        $purposeChangesCount = $this
            ->requestChanges()
            ->whereNotIn('status', [Request::STATUS_DRAFT])
            ->where('changes', 'like', '%purpose%')
            ->get()
            ->count();

        if ($this->isPeriodicExpense()) {
            return $purposeChangesCount > 1;
        }

        return $purposeChangesCount > 0;
    }

    public function getLocalizableTripElements(): LocalizableTripElementsCollection
    {
        return LocalizableTripElementsCollection::createFromRequest($this);
    }

    public function setErpAccounted(ErpAccountedSettlementDto $dto): void
    {
        if (!$this->canBeAccounted()) {
            throw new \DomainException(sprintf('Request cannot be accounted in current status [%s]', $this->status));
        }

        $this->erp_id = $dto->getErpId();
        $this->erp_accounted_at = $dto->getErpAccountedAt();
        $this->status = Request::STATUS_TRANSFERRED;
    }

    public function canBeAccounted(): bool
    {
        return in_array($this->status, [Request::STATUS_TRANSFERRED, Request::STATUS_TRANSFER_ERROR]);
    }

    public function syncOriginal()
    {
        $this->lazy_trip_starts = null;
        $this->lazy_trip_ends = null;

        return parent::syncOriginal();
    }

    public function getSlug(): Slug
    {
        return new Slug($this->slug);
    }

    public function getSettledAmount(): string
    {
        return $this->getBasicSummary()['settledConvertedAmount']['amount'] ?? '0.00';
    }

    public function getRequestedAmount(): string
    {
        return $this->getBasicSummary()['requestedConvertedAmount']['amount'] ?? '0.00';
    }

    public function getBasicSummaryInstallments(): array
    {
        return $this->getBasicSummary()['installments'] ?? [];
    }

    public static function getStatusMapForLang(string $lang): array
    {
        $statusTranslationMap = [
            self::STATUS_REJECTED => trans('global.request-status-rejected', [], $lang),
            self::STATUS_CANCELED => trans('global.request-status-canceled', [], $lang),
            self::STATUS_DELETED => trans('global.request-status-deleted', [], $lang),
            self::STATUS_DRAFT => trans('global.request-status-draft', [], $lang),
            self::STATUS_WAITING_FOR_ACCEPTANCE => trans('global.request-status-waiting_for_acceptance', [], $lang),
            self::STATUS_UPCOMING_TRIP => trans('global.request-status-upcoming_trip', [], $lang),
            self::STATUS_TRIP => trans('global.request-status-trip', [], $lang),
            self::STATUS_SETTLEMENT => trans('global.request-status-settlement', [], $lang),
            self::STATUS_ACCEPTANCE_OF_SETTLEMENT => trans('global.request-status-acceptance_of_settlement', [], $lang),
            self::STATUS_ACCOUNTING => trans('global.request-status-accounting', [], $lang),
            self::STATUS_TRANSFERRED => trans('global.request-status-transferred_to_erp', [], $lang),
            self::STATUS_TRANSFER_ERROR => trans('global.request-status-transfer_error', [], $lang),
            self::STATUS_FINISH => trans('global.request-status-finish', [], $lang),
        ];

        return $statusTranslationMap;
    }

    public function getRelatedRequestCacheOfType(string $type): ?RequestCache
    {
        return $this->requestCaches->where('type', $type)->first();
    }

    public function getMileageAllowanceDeductibilityAttribute(): DeductibilityEnum
    {
        return new DeductibilityEnum($this->attributes['mileage_allowance_deductibility']);
    }


    public function getWaitingAcceptor(User $acceptor)
    {
        return $this->acceptors()
            ->wherePivotIn('accepted', [Request::ACCEPTOR_STATUS_PENDING, Request::ACCEPTOR_STATUS_WAITING_FOR_STEP])
            ->wherePivot('user_id', $acceptor->id)
            ->first();
    }

    public function getWaitingSettlementAcceptor(User $acceptor)
    {
        return $this->settlementAcceptors()
            ->wherePivotIn('accepted', [Request::ACCEPTOR_STATUS_PENDING, Request::ACCEPTOR_STATUS_WAITING_FOR_STEP])
            ->wherePivot('user_id', $acceptor->id)
            ->first();
    }

    public function sumAccountingTravelExpensesInInstanceCurrency(): float
    {
        return $this->accountingTravelExpenses->reduce(
            function ($acc, RequestAccountingTravelExpenses $expenses) {
                return $acc + $expenses->instance_currency_amount;
            },
            0
        );
    }

    public function getCashAdvances(): Collection
    {
        return $this->installments ?? collect();
    }

    public function isStatusIn(array $statuses): bool
    {
        return in_array($this->status, $statuses);
    }

    public function doesStatusAllowToAssignAccountant(): bool
    {
        return $this->isStatusIn([
            self::STATUS_ACCOUNTING,
            self::STATUS_TRANSFER_ERROR,
            self::STATUS_TRANSFERRED,
        ]);
    }

    public function setDelegationAttribute(bool $value): void
    {
        if (false === $this->isTrip()) {
            return;
        }

        $this->attributes['delegation'] = $value;

        if ($this->delegation_initial === null) {
            $this->delegation_initial = $this->delegation;
        }

        if ($this->delegation_initial === $this->delegation) {
            $this->delegation_changed_at = null;
        } else {
            $this->delegation_changed_at = Carbon::now();
        }
    }

    public function wasDelegationSwitched(): bool
    {
        return $this->delegation_changed_at && $this->delegation_initial !== $this->delegation;
    }

    public function isOnSettlementStatus(): bool
    {
        return in_array(
            $this->status,
            [
                self::STATUS_SETTLEMENT
            ]
        );
    }

    public function resolveNational(): void
    {
        $this->resolveNationalTrip();
        $this->resolveNationalTarget();
    }

    private function resolveNationalTrip(): void
    {
        $countryId = $this->findCountryForTrip();

        $this->national_trip = $this->targetPoints->every(
            fn(TargetPoint $tp) => $tp->country_id === $countryId
        );
    }

    private function resolveNationalTarget(): void
    {
        $crossings = $this->borderCrossings;

        $countryId = $this->findCountryForTrip();

        $this->national_target = $this->isNationalTrip() || $crossings->filter(
                fn(RequestBorderCrossing $bc) => $bc->target && $bc->country_id === $countryId
            )->isNotEmpty();
    }

    private function findCountryForTrip(): int
    {
        $instanceCountryId = $this->instance->country_id;

        /** @var Location $userWorkLocation */
        $userWorkLocation = $this->user->workLocation()->first();
        $userWorkLocationCountry = $userWorkLocation ? $userWorkLocation->country()->first() : null;

        return $userWorkLocationCountry ? $userWorkLocationCountry->id : $instanceCountryId;
    }

    public function scopeNotDraft(Builder $query): Builder
    {
        return $query->where('status', '!=', self::STATUS_DRAFT);
    }

    public function setStatusAttribute(string $status): void
    {
        $this->attributes['status'] = $status;

        switch ($this->status) {
            case self::STATUS_ACCOUNTING:
                $this->documents->each(function (Document $document) {
                    $document->prepareDocumentForAccounting();
                });
                break;
        }
    }

    public function hasMpks(): BelongsToMany
    {
        return $this->belongsToMany(Mpk::class, 'request_has_mpk')
            ->withPivot(['percentage', 'main']);
    }

    public function getAllHotelNames(): array
    {
        $names = [];

        $offers = $this->offers
            ->where('request_element_type', '=', Accomodation::RELATION_NAME)
            ->where('full_offer', '!=', null);

        foreach ($offers as $offer) {
            foreach ($offer->full_offer as $fullOffer) {
                if (isset($fullOffer['attributes']['name'])) {
                    $names[] = $fullOffer['attributes']['name'];
                }
            }
        }

        return array_unique($names);
    }

    public function resolveDelegations(): void
    {
        if (false === $this->isTrip()) {
            $this->lump_sum_settled = 1;
            $this->lump_sum_accounted = 1;
            $this->delegation = false;
            return;
        }

        if ($this->user->hasRegionalPosition()) {
            $this->lump_sum_settled = 1;
            $this->lump_sum_accounted = 1;
            $this->delegation = false;
        } else {
            $this->lump_sum_settled = 0;
            $this->lump_sum_accounted = 0;
            $this->delegation = true;
        }

        if (false === $this->canAccountDelegation()) {
            $this->lump_sum_accounted = 1;
        }
    }

    public function isInvoice(): bool
    {
        return $this->type === Request::TYPE_INVOICE;
    }

    public function isPeriodicInvoice(): bool
    {
        return $this->isInvoice() && $this->periodic;
    }

    public function caches(): HasMany
    {
        return $this->hasMany(RequestCache::class, 'request_id');
    }

    public function valueOfOtherDocuments(): string
    {
        $sum = 0;

        foreach ($this->documents as $document) {
            if (!$document instanceof Document) {
                continue;
            }
            if ($document->isTravelDocument()) {
                $sum += $document->currency->equals($this->instance->currency)
                    ? (float)($document->grossSettledAmount() ?? 0)
                    : (float)($document->grossConvertedSettledAmount() ?? 0);
            }
        }

        if ($this->user->isB2BUser()) {
            $requestCache = $this->getRelatedRequestCacheOfType(RequestCache::TYPE_LUMP_SUMS_DECLARATION);
            if ($requestCache instanceof RequestCache && $requestCache->data['settled_lump_sums']) {
                $sum += $requestCache->data['settled_lump_sums'];
            }
        }

        return $sum;
    }
}
