<?php


namespace App\BillingAPI\Responses;


use App\Http\Responses\Response2;
use App\Instance;
use App\LocationAddress;

class BillingInstanceResponse extends Response2
{
    protected function transform(Instance $instance)
    {
        return [
            'id'               => $instance->id,
            'domain'           => $instance->domain,
            'name'             => $instance->name,
            'currency'         => $instance->currency->code,
            'country_code'     => $instance->country->country_code,
            'created_at'       => $this->dateTimeFormat($instance->created_at)
        ];
    }
}