<?php

declare(strict_types=1);

namespace App\BillingAPI\Factories\Users;

use App\BillingAPI\DTOs\Users\UserCreatedDto;
use App\Country;
use App\User;
use Modules\Accounting\Priv\Entities\Mpk;

final class UserCreatedDtoFactory
{
    public function createFromMindentoUser(User $user): UserCreatedDto
    {
        return new UserCreatedDto(
            $user->slug,
            $user->erp_id,
            $user->instance_id,
            $user->instance->domain,
            // To keep validation on billing for local environment wee need to pass not empty subdomain.
            empty($user->instance->subdomain) === false ? $user->instance->subdomain : $user->instance->domain,
            $user->company_id,
            $user->avatar,
            $user->email,
            $user->first_name,
            $user->last_name,
            $user->passport_number,
            $user->passport_issue_date,
            $user->passport_valid_date,
            $user->birth_date,
            $user->phone,
            $user->sex,
            $user->groups->pluck('id')->toArray(),
            $user->citizenship->country_code,
            $user->level,
            $user->getPolicyFileName(),
            $user->blocked_at,
            $user->mpk instanceof Mpk ? $user->mpk->code : null,
            $user->nationality instanceof Country ? $user->nationality->country_code : null,
            $user->internal,
        );
    }

}