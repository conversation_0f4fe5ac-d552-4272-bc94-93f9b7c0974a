<?php

namespace App\BillingAPI\Client;

use App\BillingAPI\Client\Exceptions\HasNotPaymentCardsException;
use App\BillingAPI\Client\Exceptions\OfferExpiredException;
use App\BillingAPI\Client\Exceptions\ReservationAlreadyExistsException;
use App\CardManager\Card\CardCollection;
use App\Exceptions\PaymentLimitExceededException;
use App\Helpers\Client\ClientInterface;
use App\BillingAPI\Client\Exceptions\OfferNotChosenException;
use App\BillingAPI\Client\Exceptions\OfferServiceNotFoundException;
use App\BillingAPI\Client\Services\OfferServiceFactory;
use App\Offer;
use App\Repositories\CardRepository;
use App\User;
use Illuminate\Support\Facades\Log;
use Modules\Accounting\Priv\Entities\Mpk;
use Psr\Log\LoggerInterface;

class Booker
{
    public const NO_CARDS = 'no_cards';

    /** @var \App\Helpers\Client\ClientInterface */
    protected $client;

    /** @var Offer */
    protected $offer;

    protected static $errors = [

    ];

    /** @var bool */
    protected $offerExpired = false;

    /** @var string */
    protected $errorMessage = '';

    protected LoggerInterface $logger;

    protected ReservationCommentService $reservationCommentService;

    /**
     * Booker constructor.
     * @param $offer
     * @param $additionalData
     */
    public function __construct(Offer $offer)
    {
        $this->offer = $offer;

        $this->logger  = resolve(LoggerInterface::class);
        $this->reservationCommentService = resolve(ReservationCommentService::class);
    }


    public function setClient(ClientInterface $client)
    {
        $this->client = $client;

        return $this;
    }

    /**
     * @param  CardCollection  $collection
     *
     * @return bool
     * @throws HasNotPaymentCardsException
     * @throws OfferNotChosenException
     */
    public function book(CardCollection $collection, bool $autoReserved): bool
    {
        if (!$this->offer->isChosen()) {
            throw new OfferNotChosenException();
        }

        $this->offer->waitingForFirstStatus($autoReserved);

        try {
            $response = $this->client->post($this->createBookingURI(), [
                'json'       => true,
                'bodyParams' => [
                    'cards'                 => $collection->getSlugs()
                        ->toArray(),
                    'attributes'                => $this->addAttributes(),
                    'mindento_request_slug'     => $this->offer->request->slug,
                    'mindento_element_id'       => $this->offer->request_element_id,
                    'mindento_element_type'     => $this->offer->request_element_type,
                    'mindento_request_number'   => $this->offer->request->number,
                    'mindento_request_mpk_code' => $this->offer->request->mpk instanceof Mpk ? $this->offer->request->mpk->code : null,
                    'mindento_request_mpk_name' => $this->offer->request->mpk instanceof Mpk ? $this->offer->request->mpk->name : null,
                ],
            ]);

            if (isset($response->success) === true && $response->success === false && $response->code ===  self::NO_CARDS) {
                throw new HasNotPaymentCardsException();
            }

            return true;
        } catch (HasNotPaymentCardsException $exception) {
            $this->errorMessage = $exception->getMessage();
            $this->offer->fail();
            $this->offer->setErrorMessageSLug($exception::getMessageSlug());


            return false;
        } catch (OfferExpiredException | PaymentLimitExceededException $exception) {
            $this->offerExpired = true;
            $this->errorMessage = $exception->getMessage();
            $this->offer->fail();
            $this->offer->setErrorMessageSLug($exception::getMessageSlug());

            return false;
        } catch (ReservationAlreadyExistsException $exception) {
            $this->offer->setErrorMessageSLug($exception::getMessageSlug());

            return true;
        } catch (\Throwable $exception) {
            Log::error($exception);
            $this->errorMessage = $exception->getMessage();
            $this->offer->fail();
            $this->offer->setErrorMessageSLug('error.server-error');

            return false;
        }
    }

    protected function addAttributes(): array
    {
        if ($this->offer->type === Offer::TYPE_PLANE) {
            return $this->addAttributesForPlane();
        }

        return $this->offer->getRequestedParsedAttributes();
    }

    //TODO: refactor na ladne klasy per offer type
    protected function addAttributesForPlane(): array
    {
        $targetFlightUuid = null;
        if ($this->offer->search_params['is_round_trip']) {
            $targetFlightUuid = explode('_', $this->offer->offer_uuid)[0];
        }

        return [
            'targetFlightUuid' => $targetFlightUuid,
            'services'         => $this->offer->getRequestedParsedAttributes(),
        ];
    }

    public function valuate(): bool
    {
        if (!$this->offer->isChosen()) {
            throw new OfferNotChosenException();
        }

        try {
            $response = $this->client->get($this->createValuationURI());

            try {
                $service = OfferServiceFactory::create($this->offer->type)
                    ->setClient($this->client);
            } catch (OfferServiceNotFoundException $e) {
                abort(422, trans('error.unrecognized-offer-service'));
            }

            $service->broadcastOffersChanged(
                $service->offersToResponse($service->getOffersByUuid($this->offer->search_uuid), $this->offer)
                    ->toArray(),
                $this->offer->search_uuid
            );

            return true;

        } catch (\Throwable $e) {
            return false;
        }

    }

    protected function createBookingURI()
    {
        return "/offers/{$this->getLastSelectedUuid($this->offer->offer_uuid)}/book/{$this->getLastSelectedUuid($this->offer->option_uuid)}";
    }

    protected function createValuationURI()
    {
        return "/offers/{$this->getLastSelectedUuid($this->offer->offer_uuid)}/valuate/{$this->getLastSelectedUuid($this->offer->option_uuid)}";
    }

    protected function getLastSelectedUuid(string $uuidsString)
    {
        $uuids = explode('_', $uuidsString);

        return array_pop($uuids);
    }

    public function isOfferExpired(): bool
    {
        return $this->offerExpired;
    }

    public function getErrorMessage(): string
    {
        return $this->errorMessage;
    }

    public function bookManual(User $currentUser): bool
    {
        $cards = (new \App\CardManager\Card\CardRepository(resolve(CardRepository::class), new \App\CardManager\Integration\Client()))
            ->getActiveCardsByUser($this->offer->request->user);

        try {
            $this->reservationCommentService->manualReservationStarted($this->offer, $currentUser);

            $result = $this->book($cards, false);

            $this->addComment($result, $currentUser);

            return $result;

        } catch (\Throwable $exception) {
            $this->logger->error((string)$exception);
            $this->addComment(false, $currentUser);

            return false;
        }
    }

    private function addComment(bool $result, User $currentUser): void
    {
        if ($result === true) {
            $this->reservationCommentService->manualReservationSuccess($this->offer, $currentUser);
        } else {
            $this->reservationCommentService->manualReservationFailed($this->offer, $currentUser);
        }
    }
}
