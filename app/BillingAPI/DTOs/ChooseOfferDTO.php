<?php


namespace App\BillingAPI\DTOs;


use Illuminate\Support\Facades\Log;

class ChooseOfferDTO
{
    /** @var string */
    protected $requestSlug;

    /** @var string */
    protected $searchUuid;

    /** @var string */
    protected $offerUuid;

    /** @var string */
    protected $optionUuid;

    /** @var array */
    protected $requestedAttributes;

    /** @var integer|null  */
    protected $requestElementId;

    /** @var string|null */
    protected $requestElementType;

    /**
     * ChooseOfferDTO constructor.
     * @param string $offerUuid
     * @param string $optionUuid
     * @param int $requestElementId
     * @param string $requestElementType
     */
    public function __construct(
        string $requestSlug,
        string $searchUuid,
        ?string $offerUuid,
        ?string $optionUuid,
        array $requestedAttributes,
        ?string $requestElementId,
        ?string $requestElementType
    ) {
        $this->requestSlug         = $requestSlug;
        $this->searchUuid          = $searchUuid;
        $this->offerUuid           = $offerUuid;
        $this->optionUuid          = $optionUuid;
        $this->requestedAttributes = $requestedAttributes;
        $this->requestElementId    = $requestElementId;
        $this->requestElementType  = $requestElementType;
    }

    /**
     * @param array $data
     * @return ChooseOfferDTO
     */
    public static function fromArray(array $data): ChooseOfferDTO
    {
        if(isset($data['offer_uuid']) && isset($data['paxes'])) {
            $offerUuids = explode('_', $data['offer_uuid']);

            $paxes = [];
            foreach($data['paxes'] as $requestTravelerSlug => $travlerServices) {
                $paxes[$requestTravelerSlug] = [];
                $servicesForSelectedOffers = [];
                if (isset($travlerServices['requestedServices']['services'])) {
                    foreach ($travlerServices['requestedServices']['services'] as $offerUuid => $services) {
                        if (in_array($offerUuid, $offerUuids)) {
                            $servicesForSelectedOffers[$offerUuid] = $services;
                        }
                    }
                    $paxes[$requestTravelerSlug]['services'] = $servicesForSelectedOffers;
                }
                if (isset($travlerServices['requestedServices']['both'])) {
                    $paxes[$requestTravelerSlug]['both'] = $travlerServices['requestedServices']['both'];
                }
            }
            $data['paxes'] = $paxes;
        } else {
            $data['paxes'] = [];
        }

        try {
            return new static(
                $data['request_slug'],
                $data['search_uuid'],
                $data['offer_uuid'] ?? null,
                $data['option_uuid'] ?? null,
                $data['attributes'] ?? $data['paxes'],
                $data['request_element']['id'] ?? null,
                $data['request_element']['type'] ?? null
            );
        } catch (\Throwable $exception) {
            Log::error($exception);
            abort(422, trans('error.invalid-request'));
        }
    }

    /**
     * @return string
     */
    public function getSearchUuid(): string
    {
        return $this->searchUuid;
    }

    /**
     * @return string
     */
    public function getOfferUuid(): ?string
    {
        return $this->offerUuid;
    }

    /**
     * @return string
     */
    public function getOptionUuid(): ?string
    {
        return $this->optionUuid;
    }

    /**
     * @return int
     */
    public function getRequestElementId(): ?int
    {
        return $this->requestElementId;
    }

    /**
     * @return string
     */
    public function getRequestElementType(): ?string
    {
        return $this->requestElementType;
    }

    /**
     * @return string
     */
    public function getRequestSlug(): string
    {
        return $this->requestSlug;
    }

    /**
     * @return array
     */
    public function getRequestedAttributes(): array
    {
        return $this->requestedAttributes;
    }
}
