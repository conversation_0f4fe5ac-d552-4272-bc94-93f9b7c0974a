<?php

namespace App\Ocr\DTO;

use Modules\Accounting\Priv\Modules\Provider\Exceptions\InvalidNipException;
use Modules\Accounting\Priv\Modules\Provider\ValueObjects\Nip;

class OcrPurchaserDto
{
    private string $registryNumber;

    private string $name;

    private string $countryCode;

    private string $address;

    private string $city;

    private string $postCode;

    /**
     * @throws InvalidNipException
     */
    public static function fromArray(array $purchaserData): self {
        $purchaser = new self();

        $purchaser->registryNumber = new Nip($purchaserData['registry_number'] ?? '');
        $purchaser->name = $purchaserData['name'] ?? '';
        $purchaser->countryCode = $purchaserData['postcode'] ?? '';
        $purchaser->address = $purchaserData['street'] ?? '';
        $purchaser->city = $purchaserData['city'] ?? '';
        $purchaser->postCode = $purchaserData['postcode'] ?? '';

        return $purchaser;
    }

    public function generateLabelForPurchaser(): string
    {
        return sprintf('%s (%s)', $this->name, $this->registryNumber);
    }

    public function getRegistryNumber(): string
    {
        return $this->registryNumber;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getPostCode(): string
    {
        return $this->postCode;
    }
}