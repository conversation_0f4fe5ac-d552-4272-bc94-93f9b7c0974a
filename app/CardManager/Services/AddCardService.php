<?php

declare(strict_types=1);

namespace App\CardManager\Services;

use App\CardManager\Card\Card;
use App\CardManager\DTOs\AddCardDTO;
use App\CardManager\Factories\CardFactory;
use App\CardManager\Integration\AddedCard;
use App\CardManager\Integration\AddCardInterface;
use App\Instance;
use App\Repositories\CardRepository;
use App\User;

class AddCardService
{
    protected CardRepository $cardRepository;
    private AddCardInterface $addCard;

    public function __construct(
        AddCardInterface $addCard,
        CardRepository $cardRepository
    ) {
        $this->cardRepository = $cardRepository;
        $this->addCard = $addCard;
    }

    public function add(
        Instance $instance,
        User $user,
        AddCardDTO $DTO,
        array $companies
    ): AddedCard {
        switch ($DTO->getType()) {
            case \App\Card::INDIVIDUAL_TYPE:
                return $this->addIndividual($DTO, $companies, $user);
            case \App\Card::CORPORATION_TYPE:
                return $this->addCorporate($DTO, $instance, $companies, $user);
            default:
                throw new \InvalidArgumentException('Invalid card type');
        }
    }

    public function addIndividual(AddCardDTO $DTO, array $companies, User $user): AddedCard
    {
        $addCardData = $this->sendRequest($DTO);
        
        $card = $this->createCard($addCardData, $DTO);

        if (count($companies) !== 1) throw new \InvalidArgumentException('Invalid company data');

        $this->cardRepository->create(array_merge($card->toArray(), [
            'users_id' => [$user->id],
            'instance_id' => $user->instance_id,
            'owner_id' => $user->id,
            'company_id' => $companies[0]
        ]));

        return $addCardData;
    }

    public function addCorporate(AddCardDTO $DTO, Instance $instance, array $companies, User $user): AddedCard
    {
        $addCardData = $this->sendRequest($DTO);

        $card = $this->createCard($addCardData, $DTO);

        $this->cardRepository->createForCompanies($companies, array_merge($card->toArray(), ['owner_id' => $user->id]), $instance);

        return $addCardData;
    }

    protected function createCard(AddedCard $addedCard, AddCardDTO $addCardDTO): Card
    {
        $card = CardFactory::fromAddedCard($addedCard);
        $card->type = $addCardDTO->getType();
        
        return $card;
    }

    protected function sendRequest(AddCardDTO $addCardDTO): AddedCard
    {
        return $this->addCard->add($addCardDTO);
    }
}
