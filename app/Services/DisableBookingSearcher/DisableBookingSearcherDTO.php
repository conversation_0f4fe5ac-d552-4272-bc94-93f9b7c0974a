<?php
/**
 * Created by PhpStorm.
 * User: patryk
 * Date: 21.02.19
 * Time: 12:05
 */

namespace App\Services\DisableBookingSearcher;


class DisableBookingSearcherDTO
{
    /** @var string */
    protected $requestSlug;

    /** @var string */
    protected $elementType;

    /** @var int */
    protected $elementId;

    /**
     * DisableBookingSearcherDTO constructor.
     * @param string $requestSlug
     * @param string $elementType
     * @param int $elementId
     */
    public function __construct(string $requestSlug, string $elementType, int $elementId)
    {
        $this->requestSlug = $requestSlug;
        $this->elementType = $elementType;
        $this->elementId = $elementId;
    }

    /**
     * @return string
     */
    public function getRequestSlug(): string
    {
        return $this->requestSlug;
    }

    /**
     * @return string
     */
    public function getElementType(): string
    {
        return $this->elementType;
    }

    /**
     * @return int
     */
    public function getElementId(): int
    {
        return $this->elementId;
    }

}