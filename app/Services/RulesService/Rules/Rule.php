<?php

namespace App\Services\RulesService\Rules;

use App\Instance;
use App\Services\RulesService\ConfigResolver;
use App\Services\RulesService\Contracts\RuleInterface;
use App\Services\RulesService\Exceptions\RuleDoesNotExistException;
use App\Services\RulesService\Message\RuleMessageCollection;
use App\User;

abstract class Rule implements RuleInterface
{

    public const COMPARE_VALUES_SIMPLE_REVERT = 'simple_revert';
    public const COMPARE_VALUES_SIMPLE = 'simple';
    public const COMPARE_VALUES_PERCENT = 'percent';

    protected array $config = [];
    protected $model;

    protected RuleDTO $rule;

    protected RuleMessageCollection $messages;

    protected bool $valid = false;

    protected ?User $user;

    abstract public function getName(): string;

    abstract public function validate(): void;

    final public function isValid(): bool
    {
        return $this->valid;
    }

    /**
     * Rule constructor.
     * @param $model
     * @param User|null $user
     * @throws \Throwable
     */
    public function __construct($model, Instance $instance, ?User $user = null)
    {
        $this->model = $model;
        $this->user = $user;
        $this->setRule($instance);
        $this->messages = new RuleMessageCollection();
    }

    public function __get($name)
    {
        if (isset($this->config[$name])) {
            return $this->config[$name];
        } else {
            if (is_callable([$this,])) {
                return $this->{'get' . ucfirst($name)};
            } else {
                return null;
            }
        }
    }

    public function __set($name, $value)
    {
    }

    public function __isset($name)
    {
        return isset($this->config[$name]) || isset($this->$name);
    }

    final public function getLevel(): string
    {
        return $this->rule->getLevel();
    }

    /**
     * @throws \Throwable
     */
    protected function setRule(Instance $instance)
    {
        $this->rule = app(RuleDTOFactory::class)->createRule($this->getName(), $instance->id);
        throw_if(!$this->rule, new RuleDoesNotExistException('Rule ' . $this->getName() . ' does not exist'));

        $this->config = (new ConfigResolver(
            $this->rule->getParameters(),
            $this->user,
            $this->model
        ))->resolve();
    }

    public function getMessages()
    {
        if (!isset($this->messages) || !empty($this->config['skip_message'])) {
            return new RuleMessageCollection();
        }

        return $this->messages;
    }

    protected function validateRules(array $validators): bool
    {
        foreach ($validators as $success) {
            if (!$success) {
                return false;
            }
        }

        return true;
    }

    public function getValidationValues(): array
    {
        return [];
    }

    public static function defaultConfiguration(): array
    {
        return [];
    }

    public function getLimits(): array
    {
        return $this->config;
    }
}
