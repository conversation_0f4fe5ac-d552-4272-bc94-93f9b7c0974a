<?php


namespace App\Services\RulesService\Rules;

use App\Document;
use App\Instance;
use App\Request;

class RequestHasAccountingDocuments extends Rule
{
    const NAME = 'request_has_accounting_documents';

    /** @var Request */
    protected $model;

    public function __construct(Request $model, Instance $instance)
    {
        parent::__construct($model, $instance);
    }

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->requestHasRequestedAmount();
    }

    protected function requestHasRequestedAmount(): bool
    {
        foreach ($this->model->documents as $document) {
            if ($document->type === Document::TYPE_ACCOUNTING) {
                return true;
            }
        }

        return false;
    }
}