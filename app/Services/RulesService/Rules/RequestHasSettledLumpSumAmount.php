<?php


namespace App\Services\RulesService\Rules;

use App\Instance;
use App\Request;
use App\RequestCache;
use App\Vendors\Math;

class RequestHasSettledLumpSumAmount extends Rule
{
    const NAME = 'request_has_settled_lump_sum_amount';

    /** @var Request */
    protected $model;

    public function __construct(Request $model, Instance $instance)
    {
        parent::__construct($model, $instance);
    }

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->requestHasSettledLumpSumpAmount();
    }

    protected function requestHasSettledLumpSumpAmount(): bool
    {
        $requestCache = $this->model->getRelatedRequestCacheOfType(RequestCache::TYPE_LUMP_SUMS_DECLARATION);

        if (!$requestCache instanceof RequestCache) {
            return false;
        }

        $lumpSumDeclaration = $requestCache->data;

        if (isset($lumpSumDeclaration['settled_lump_sums'])
            && Math::isGreaterThanZero($lumpSumDeclaration['settled_lump_sums'])) {
            return true;
        }

        return false;
    }
}