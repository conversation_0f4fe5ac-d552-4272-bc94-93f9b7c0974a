<?php

declare(strict_types=1);

namespace App\Services\RulesService\Rules;

use Modules\Analytics\Pub\Facades\AccountDimensionFacade;

class RequestHasAccountingDimensionsRule implements \Illuminate\Contracts\Validation\Rule
{
    protected const ARRAY_KEY_PREFIX = 'account-dimensions.';

    protected const EMPTY = '';

    protected AccountDimensionFacade $facade;

    public function __construct(AccountDimensionFacade $facade)
    {
        $this->facade = $facade;
    }

    /**
     * @inheritDoc
     */
    public function passes($attribute, $value)
    {
        $accountDimension = $this->facade->getBySlug(str_replace(self::ARRAY_KEY_PREFIX, self::EMPTY, $attribute));

        if($accountDimension->isRequired() && $value === null) {
            return false;
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function message()
    {
        return trans('validation.filled');
    }
}
