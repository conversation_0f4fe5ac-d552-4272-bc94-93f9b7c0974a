<?php


namespace App\Services\RulesService\Rules;

use App\Helpers\NameTranslation;
use App\Instance;
use Modules\Accounting\Priv\Repositories\MpkRepository;
use App\Request;
use App\Services\RulesService\Message\RuleMessage;

class RequestHasMPKRule extends Rule
{
    const NAME = 'request_has_mpk';

    /** @var Request */
    protected $model;

    /**
     * RequestNeedTargetPointRule constructor.
     * @param Request $request
     * @throws \Throwable
     */
    public function __construct(Request $model, Instance $instance)
    {
        parent::__construct($model, $instance);
    }

    public function getName(): string
    {
        return static::NAME;
    }

    public function validate(): void
    {
        $this->valid = $this->checkMPK();
    }

    protected function checkMPK()
    {
        $mpk = resolve(MpkRepository::class)->getById($this->model->mpk_id);
        if (!$mpk) {
            $this->messages->push(new RuleMessage($this, new NameTranslation('rules.request-not-selected-mpk'), false));
            return false;
        }

        return true;
    }
}
