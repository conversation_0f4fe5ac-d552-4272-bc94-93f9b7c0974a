<?php

declare(strict_types=1);

namespace App\Services\RequestPosting;

class AccountDimensionPostingDto
{
    private string $code;
    private string $name;
    private ?string $value;

    public function __construct(string $code, string $name, ?string $value)
    {
        $this->code = $code;
        $this->name = $name;
        $this->value = $value;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function getCode(): string
    {
        return $this->code;
    }
}