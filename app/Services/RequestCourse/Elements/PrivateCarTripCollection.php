<?php


namespace App\Services\RequestCourse\Elements;


use App\PrivateCarTrip;
use App\Request;
use App\Services\RequestCourse\Elements\Contracts\RequestCourseElementInterface;
use App\Services\RequestCourse\Location\LocationElement;
use App\Services\RequestCourse\Location\LocationElementsCollection;
use Illuminate\Support\Collection;

class PrivateCarTripCollection implements RequestCourseElementInterface
{
    use SwapCountryCodeForEstimationTrait;

    protected $request;
    protected $forEstimation;

    public function __construct(Request $request, bool $forEstimation = false)
    {
        $this->request = $request;
        $this->forEstimation = $forEstimation;
    }

    public function getElementsCollection(): Collection
    {
        return PrivateCarTrip::where(['request_id' => $this->request->id])->with(['departureLocation', 'destinationLocation'])->get();
    }

    public function unifyLocationElements(Collection $locations = null): LocationElementsCollection
    {
        if(!$locations) {
            $locations = $this->getElementsCollection();
        }

        $unifiedLocations = new LocationElementsCollection();

        $locations->each(function(PrivateCarTrip $privateCarTrip) use (&$unifiedLocations) {
            $unifiedLocations->push(new LocationElement($this->swapCountryCodeForEstimation($privateCarTrip->departureLocation, $privateCarTrip->destinationLocation, $this->forEstimation), $privateCarTrip->getStartDate(), false, $privateCarTrip->weight));
            $unifiedLocations->push(new LocationElement($privateCarTrip->destinationLocation, $privateCarTrip->getStartDate(), false, $privateCarTrip->weight+0.01));

            if($privateCarTrip->round_trip) {
                $unifiedLocations->push(new LocationElement($privateCarTrip->destinationLocation, $privateCarTrip->getEndDate(), false, $privateCarTrip->return_weight));
                $unifiedLocations->push(new LocationElement($privateCarTrip->departureLocation, $privateCarTrip->getEndDate(), false, $privateCarTrip->return_weight+0.01));
            }
        });

        return $unifiedLocations;
    }
}