<?php

declare(strict_types=1);

namespace App\Services\DefaultAcceptor\Strategies;

use App\Repositories\UserRepository;
use App\Request;
use App\Services\DefaultAcceptor\Collections\AcceptorsCollection;
use App\Services\DefaultAcceptor\Consts\AttachDefaultAcceptorStrategies;
use App\Services\DefaultAcceptor\Dtos\AcceptorDto;
use App\Services\DefaultAcceptor\Enums\AcceptingHierarchyStrategyEnum;
use App\User;

class DirectSupervisorBeforeDefinedUserStrategy extends AbstractStrategy
{
    protected StrategyInterface $dependentStrategy;

    protected UserRepository $userRepository;

    protected string $slug;

    public function __construct(
        Request $request,
        StrategyInterface $dependentStrategy,
        UserRepository $userRepository,
        AcceptingHierarchyStrategyEnum $acceptingHierarchyStrategyEnum,
        string $slug
    ) {
        parent::__construct($request, null, $acceptingHierarchyStrategyEnum);
        $this->dependentStrategy = $dependentStrategy;
        $this->userRepository = $userRepository;
        $this->slug = $slug;
    }

    private function getAcceptors(): AcceptorsCollection
    {
        $acceptors = new AcceptorsCollection();

        $directSupervisors = $this->dependentStrategy->defaultAcceptorsOnCreate();

        if ($directSupervisors->isNotEmpty()) {
            /**
             * @var AcceptorDto $directSupervisor
             */
            foreach ($directSupervisors as $directSupervisor) {
                $acceptors->push(new AcceptorDto($directSupervisor->getUser(), 0));
            }
        }

        /**
         * @var User $definedUser
         */
        $definedUser = $this->userRepository->findBySlugAndInstanceId($this->slug, $this->request->instance_id);
        if ($definedUser !== null && $acceptors->contains(function (AcceptorDto $acceptorDto) use ($definedUser) {
                return $acceptorDto->getUser()->id === $definedUser->id;
            }) === false) {
            $acceptors->push(new AcceptorDto($definedUser, $this->getNextStep($acceptors)));
        }

        return $acceptors;
    }

    public function defaultAcceptorsOnCreate(): AcceptorsCollection
    {
        return $this->getAcceptors();
    }

    public function defaultSettlementAcceptorsOnCreate(): AcceptorsCollection
    {
        $this->dependentStrategy->defaultAcceptorsOnCreate();
    }

    public function defaultAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->getAcceptors();
    }

    public function defaultSettlementAcceptorsOnSendToAcceptance(): AcceptorsCollection
    {
        return $this->dependentStrategy->defaultSettlementAcceptorsOnSendToAcceptance();
    }

    public function defaultSettlementAcceptorsOnSendToSettlementAcceptance(): AcceptorsCollection
    {
        return $this->defaultAcceptorsOnSendToAcceptance();
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }

    /**
     * @return AttachDefaultAcceptorStrategies
     */
    public function defaultSettlementAcceptorOverwriteStrategy(): AttachDefaultAcceptorStrategies
    {
        return new AttachDefaultAcceptorStrategies(AttachDefaultAcceptorStrategies::OVERWRITE_ALL);
    }

    public function getNextStep(AcceptorsCollection $collection): int
    {
        if ($collection->isEmpty()) {
            return 0;
        }

        $nextStep = $collection->getLastStep();
        if ($this->acceptingHierarchyStrategyEnum->equals(AcceptingHierarchyStrategyEnum::STEPPED())) {
            $nextStep++;
        }

        return $nextStep;
    }
}