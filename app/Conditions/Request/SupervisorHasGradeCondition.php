<?php

declare(strict_types=1);

namespace App\Conditions\Request;

use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;

class SupervisorHasGradeCondition extends AbstractRequestCondition
{
    public const CODE = 'supervisor_has_grade';

    protected string $grade;

    public function __construct(string $grade)
    {
        $this->grade = $grade;
    }

    public function check(DataSetInterface $data): bool
    {
        $request = $this->extractRequest($data);

        $user = $request->user;

        if (!$user->hasSupervisor()) {
            return false;
        }

        return $user->supervisor->grade === $this->grade;
    }
}
