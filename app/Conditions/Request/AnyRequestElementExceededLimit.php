<?php

declare(strict_types=1);

namespace App\Conditions\Request;

use App\Services\Summary\SummaryService;
use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;

class AnyRequestElementExceededLimit extends AbstractRequestCondition
{
    public const CODE = 'any_request_element_exceeded_limit';

    protected array $grades;

    public function __construct(array $grades = [])
    {
        $this->grades = $grades;
    }

    public function check(DataSetInterface $data): bool
    {
        $request = $this->extractRequest($data);

        if (!empty($this->grades) && !in_array($request->user->grade, $this->grades)) {
            return false;
        }

        $request = $data->get('request');
        $summaryService = resolve(SummaryService::class)->init($request, false);
        $summaryService->setCollection($request);

        return $summaryService->getRules()->where('valid', false)->isNotEmpty();
    }
}