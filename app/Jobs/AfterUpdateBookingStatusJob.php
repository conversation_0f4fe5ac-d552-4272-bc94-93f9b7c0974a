<?php

declare(strict_types=1);

namespace App\Jobs;

use App\BillingAPI\Client\Services\UpdateBookingStatusService;
use App\BillingAPI\DTOs\UpdateBookingStatusDTO;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Mindento\Tracer\Traits\Traceable;

class AfterUpdateBookingStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Traceable;

    /**
     * @var UpdateBookingStatusDTO
     */
    protected $dto;

    /**
     * AfterUpdateBookingStatusJob constructor.
     * @param UpdateBookingStatusDTO $dto
     */
    public function __construct(UpdateBookingStatusDTO $dto)
    {
        $this->dto = $dto;
    }

    public function handle()
    {
        (new UpdateBookingStatusService($this->dto))->afterUpdateAndBroadcast();
    }
}