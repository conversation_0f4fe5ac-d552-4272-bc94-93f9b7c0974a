<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;

class RemoveUnmanagedRequests extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'requests:remove-unmanaged';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '<PERSON><PERSON><PERSON> w<PERSON>, których data stworzenia i aktualizacji jest taka sama';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        return \App\Request::whereDate('created_at', '<', Carbon::yesterday()->toDateString())->whereRaw('created_at = updated_at')->delete();
    }
}
