<?php

namespace App\Console\Commands;

use App\Document;
use App\Ocr\OcrDocumentJob;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

/**
 * @deprecated
 */
class OcrProcess extends Command
{
    protected $signature = 'ocr:process';

    protected $description = 'Sets status filed to documents that where not processed during last two hours';

    public function handle()
    {
        $cursor = Document::with('instance.ocr')->where([
            'readonly' => false,
            'status' => Document::STATUS_PENDING
        ])
            ->where(function (Builder $builder) {
                $builder->where(['type' => Document::TYPE_ACCOUNTING])
                    ->orWhereNull('type');
            })
            ->cursor();

        /** @var Document $document */
        foreach ($cursor as $document) {
            dispatch(new OcrDocumentJob($document));
        }
    }
}
