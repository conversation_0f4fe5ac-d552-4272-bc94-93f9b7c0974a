<?php

namespace App\Http\Responses;

use App\Document;

class DocumentMoneyResponse extends Response2
{
    /** @var Document $document */
    protected $document;

    protected function transform(Document $document): array
    {
        $this->document = $document;

        $response = [
            'gross'                      => $document->gross,
            'gross_original_converted'   => $document->getGrossAmountInInstanceCurrency(),
            'base_net'                   => $document->getBaseNetAmountInDocumentCurrency(),
            'net'                        => $document->net,
            'net_original_converted'     => $document->getOriginalConvertedNetAmount(),
            'tax_original'               => $document->getOriginalTaxAmount(),
            'tax_original_converted'     => $document->getOriginalConvertedTaxAmount(),
            'base_tax'                   => $document->getBaseTaxAmount(),
            'tax'                        => $document->tax,
            'reverse_tax'                => $document->reverse_tax,
            'gross_sum_of_elements' => $document->grossSettledAmount(),
            'net_sum_of_elements'      => $document->netSettledAmount(),
            'converted_gross_sum_of_elements' => $document->grossConvertedSettledAmount(),
            'converted_net_sum_of_elements' => $document->netConvertedSettledAmount(),
            'converted_tax_sum_of_elements' => $document->getVatInDefaultInstanceCurrency(),
            'converted_base_tax_sum_of_elements' => $document->getBaseTaxInDefaultInstanceCurrency(),
            'converted_base_net_sum_of_elements' => $document->getBaseNetAmount(),
        ];

        return $response;
    }
}
