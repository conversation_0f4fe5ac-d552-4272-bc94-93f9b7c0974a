<?php

namespace App\Http\Responses\Auth;

use App\Http\Responses\LocationResponse;
use App\Http\Responses\Response2;
use App\IdentityProviders\IdentityProviderFacade;
use App\Instance;
use App\Translation;
use Mindento\Slack\UserInterface\Facade\SlackFacadeInterface;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;
use Modules\FeatureSwitcher\Pub\Enums\IntegrationModeEnum;
use Modules\FeatureSwitcher\Pub\Facades\FeatureSwitcherFacade;


class InstanceBasicsResponse extends Response2
{
    protected FeatureSwitcherFacade $featureSwitcherFacade;

    protected IdentityProviderFacade $identityProviderFacade;

    public function init()
    {
        parent::init();

        $this->featureSwitcherFacade = resolve(FeatureSwitcherFacade::class);
        $this->identityProviderFacade = resolve(IdentityProviderFacade::class);
    }

    public function transform(Instance $instance)
    {
        //return only basics data without secrets
        $instance->load(['currency', 'country']);

        $mpkFeatureEnabled = \Auth::user() !== null && $this->featureSwitcherFacade->isEnabledForCompany(
                FeatureEnum::FEATURE_MANAGE_MPK_ENABLED(),
                \Auth::user()->company
            );

        $response = [
            'instance' => [
                'id' => $instance->id,
                'identity_provider_url' => $this->identityProviderFacade->getIdentityProviderUrl($instance),
                'name' => $instance->name,
                'locale' => $instance->locale,
                'default_lang' => (new Translation())->getShortNameFromLocale($instance->locale),
                'domain' => $instance->domain,
                'subdomain' => $instance->subdomain,
                'currency' => $instance->currency->code,
                'country' => $instance->country->name,
                'ios_version' => $instance->ios_version,
                'android_version' => $instance->android_version,
                'ios_force_update' => $instance->ios_force_update,
                'android_force_update' => $instance->android_force_update,
                'location' => LocationResponse::item($instance->location)->getData(),
                'modules' => $instance->modules,
                'env' => \App::environment(),
                'blocked_at' => $this->dateTimeFormat($instance->blocked_at),
                'isBlocked' => $instance->isBlocked(),
                'socket_url' => $instance->getSocketURL(),
                'demo_mode' => $instance->demo_mode,
                'sso_login' => (bool)$instance->ssoSetting,
                'trip_agent' => $instance->trip_agent,
                'slack' => [
                    'installed' => $slackStatus = resolve(SlackFacadeInterface::class)->isInstanceInstalled(
                        $instance->id
                    ),
                    'message' => $slackStatus ? trans('slack::slack.installation_done') : trans(
                        'slack::slack.installation_todo'
                    ),
                ],
                'features' => [
                    (string)FeatureEnum::FEATURE_IMPORT_FILE_INTEGRATION_ENABLED() => \Auth::user(
                        ) !== null && $this->featureSwitcherFacade->isEnabledForCompany(
                            FeatureEnum::FEATURE_IMPORT_FILE_INTEGRATION_ENABLED(),
                            \Auth::user()->company
                        ),
                    (string)FeatureEnum::FEATURE_DEDUCTIBILITY_ENABLED() => \Auth::user(
                        ) !== null && $this->featureSwitcherFacade->isEnabledForCompany(
                            FeatureEnum::FEATURE_DEDUCTIBILITY_ENABLED(),
                            \Auth::user()->company
                        ),
                    (string)FeatureEnum::FEATURE_INTEGRATION_MODE() => \Auth::user() !== null ? (string)$this->featureSwitcherFacade->getEnumValue(FeatureEnum::FEATURE_INTEGRATION_MODE(), \Auth::user()->company)->getEnum() : IntegrationModeEnum::DISABLED(),
                    (string)FeatureEnum::FEATURE_MANAGE_MPK_ENABLED() => $mpkFeatureEnabled,
                    (string)FeatureEnum::FEATURE_MANAGE_DOCUMENT_MPK_ENABLED() => $mpkFeatureEnabled && (\Auth::user(
                            ) !== null && $this->featureSwitcherFacade->isEnabledForCompany(
                                FeatureEnum::FEATURE_MANAGE_DOCUMENT_MPK_ENABLED(),
                                \Auth::user()->company
                            )),
                    (string)FeatureEnum::FEATURE_MANAGE_DOCUMENT_PROJECT_ENABLED() => \Auth::user(
                        ) !== null && $this->featureSwitcherFacade->isEnabledForCompany(
                            FeatureEnum::FEATURE_MANAGE_DOCUMENT_PROJECT_ENABLED(),
                            \Auth::user()->company
                        ),
                ]
            ],
            'locale' => $instance ? $instance->locale : \Lang::getLocale(),
        ];

        $this->translations($instance, $response);
        $this->loginPageTrans($instance, $response);

        return $response;
    }

    protected function translations($instance, &$response): void
    {
        if ($this->isParamInWithParams('translations')) {
            $response['translations'] = (new Translation())->all();
        }
    }

    protected function loginPageTrans($instance, &$response)
    {
        if (request()->has('lang')) {
            $trans = (new Translation());
            $lang = $trans->getLocaleFromShortName(request()->get('lang'));

            $response['translations'] = $trans->groupsByLocale($lang, ['login-page']);
        }
    }
}
