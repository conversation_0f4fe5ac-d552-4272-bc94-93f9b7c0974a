<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Modules\MyCard\Pub\Facades\MyCardUserResponseFacade;

class UsersCurrentInstanceResponse extends Response2Paginated
{
    protected MyCardUserResponseFacade $myCardResponseFacade;

    protected function init()
    {
        $this->myCardResponseFacade = resolve(MyCardUserResponseFacade::class);
    }

    protected function transform(\stdClass $user)
    {
        $response = [
            'id'          => $user->id,
            'first_name'  => $user->first_name,
            'last_name'   => $user->last_name,
            'full_name'   => $user->full_name,
            'slug'        => $user->slug,
            'email'       => $user->email,
            'blocked_at'  => $this->dateTimeFormat($user->blocked_at),
            'company' => [
                'code' => $user->company_code
            ],
            'my_card' => $this->myCardResponseFacade->getDataForUser($user->id),
        ];

        if (is_string($user->supervisor_first_name) === true && is_string($user->supervisor_last_name) === true) {
            $response['supervisor'] = [
                'first_name'  => $user->supervisor_first_name,
                'last_name'   => $user->supervisor_last_name,
                'full_name'   => $user->supervisor_full_name
            ];
        }

        return $response;
    }
}
