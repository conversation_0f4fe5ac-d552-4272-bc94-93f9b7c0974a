<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Modules\Accounting\Pub\ViewObjects\DocumentElementAccountDimensionItemViewObject;

class DocumentElementAccountDimensionItemResponse extends Response2
{
    protected function transform(DocumentElementAccountDimensionItemViewObject $item)
    {
        return [
            'id' => $item->getId(),
            'slug' => $item->getSlug(),
            'document_id' => $item->getDocumentId(),
            'document_element_id' => $item->getDocumentElementId(),
            'account_dimension_id' => $item->getAccountDimensionId(),
            'account_dimension_item_id' => $item->getAccountDimensionItemId(),
            'accountDimensionItem' => AccountDimensionItemResponse::item($item->getAccountDimensionItem())->getData()
        ];
    }
}
