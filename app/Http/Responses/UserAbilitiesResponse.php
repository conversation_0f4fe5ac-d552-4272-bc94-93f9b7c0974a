<?php

declare(strict_types=1);

namespace App\Http\Responses;

use App\Permission;
use App\User;
use Modules\MyCard\Priv\Policies\MyCardPolicy;

class UserAbilitiesResponse extends Response2
{
    protected function transform(User $user)
    {
        return [
            'isAccountant' => $user->isAccountant(),
            'isSuperAdmin' => $user->isSuperAdmin(),
            'isAdmin' => $user->isAdmin(),
            'isInstanceAdmin' => $user->isSuperAdmin() || $user->isAdmin(),
            'isController' => $user->isController(),
            'isAgent' => $user->isAgent(),
            'isRegular' => $user->isRegular(),
            'isManager' => $user->isManager(),

            'clearInstance' => $user->can(Permission::CLEAR_INSTANCE, $user->instance),
            'showExpense' => $user->hasAbility(Permission::SHOW_EXPENSE),
            'showTransaction' => $user->hasAbility(Permission::SHOW_TRANSACTION),
            'showReports' => $user->can('showReportsPage', $user),
            'showCockpit' => $user->can('showCockpit', $user),
            'showInstallments' => $user->can('showInstallmentPage', $user),
            'showSettlements' => $user->can('showSettlementsPage', $user),
            'showPeriodicExpense' => $user->hasAbility(Permission::PERIODIC_REQUEST_EXPENSE),
            'showRegularExpense' => $user->hasAbility(Permission::REGULAR_REQUEST_EXPENSE),
            'showRegularTrip' => $user->hasAbility(Permission::REGULAR_REQUEST_TRIP),
            Permission::SHOW_INVOICE => $user->hasAbility(Permission::SHOW_INVOICE),
            'showTrip' => $user->hasAbility(Permission::REGULAR_TRIP),

            'userCreate' => $user->hasAbility(Permission::USER_CREATE),
            'userChangeSensitiveData' => $user->hasAbility(Permission::USER_CHANGE_SENSITIVE_DATA),
            'dictionariesManage' => $user->hasAbility(Permission::DICTIONARIES_MANAGE),
            'assignAllowancesToRequest' => $user->hasAbility(Permission::ASSIGN_ALLOWANCES_TO_REQUEST),
            Permission::CLAIMS_PAGE_REPORT => $user->hasAbility(Permission::CLAIMS_PAGE_REPORT),
            Permission::INSTANCE_SETTINGS_MANAGE_USERS => $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS),
            Permission::INSTANCE_SETTINGS_MANAGE_COMPANY => $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_COMPANY),
            Permission::INSTANCE_SETTINGS_MANAGE_COMPANY_CARDS => $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_COMPANY_CARDS),
            Permission::MYCARD_PAYMENTS => $user->can(MyCardPolicy::PAYMENTS),
            Permission::MYCARD_STATEMENTS => $user->can(MyCardPolicy::STATEMENTS),
            Permission::MYCARD_CARD_ISSUING => $user->hasAbility(Permission::MYCARD_CARD_ISSUING),
            Permission::SHOW_PRIVATE_ACCOMMODATION_TYPE => $user->can(Permission::SHOW_PRIVATE_ACCOMMODATION_TYPE, $user),
            Permission::SHOW_PROVIDED_ACCOMMODATION_TYPE => $user->can(Permission::SHOW_PROVIDED_ACCOMMODATION_TYPE, $user),
            Permission::INTEGRATIONS => $user->can(Permission::INTEGRATIONS, $user),
        ];
    }
}
