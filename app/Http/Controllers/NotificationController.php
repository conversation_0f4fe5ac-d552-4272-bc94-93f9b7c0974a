<?php

namespace App\Http\Controllers;

use App\Http\Requests\Notification\ReadNotificationRequest;
use App\Http\Responses\NotificationIndexResponse;
use App\Repositories\NotificationRepository;
use App\User;
use Illuminate\Http\Request;
use Modules\Common\Dtos\OffsetPageDto;

class NotificationController extends Controller
{
    /**
     * @return static
     */
    public function index(NotificationRepository $notificationRepository, Request $request)
    {
        $notifications = $notificationRepository->getPaginatedForUser($request->user(), OffsetPageDto::fromRequest());

        return NotificationIndexResponse::collection($notifications);
    }

    public function read(NotificationRepository $notificationRepository, ReadNotificationRequest $request)
    {
        $notifications = collect();

        foreach (\Auth::user()->notifications as $notification) {
            if (in_array($notification->id, $request->get('ids'))) {
                $notification->markAsRead();
                $notifications->push($notification);
            }
        }

        $notifications = $notificationRepository->getPaginatedForUser($request->user(), OffsetPageDto::fromRequest());

        return NotificationIndexResponse::collection($notifications);
    }

    public function test($email)
    {
        $user = User::where(['email' => $email])->first();
        if($user) {
            \Auth::setUser($user);
            \Artisan::call('mail:test', ['type' => 'all', 'address' => $user->email]);
        }

        return [];
    }
}