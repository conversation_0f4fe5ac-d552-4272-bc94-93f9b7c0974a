<?php

namespace App\Http\Controllers;

use App\Company\Policy;
use App\Http\Requests\CurrentInstanceRequest;
use App\Http\Responses\CompanyIndexResponse;
use App\Http\Responses\CompanyResponse;
use App\Repositories\CompanyRepository;
use Illuminate\Http\Request;
use Modules\Common\Dtos\OffsetPageDto;

/**
 * Class CompanyController
 * @package App\Http\Controllers
 */
class CompanyController extends Controller {
    public function index(CompanyRepository $companyRepository, Request $request) {
        return CompanyIndexResponse::collection($companyRepository->getCompanyIndex(OffsetPageDto::fromRequest(), $request->user()->instance));
    }

    /**
     * @param CompanyRepository $companyRepository
     * @return $this
     * @throws \Exception
     */
    public function store(CompanyRepository $companyRepository, CurrentInstanceRequest $request) {
        $company = $companyRepository->create(array_merge([
            request()->all(),
            [
                'instance_id' => $request->getCurrentInstance()->id
            ]
        ]));

        return CompanyResponse::item($company)->addInfo(trans('info.company-has-been-saved'));
    }

    /**
     * @param $company_id
     * @param CompanyRepository $companyRepository
     * @return $this
     * @throws \Exception
     */
    public function update($company_id, CompanyRepository $companyRepository) {
        $company = $companyRepository->update($company_id, request()->all());

        return CompanyResponse::item($company)->addInfo(trans('info.company-has-been-saved'));
    }

    /**
     * @param $company_id
     * @param CompanyRepository $companyRepository
     * @return $this
     * @throws \Exception
     */
    public function destroy($company_id, CompanyRepository $companyRepository) {
        $company = $companyRepository->delete($company_id);

        return CompanyResponse::item($company)->addInfo(trans('info.company-has-been-saved'));
    }

    /**
     * @param  string  $fileName
     * @param  string  $token
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function companyPolicy(Request $request, string $fileName, string $token)
    {
        /** @var Policy $policy */
        $policy = resolve(Policy::class)->getPolicy($fileName, $token);

        !$policy && abort(403);

        if($policy->isPDF){
            return response()->make($policy->content, 200, [
                'Content-Type'=> 'application/pdf',
                'Content-Disposition' => 'inline; filename="'.$policy->name.'"'
            ]);
        } else{
            return response()->download($policy->path, $policy->name);
        }
    }
}
