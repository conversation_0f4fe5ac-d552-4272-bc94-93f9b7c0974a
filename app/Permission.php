<?php

namespace App;

use App\Traits\InstanceTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection;

/**
 * App\Permission
 *
 * @property string ability
 * @property int scope_instance
 * @property int scope_company
 * @property int scope_direct_subordinates
 * @property int scope_all_subordinates
 * @property int scope_own
 * @property int group_id
 * @property string name
 * @property int instance_id
 * @property string scope
 * @property int id
 * @property \Illuminate\Support\Carbon|null created_at
 * @property \Illuminate\Support\Carbon|null updated_at
 * @property int can
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Group[] group
 * @property-read int|null group_count
 * @method static \Illuminate\Database\Eloquent\Builder|Permission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission query()
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereAbility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereCan($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereInstanceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereScope($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Permission whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property-read \App\Instance instance
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $group_id
 * @property string $ability
 * @property string|null $scope
 * @property string $name
 * @property int $can
 * @property int $instance_id
 * @property-read \App\Group $group
 * @property-read \App\Instance $instance
 */
class Permission extends Model
{
    use InstanceTrait;

    /**
     * User can create request
     */
    public const REQUEST_CREATE = 'request_create';

    /**
     * User can accept own request
     */
    public const REQUEST_ACCEPT_OWN_REQUEST = 'request_accept_own_request';

    /**
     * User can accept own request settlement
     */
    public const REQUEST_ACCEPT_OWN_SETTLEMENT = 'request_accept_own_settlement';

    /**
     *  User can read request in the same instance
     */
    public const REQUEST_READ_INSTANCE_SCOPE = 'request_read_instance_scope';

    /**
     *  User can read request in the same company
     */
    public const REQUEST_READ_COMPANY_SCOPE = 'request_read_company_scope';

    /**
     *  User can read request of his subordinates
     */
    public const REQUEST_READ_SUBORDINATES_SCOPE = 'request_read_subordinates_scope';

    /**
     * User can read request which is an acceptor.
     */
    public const REQUEST_READ_IS_ACCEPTOR_SCOPE = 'request_read_is_acceptor_scope';

    /**
     * User can read own request
     */
    public const REQUEST_READ_OWN = 'request_read_own';

    /**
     *  User can account request
     */
    public const REQUEST_ACCOUNTING = 'request_accounting';

    /**
     * User can manage credit cards
     */
    public const CREDIT_CARD_MANAGE = 'credit_card_manage';

    /**
     * User can change integration settings
     */
    public const INTEGRATION_SETTINGS = 'integration_settings';

    /**
     * User can manage compliance rules
     */
    public const COMPLIANCE_RULES_MANAGE = 'compliance_rules_manage';

    /**
     * User can create another users
     */
    public const USER_CREATE = 'user_create';

    /**
     * User can manage another users
     */
    public const USER_MANAGE = 'user_manage';

    /**
     * User can manage companies
     */
    public const COMPANY_MANAGE = 'company_manage';

    /**
     * User can get information about all users, their groups and instances (for now)
     */
    public const MULTI_INSTANCE_VIEW = 'multi_instance_view';

    /**
     * User can get information about all users, their groups and instances (for now)
     */
    public const ADD_DOCUMENT_BY_API = 'add_document_by_api';

    /**
     * User can see expense menu.
     */
    public const SHOW_EXPENSE = 'show_expense';

    public const SHOW_INVOICE = 'showInvoice';

    /**
     * User can see transaction menu.
     */
    public const SHOW_TRANSACTION = 'show_transaction';

    /**
     * User can see report menu.
     */
    public const SHOW_REPORT = 'show_report';

    /**
     * User can see installments menu.
     */
    public const SHOW_INSTALLMENT = 'showInstallment';

    /**
     * User can see settlement menu.
     */
    public const SHOW_SETTLEMENT = 'show_settlement';

    /**
     * User can see lump sums.
     */
    public const SHOW_TRIP_REQUEST_LUMP_SUM = 'showTripRequestLumpSum';

    /**
     * User can see provided accommodation type.
     */
    public const SHOW_PROVIDED_ACCOMMODATION_TYPE = 'showProvidedAccommodationType';

    /**
     * User can see private accommodation type.
     */
    public const SHOW_PRIVATE_ACCOMMODATION_TYPE = 'showPrivateAccommodationType';

    /**
     * User can change sensitive data
     */
    public const USER_CHANGE_SENSITIVE_DATA = 'user_change_sensitive_data';

    /**
     * User can manage dictionaries
     */
    public const DICTIONARIES_MANAGE = 'dictionaries_manage';

    /** User can use periodic request expense */
    public const PERIODIC_REQUEST_EXPENSE = 'periodic-request-expense';

    /** @var string Permission for regular expenses */
    public const REGULAR_REQUEST_EXPENSE = 'regular-request-expense';

    /** @var string Permission for regular trip */
    public const REGULAR_REQUEST_TRIP = 'regular-request-trip';

    /** @var string User can see trip menu */
    public const REGULAR_TRIP = 'regular-trip';

    /** @var string User can edit allowances on request draft */
    public const ASSIGN_ALLOWANCES_TO_REQUEST = 'assign-allowances-to-request';

    /** @var string User has access to report at claims page */
    public const CLAIMS_PAGE_REPORT = 'claimsPageReport';

    /** @var string User can see reports tab */
    public const REPORTS_SECTION = 'reportsSection';

    /** @var string User has access to cockpit page */
    public const SHOW_COCKPIT = 'showCockpit';

    /** @var string User has access to integration api */
    public const INTEGRATION_API_ACCESS = 'integrationAPIAccess';

    /** @var string User can accept requests marked as private */
    public const PRIVATE_REQUEST_ACCEPTOR = 'privateRequestAcceptor';

    public const CLEAR_INSTANCE = 'clearInstance';

    /**
     * @var string User can see list of users
     */
    public const INSTANCE_SETTINGS_MANAGE_USERS = 'instanceSettingsManageUsers';

    /**
     * @var string User can see company settings
     */
    public const INSTANCE_SETTINGS_MANAGE_COMPANY = 'instanceSettingsManageCompany';

    /**
     * @var string User can see company cards settings
     */
    public const INSTANCE_SETTINGS_MANAGE_COMPANY_CARDS = 'instanceSettingsManageCompanyCards';

    /**
     * @var string User can change another user's password without passing old one
     */
    public const MANAGE_PASSWORD = 'managePassword';

    /**
     * @var string User can manage another user's groups/roles
     */
    public const MANAGE_ROLE = 'manageRole';

    /**
     * @var string User can manage another user's account access
     */
    public const MANAGE_ACCOUNT_ACCESS = 'manageAccountAccess';

    /**
     * @var string User is a employee from a region
     */
    public const REGIONAL_POSITION = 'regionalPosition';

    public const MYCARD_PAYMENTS = 'mycardPayments';

    public const MYCARD_STATEMENTS = 'mycardStatements';

    public const MYCARD_ACCOUNT_STATEMENTS = 'mycardAccountStatements';

    public const MYCARD_CARD_ISSUING = 'mycardCardIssuing';

    public const DOCUMENT_UPLOAD_AND_DELETE = 'documentUploadAndDelete';

    public const ACCOUNT_DELEGATION = 'accountDelegation';
    public const SHOW_OTHER_COSTS = 'showOtherCosts';

    public const SLACK_INSTALL = 'slackInstall';

    public const INTEGRATIONS = 'integrations';

    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class);
    }

    /**
     * Get collection of const abilities from this model.
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getAbilities(): Collection
    {
        return collect([
            self::REQUEST_CREATE,
            self::REQUEST_ACCEPT_OWN_REQUEST,
            self::REQUEST_ACCEPT_OWN_SETTLEMENT,
            self::REQUEST_READ_INSTANCE_SCOPE,
            self::REQUEST_READ_COMPANY_SCOPE,
            self::REQUEST_READ_SUBORDINATES_SCOPE,
            self::REQUEST_READ_IS_ACCEPTOR_SCOPE,
            self::REQUEST_READ_OWN,
            self::REQUEST_ACCOUNTING,
            self::CREDIT_CARD_MANAGE,
            self::INTEGRATION_SETTINGS,
            self::COMPLIANCE_RULES_MANAGE,
            self::USER_CREATE,
            self::USER_MANAGE,
            self::COMPANY_MANAGE,
            self::MULTI_INSTANCE_VIEW,
            self::ADD_DOCUMENT_BY_API,
            self::SHOW_EXPENSE,
            self::SHOW_TRANSACTION,
            self::SHOW_REPORT,
            self::SHOW_INSTALLMENT,
            self::SHOW_SETTLEMENT,
            self::SHOW_TRIP_REQUEST_LUMP_SUM,
            self::SHOW_PROVIDED_ACCOMMODATION_TYPE,
            self::SHOW_PRIVATE_ACCOMMODATION_TYPE,
            self::USER_CHANGE_SENSITIVE_DATA,
            self::DICTIONARIES_MANAGE,
            self::PERIODIC_REQUEST_EXPENSE,
            self::REGULAR_REQUEST_EXPENSE,
            self::REGULAR_REQUEST_TRIP,
            self::REGULAR_TRIP,
            self::ASSIGN_ALLOWANCES_TO_REQUEST,
            self::CLAIMS_PAGE_REPORT,
            self::REPORTS_SECTION,
            self::SHOW_COCKPIT,
            self::INTEGRATION_API_ACCESS,
            self::PRIVATE_REQUEST_ACCEPTOR,
            self::CLEAR_INSTANCE,
            self::INSTANCE_SETTINGS_MANAGE_USERS,
            self::INSTANCE_SETTINGS_MANAGE_COMPANY,
            self::INSTANCE_SETTINGS_MANAGE_COMPANY_CARDS,
            self::MANAGE_PASSWORD,
            self::MANAGE_ROLE,
            self::MANAGE_ACCOUNT_ACCESS,
            self::REGIONAL_POSITION,
            self::MYCARD_PAYMENTS,
            self::MYCARD_STATEMENTS,
            self::MYCARD_ACCOUNT_STATEMENTS,
            self::MYCARD_CARD_ISSUING,
            self::DOCUMENT_UPLOAD_AND_DELETE,
            self::ACCOUNT_DELEGATION,
            self::SHOW_OTHER_COSTS,
            self::SLACK_INSTALL,
            self::SHOW_INVOICE,
            self::INTEGRATIONS,
        ]);
    }

    public static function getCamelCased(string $name): string
    {
        return camel_case($name);
    }
}
