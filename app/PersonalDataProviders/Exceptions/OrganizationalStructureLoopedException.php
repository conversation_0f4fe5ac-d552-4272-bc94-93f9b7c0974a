<?php
/**
 * Created by PhpStorm.
 * User: patry<PERSON>
 * Date: 31.07.19
 * Time: 18:35
 */

namespace App\PersonalDataProviders\Exceptions;


use App\PersonalDataProviders\Contracts\PersonalDataProvidersExceptionInterface;
use Exception;

class OrganizationalStructureLoopedException extends Exception implements PersonalDataProvidersExceptionInterface
{
    protected $supervisorEmail;
    protected $userEmail;

    public function __construct(string $supervisorEmail, string $userEmail)
    {
        $this->supervisorEmail = $supervisorEmail;
        $this->userEmail = $userEmail;

        parent::__construct("Invalid structure user {$supervisorEmail} is both supervisor and subordinate of $userEmail");
    }


    public function toNotification(): string
    {
        return sprintf('Struktura jest nieprawidłowa, użytkownik o emailu %s jest zarówno przełożonym jak i podwładnym użytkownika o emailu %s',
            $this->supervisorEmail, $this->userEmail);
    }
}
