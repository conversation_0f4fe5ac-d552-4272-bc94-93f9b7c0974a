<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Instance;

class InstanceViewObject
{
    protected CurrencyViewObject $currency;
    private string $countryCode;
    private int $id;

    public function __construct(
        int $id,
        CurrencyViewObject $currency,
        string $countryCode
    ) {
        $this->id = $id;
        $this->currency = $currency;
        $this->countryCode = $countryCode;
    }

    public static function createFromInstance(Instance $instance): InstanceViewObject
    {
        return new static(
            $instance->id,
            CurrencyViewObject::getDefaultInstanceCurrency($instance),
            $instance->country->country_code
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCurrency(): CurrencyViewObject
    {
        return $this->currency;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }
}
