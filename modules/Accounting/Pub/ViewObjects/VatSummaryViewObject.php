<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\DocumentElement;
use App\Vendors\Math;
use Illuminate\Support\Collection;

class VatSummaryViewObject
{
    protected VatNumberViewObject $vatNumber;

    protected AccountingAccountViewObject $accountingAccount;

    protected string $netAmount;

    protected string $vatAmount;

    protected string $grossAmount;

    protected string $foreignNetAmount;

    protected string $foreignVatAmount;

    protected string $foreignGrossAmount;
    private ?string $localCurrencyReverseTaxAmount;
    private ?string $foreignCurrencyReverseTaxAmount;

    public function __construct(
        VatNumberViewObject $vatNumber,
        AccountingAccountViewObject $accountingAccount,
        string $netAmount,
        string $vatAmount,
        string $grossAmount,
        string $foreignNetAmount,
        string $foreignVatAmount,
        string $foreignGrossAmount,
        ?string $reverseTaxAmount,
        ?string $foreignCurrencyReverseTaxAmount = null
    ) {
        $this->vatNumber = $vatNumber;
        $this->accountingAccount = $accountingAccount;
        $this->netAmount = $netAmount;
        $this->vatAmount = $vatAmount;
        $this->grossAmount = $grossAmount;
        $this->foreignNetAmount = $foreignNetAmount;
        $this->foreignVatAmount = $foreignVatAmount;
        $this->foreignGrossAmount = $foreignGrossAmount;
        $this->localCurrencyReverseTaxAmount = $reverseTaxAmount;
        $this->foreignCurrencyReverseTaxAmount = $foreignCurrencyReverseTaxAmount;
    }

    public static function createFromElementsCollection(Collection $elements): Collection
    {
        $vatSummaries = Collection::make();

        /** @var DocumentElement $documentElement */
        foreach ($elements as $documentElement) {
            $taxCode = $documentElement->vatNumber->code;
            if ($vatSummaries->has($taxCode)) {
                $vatSummaries->get($taxCode)->addDocumentElement($documentElement);
            } else {
                $vatLine = self::createFromDocumentElement($documentElement);

                $vatSummaries->put($taxCode, $vatLine);
            }
        }

        return $vatSummaries->values()->sort(function (VatSummaryViewObject $a, VatSummaryViewObject $b) {
            if ($a->getVatNumber()->getValue() === $b->getVatNumber()->getValue()) {
                return 0;
            }

            return $a->getVatNumber()->getValue() < $b->getVatNumber()->getValue() ? -1 : 1;
        })->values();
    }

    public static function createFromDocumentElement(DocumentElement $documentElement): VatSummaryViewObject
    {
        $vatNumber = VatNumberViewObject::createFromVatNumber($documentElement->vatNumber);
        return new static(
            $vatNumber,
            $vatNumber->getAccountingAccount(),
            $documentElement->getNetInDefaultInstanceCurrency(),
            $documentElement->getVatInDefaultInstanceCurrency(),
            $documentElement->getGrossInDefaultInstanceCurrency(),
            $documentElement->getNetInDocumentCurrency(),
            $documentElement->getVatInDocumentCurrency(),
            $documentElement->getGrossInDocumentCurrency(),
            (string)$documentElement->reverse_tax,
            $documentElement->getReverseTaxInDocumentCurrency(),
        );
    }

    public function getLocalCurrencyReverseTaxAmount(): ?string
    {
        return $this->localCurrencyReverseTaxAmount;
    }
    
    public function getForeignCurrencyReverseTaxAmount(): ?string
    {
        return $this->foreignCurrencyReverseTaxAmount;
    }

    public function getVatNumber(): VatNumberViewObject
    {
        return $this->vatNumber;
    }

    public function getNetAmount(): string
    {
        return $this->netAmount;
    }

    public function getVatAmount(): string
    {
        return $this->vatAmount;
    }

    public function getGrossAmount(): string
    {
        return $this->grossAmount;
    }

    public function getForeignNetAmount(): string
    {
        return $this->foreignNetAmount;
    }

    public function getForeignVatAmount(): string
    {
        return $this->foreignVatAmount;
    }

    public function getForeignGrossAmount(): string
    {
        return $this->foreignGrossAmount;
    }

    public function getAccountingAccount(): AccountingAccountViewObject
    {
        return $this->accountingAccount;
    }

    protected function addDocumentElement(DocumentElement $documentElement)
    {
        $this->netAmount = Math::add($this->netAmount, $documentElement->getNetInDefaultInstanceCurrency());
        $this->vatAmount = Math::add($this->vatAmount, $documentElement->getVatInDefaultInstanceCurrency());
        $this->localCurrencyReverseTaxAmount = Math::add(
            $this->localCurrencyReverseTaxAmount,
            (string)$documentElement->reverse_tax,
        );
        $this->foreignCurrencyReverseTaxAmount = Math::add(
            $this->foreignCurrencyReverseTaxAmount,
            $documentElement->getReverseTaxInDocumentCurrency(),
        );
        $this->grossAmount = Math::add($this->grossAmount, $documentElement->getGrossInDefaultInstanceCurrency());
        $this->foreignNetAmount = Math::add($this->foreignNetAmount, $documentElement->getNetInDocumentCurrency());
        $this->foreignVatAmount = Math::add($this->foreignVatAmount, $documentElement->getVatInDocumentCurrency());
        $this->foreignGrossAmount = Math::add(
            $this->foreignGrossAmount,
            $documentElement->getGrossInDocumentCurrency(),
        );
    }
}
