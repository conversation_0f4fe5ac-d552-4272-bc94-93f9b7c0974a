<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Document;
use App\Installment;
use App\Request;
use App\RequestAccountingMileageAllowance;
use App\RequestAccountingTravelExpenses;
use App\TargetPoint;
use App\User;
use App\Vendors\Math;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;
use Modules\Common\ValueObjects\RequestType;
use Modules\TripPlanner\Priv\Entities\RequestTraveler;

class SettlementSummaryViewObject
{
    /**
     * @var string
     */
    protected $slug;

    protected string $name;
    private InstanceViewObject $instance;

    protected CompanyViewObject $company;

    /**
     * @var string
     */
    protected $number;

    protected string $status;

    protected RequestType $type;

    protected ?Carbon $settledAt;

    public function isNationalTrip(): bool
    {
        return $this->isNationalTrip;
    }

    protected UserViewObject $employee;

    protected ?Carbon $completionDate;

    /**
     * @var string|null
     */
    protected $erpId;

    /**
     * @var Carbon|null;
     */
    protected $erpVatAt;

    /**
     * @var Carbon|null;
     */
    protected $erpAccountedAt;

    /**
     * @var AllowanceCollection<AllowanceViewObject>
     */
    protected $allowances;

    /**
     * @var DocumentCollection<DocumentSummaryViewObject>
     */
    protected DocumentCollection $documents;

    protected Collection $travelers;

    protected ?UserViewObject $accountedBy;

    protected Collection $requestApprovers;

    protected Collection $claimApprovers;

    protected Collection $accountingDimensions;

    protected string $frontendUrl;

    protected bool $isDelegation;

    protected Collection $locations;

    protected string $purpose;

    protected bool $isNationalTrip;

    protected ?Carbon $tripStarts;

    protected ?Carbon $tripEnds;

    protected ?Carbon $unrealizedAt;

    protected Collection $cashAdvances;

    protected ?MpkViewObject $mpk;

    public function __construct(
        string $slug,
        string $name,
        InstanceViewObject $instance,
        CompanyViewObject $company,
        string $number,
        string $status,
        RequestType $type,
        ?Carbon $settledAt,
        UserViewObject $employee,
        ?Carbon $completionDate,
        ?string $erpId,
        ?Carbon $erpVatAt,
        ?Carbon $erpAccountedAt,
        Collection $allowances,
        DocumentCollection $documents,
        Collection $travelers,
        ?UserViewObject $accountedBy,
        Collection $requestApprovers,
        Collection $claimApprovers,
        Collection $accountingDimensions,
        Collection $cashAdvances,
        string $frontendUrl,
        bool $isDelegation,
        Collection $locations,
        string $purpose,
        ?Carbon $tripStarts,
        ?Carbon $tripEnds,
        ?Carbon $unrealizedAt,
        bool $isNationalTrip,
        ?MpkViewObject $mpk
    ) {
        $this->slug = $slug;
        $this->name = $name;
        $this->company = $company;
        $this->instance = $instance;
        $this->number = $number;
        $this->status = $status;
        $this->type = $type;
        $this->settledAt = $settledAt;
        $this->employee = $employee;
        $this->completionDate = $completionDate;
        $this->erpId = $erpId;
        $this->erpVatAt = $erpVatAt;
        $this->erpAccountedAt = $erpAccountedAt;
        $this->allowances = $allowances;
        $this->documents = $documents;
        $this->travelers = $travelers;
        $this->accountedBy = $accountedBy;
        $this->requestApprovers = $requestApprovers;
        $this->claimApprovers = $claimApprovers;
        $this->accountingDimensions = $accountingDimensions;
        $this->cashAdvances = $cashAdvances;
        $this->frontendUrl = $frontendUrl;
        $this->isDelegation = $isDelegation;
        $this->locations = $locations;
        $this->purpose = $purpose;
        $this->tripStarts = $tripStarts;
        $this->tripEnds = $tripEnds;
        $this->unrealizedAt = $unrealizedAt;
        $this->isNationalTrip = $isNationalTrip;
        $this->mpk = $mpk;
    }

    public static function fromRequest(Request $request): SettlementSummaryViewObject
    {
        $accountingDocuments = $request->accountingDocuments;
        $documents = $accountingDocuments->map(function (Document $document) {
            return DocumentSummaryViewObject::createFromDocument($document);
        });

        $allowanceCollection = new AllowanceCollection();

        if ($request->isDelegation() && $request->canAccountDelegation()) {
            $request->accountingTravelExpenses->filter(function (RequestAccountingTravelExpenses $expense) {
                return Math::isGreaterThanZero($expense->getAmount());
            })->groupBy(function (RequestAccountingTravelExpenses $expense) {
                return $expense->currency->code;
            })->each(
                function (Collection $groupedByCurrencyCode, string $currencyCode) use (
                    $allowanceCollection,
                    $request
                ) {
                    $allowanceLines = new AllowanceLineCollection(
                        $groupedByCurrencyCode->map(function (RequestAccountingTravelExpenses $expense) {
                            return AllowanceLineViewObject::createFromRequestAccountingTravelExpenses($expense);
                        })
                    );

                    $currencyViewObject = CurrencyViewObject::createFromRequestAccountingTravelExpenses(
                        $groupedByCurrencyCode->first()
                    );

                    $allowanceCollection->put(
                        $currencyCode,
                        AllowanceViewObject::createFromRequest($request, $currencyViewObject, $allowanceLines)
                    );
                }
            );
        }

        $request->accountingMileageAllowances->filter(
            function (RequestAccountingMileageAllowance $accountingMileageAllowance) {
                return Math::isGreaterThanZero($accountingMileageAllowance->getAmount());
            }
        )->groupBy(function (RequestAccountingMileageAllowance $accountingMileageAllowance) {
            return $accountingMileageAllowance->currency->code;
        })->each(
            function (Collection $groupedByCurrencyCode, string $currencyCode) use (
                $allowanceCollection,
                $request
            ) {
                $allowanceLines = new AllowanceLineCollection(
                    $groupedByCurrencyCode->map(
                        function (RequestAccountingMileageAllowance $accountingMileageAllowance) {
                            return AllowanceLineViewObject::createFromRequestAccountingMileageAllowance(
                                $accountingMileageAllowance
                            );
                        }
                    )
                );

                $currencyViewObject = CurrencyViewObject::createFromRequestAccountingMileageAllowance(
                    $groupedByCurrencyCode->first()
                );
                if ($allowanceCollection->has($currencyCode)) {
                    $allowanceLines = $allowanceCollection->get($currencyCode)->getAllowanceLines()->merge(
                        $allowanceLines
                    );
                    $currencyViewObject = $allowanceCollection->get($currencyCode)->getCurrency();
                }

                $allowanceCollection->put(
                    $currencyCode,
                    AllowanceViewObject::createFromRequest($request, $currencyViewObject, $allowanceLines)
                );
            }
        );

        $travelers = $request->travelers->map(function (RequestTraveler $requestTraveler) {
            return UserViewObject::createFromUser($requestTraveler->requestTraveler)
                ->setSupervisorFromUser($requestTraveler->requestTraveler)
                ->setAssistantFromUser($requestTraveler->requestTraveler);
        });

        $requestApprovers = $request->acceptors->map(function (User $user) {
            return UserViewObject::createFromUser($user)
                ->setSupervisorFromUser($user)
                ->setAssistantFromUser($user);
        });

        $claimApprovers = $request->settlementAcceptors->map(function (User $user) {
            return UserViewObject::createFromUser($user)
                ->setSupervisorFromUser($user)
                ->setAssistantFromUser($user);
        });

        $accountingDimensions = $request->accountDimensionItems->map(function (RequestAccountDimensionItem $item) {
            return SelectedAccountDimensionViewObject::createFromAccountDimensionItem($item->accountDimensionItem);
        });

        $locations = $request->targetPoints->map(function (TargetPoint $targetPoint) {
            return LocationViewObject::createFromLocation($targetPoint->location);
        });

        $cashAdvances = $request->installments->map(function (Installment $installment) {
            return CashAdvanceViewObject::fromInstallment($installment);
        });

        return new static(
            $request->slug,
            $request->name ?? '',
            InstanceViewObject::createFromInstance($request->instance),
            CompanyViewObject::createFromCompany($request->company),
            $request->number,
            $request->status,
            new RequestType($request->type),
            $request->settled_at,
            UserViewObject::createFromUser($request->user)
                ->setSupervisorFromUser($request->user)
                ->setAssistantFromUser($request->user),
            $request->completion_date,
            $request->erp_id,
            $request->erp_vat_at,
            $request->erp_accounted_at,
            $allowanceCollection->values(),
            DocumentCollection::make($documents->all()),
            $travelers,
            $request->accountingUser ? UserViewObject::createFromUser($request->accountingUser)
                ->setSupervisorFromUser($request->accountingUser)
                ->setAssistantFromUser($request->accountingUser) : null,
            $requestApprovers,
            $claimApprovers,
            $accountingDimensions,
            $cashAdvances,
            $request->getFrontendUrl(),
            $request->isDelegation(),
            $locations,
            $request->purpose ?? '',
            $request->trip_starts,
            $request->trip_ends,
            $request->unrealized_at,
            $request->isNationalTrip(),
            $request->mpk ? MpkViewObject::createFromMpk($request->mpk) : null
        );
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCompletionDate(): ?Carbon
    {
        return $this->completionDate;
    }

    public function getAllowances(): AllowanceCollection
    {
        return $this->allowances;
    }

    public function getDocuments(): DocumentCollection
    {
        return $this->documents;
    }

    public function getErpId(): ?string
    {
        return $this->erpId;
    }

    public function getErpVatAt(): ?Carbon
    {
        return $this->erpVatAt;
    }

    public function getErpAccountedAt(): ?Carbon
    {
        return $this->erpAccountedAt;
    }

    public function getInstance(): InstanceViewObject
    {
        return $this->instance;
    }

    public function getCompany(): CompanyViewObject
    {
        return $this->company;
    }

    public function getSettledAt(): ?Carbon
    {
        return $this->settledAt;
    }

    public function getTravelers(): Collection
    {
        return $this->travelers;
    }

    public function getAccountedBy(): ?UserViewObject
    {
        return $this->accountedBy;
    }

    public function getEmployee(): UserViewObject
    {
        return $this->employee;
    }

    public function getRequestApprovers(): Collection
    {
        return $this->requestApprovers;
    }

    public function getClaimApprovers(): Collection
    {
        return $this->claimApprovers;
    }

    public function getAccountingDimensions(): Collection
    {
        return $this->accountingDimensions;
    }

    public function getFrontendUrl(): string
    {
        return $this->frontendUrl;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getType(): RequestType
    {
        return $this->type;
    }

    public function isDelegation(): bool
    {
        return $this->isDelegation;
    }

    public function getLocations(): Collection
    {
        return $this->locations;
    }

    public function getPurpose(): string
    {
        return $this->purpose;
    }

    public function getTripStarts(): ?Carbon
    {
        return $this->tripStarts;
    }

    public function getTripEnds(): ?Carbon
    {
        return $this->tripEnds;
    }

    public function getTotalAmountInLocalCurrency(): string
    {
        return Math::add(
            $this->documents->getTotalAmountInInstanceCurrency(),
            $this->allowances->getTotalAmountInInstanceCurrency()
        );
    }

    public function getUnrealizedAt(): ?Carbon
    {
        return $this->unrealizedAt;
    }

    public function getCashAdvances(): Collection
    {
        return $this->cashAdvances ?? new Collection();
    }

    public function getMpk(): ?MpkViewObject
    {
        return $this->mpk;
    }
}
