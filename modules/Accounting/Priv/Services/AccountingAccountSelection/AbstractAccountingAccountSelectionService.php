<?php

namespace Modules\Accounting\Priv\Services\AccountingAccountSelection;

use App\Document;
use App\Instance;
use App\Request;
use App\Services\DefaultAcceptor\Exceptions\DecisionNotFoundException;
use Modules\Accounting\Priv\Entities\AccountingAccount;
use Modules\Accounting\Priv\Enum\AccountingAccountKindEnum;
use Modules\Accounting\Priv\Enum\AccountingAccountTypeEnum;
use Modules\Accounting\Priv\Exceptions\AccountingAccountNotFound;
use Modules\Accounting\Priv\Exceptions\AccountingAccountNotProvided;
use Modules\DecisionMaker\Priv\Exceptions\DecisionProcessNodesNotFoundException;
use Modules\DecisionMaker\Priv\Exceptions\DecisionProcessNotFoundException;
use Modules\DecisionMaker\Pub\DataSets\BasicDataSet;
use Modules\DecisionMaker\Pub\DataSets\DataSetInterface;
use Modules\DecisionMaker\Pub\Enums\DecisionProcessEnum;
use Modules\DecisionMaker\Pub\Facades\DecisionMakerFacade;

abstract class AbstractAccountingAccountSelectionService
{
    protected AccountingAccountTypeEnum $accountingAccountType;
    private DecisionMakerFacade $decisionMakerFacade;

    public function __construct(DecisionMakerFacade $decisionMakerFacade)
    {
        $this->decisionMakerFacade = $decisionMakerFacade;
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getAccommodationLumSumAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::ACCOMMODATION_LUM_SUM_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::ACCOMMODATION_LUMP_SUM(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getDriveLumpSumAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::DRIVE_LUMP_SUM_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::DRIVE_LUMP_SUM(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getMileageAllowanceAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::MILEAGE_ALLOWANCE_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::MILEAGE_ALLOWANCE(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getProviderAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::PROVIDER_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::PROVIDER(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Document $document
     * @param bool $isForeign
     * @return AccountingAccount
     */
    public function getProviderAccountingAccountForDocument(
        Instance $instance,
        Document $document,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForDocument(
            $instance,
            $document,
            DecisionProcessEnum::PROVIDER_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::PROVIDER(),
            $isForeign
        );
    }


    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getTechnicalAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::TECHNICAL_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::TECHNICAL(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getTravelExpensesAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::TRAVEL_EXPENSE_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::TRAVEL_EXPENSES(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param bool $isForeign
     * @return AccountingAccount
     * @throws AccountingAccountNotFound
     */
    public function getAccessLumpSumAccountingAccount(
        Instance $instance,
        Request $request,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccountForRequest(
            $instance,
            $request,
            DecisionProcessEnum::ACCESS_LUMP_SUM_ACCOUNTING_ACCOUNTS_STRATEGY_SELECTION(),
            AccountingAccountKindEnum::ACCESS_LUMP_SUM(),
            $isForeign
        );
    }

    /**
     * @param Instance $instance
     * @param Request $request
     * @param AccountingAccountKindEnum $accountingAccountKind
     * @param bool $isForeign
     * @return AccountingAccount|string
     * @throws AccountingAccountNotFound
     */
    public function getAccountingAccountByKind(
        Instance $instance,
        Request $request,
        AccountingAccountKindEnum $accountingAccountKind,
        bool $isForeign = false
    ): AccountingAccount {
        switch ($accountingAccountKind) {
            case(AccountingAccountKindEnum::PROVIDER()):
                return $this->getProviderAccountingAccount($instance, $request, $isForeign);
            case(AccountingAccountKindEnum::TECHNICAL()):
                return $this->getTechnicalAccountingAccount($instance, $request, $isForeign);
            case(AccountingAccountKindEnum::TRAVEL_EXPENSES()):
                return $this->getTravelExpensesAccountingAccount($instance, $request, $isForeign);
            case(AccountingAccountKindEnum::MILEAGE_ALLOWANCE()):
                return $this->getMileageAllowanceAccountingAccount($instance, $request, $isForeign);
            case(AccountingAccountKindEnum::ACCOMMODATION_LUMP_SUM()):
                return $this->getAccommodationLumSumAccountingAccount($instance, $request, $isForeign);
            case(AccountingAccountKindEnum::DRIVE_LUMP_SUM()):
                return $this->getDriveLumpSumAccountingAccount($instance, $request, $isForeign);
            case(AccountingAccountKindEnum::ACCESS_LUMP_SUM()):
                return $this->getAccessLumpSumAccountingAccount($instance, $request, $isForeign);
            default:
                throw new AccountingAccountNotFound($isForeign, $this->accountingAccountType, $accountingAccountKind);
        }
    }

    private function getAccountingAccountForRequest(
        Instance $instance,
        Request $request,
        DecisionProcessEnum $decisionProcess,
        AccountingAccountKindEnum $accountingAccountKind,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccount(
            $instance,
            [DataSetInterface::REQUEST => $request],
            $decisionProcess,
            $accountingAccountKind,
            $isForeign
        );
    }

    private function getAccountingAccountForDocument(
        Instance $instance,
        Document $document,
        DecisionProcessEnum $decisionProcess,
        AccountingAccountKindEnum $accountingAccountKind,
        bool $isForeign = false
    ): AccountingAccount {
        return $this->getAccountingAccount(
            $instance,
            [DataSetInterface::DOCUMENT => $document],
            $decisionProcess,
            $accountingAccountKind,
            $isForeign
        );
    }

    private function getAccountingAccount(
        Instance $instance,
        array $dataSet,
        DecisionProcessEnum $decisionProcess,
        AccountingAccountKindEnum $accountingAccountKind,
        bool $isForeign = false
    ): AccountingAccount {
        try {
            $decisionMaker = $this->decisionMakerFacade->buildForSlug(
                $decisionProcess,
                $instance->id
            );

            $decision = $decisionMaker->decide(new BasicDataSet($dataSet));

            $result = $decision->execute($dataSet);

            $accountingAccount = $this->accountingAccountType->equals(
                AccountingAccountTypeEnum::DEBIT()
            ) ? $result->getDebitAccountingAccount() : $result->getCreditAccountingAccount();

            if ($accountingAccount instanceof AccountingAccount === false) {
                throw new AccountingAccountNotProvided();
            }

            return $accountingAccount;
        } catch (DecisionNotFoundException|DecisionProcessNotFoundException|DecisionProcessNodesNotFoundException|AccountingAccountNotProvided $e) {
            $accountingAccount = $instance->instanceAccountingAccount(
                $accountingAccountKind,
                $this->accountingAccountType,
                $isForeign
            );

            if ($accountingAccount instanceof AccountingAccount === false) {
                throw new AccountingAccountNotFound($isForeign, $this->accountingAccountType, $accountingAccountKind);
            }

            return $accountingAccount;
        }
    }
}