<?php

declare(strict_types=1);

namespace Modules\Accounting\Priv\Services;

use App\Instance;
use App\Repositories\Pagination\PaginatorInterface;
use App\Services\SlugGeneratorService;
use Modules\Accounting\Priv\Entities\Mpk;
use Modules\Accounting\Priv\Repositories\MpkRepository;
use Modules\Accounting\Pub\Interfaces\CreateMpkDtoInterface;
use Modules\Accounting\Pub\Interfaces\UpdateMpkDtoInterface;
use Modules\Common\Dtos\PageInterface;

class MpkService
{
    /**
     * @var MpkRepository
     */
    protected $mpkRepository;

    /**
     * @var SlugGeneratorService
     */
    protected $slugGeneratorService;

    /**
     * MpkService constructor.
     * @param MpkRepository $mpkRepository
     * @param SlugGeneratorService $slugGeneratorService
     */
    public function __construct(MpkRepository $mpkRepository, SlugGeneratorService $slugGeneratorService)
    {
        $this->mpkRepository = $mpkRepository;
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public function create(CreateMpkDtoInterface $newMpkDto): Mpk
    {
        $mpk = new Mpk();
        $company = $newMpkDto->getCompany();
        if($newMpkDto->getCompany() !== null) {
            $mpk->company_id = $company->id;
        }

        $mpk->instance_id = $newMpkDto->getInstance()->id;
        $mpk->name = $newMpkDto->getName();
        $mpk->code = $newMpkDto->getCode();
        $mpk->is_active = $newMpkDto->getIsActive();
        $mpk->slug = $newMpkDto->getSlug();

        $mpk = $this->mpkRepository->persist($mpk);
        $mpk = $this->mpkRepository->getFreshModel($mpk);

        return $mpk;
    }

    public function edit(UpdateMpkDtoInterface $editMpkDto): Mpk
    {
        $mpk = $editMpkDto->getMpkToChange();
        $mpk->code = $editMpkDto->getCode();
        $mpk->name = $editMpkDto->getName();
        $mpk->is_active = $editMpkDto->getIsActive();
        if ($editMpkDto->getCompany() !== null) {
            $mpk->company_id = $editMpkDto->getCompany()->id;
        } else {
            $mpk->company_id = null;
        }

        $mpk = $this->mpkRepository->persist($mpk);
        $mpk = $this->mpkRepository->getFreshModel($mpk);

        return $mpk;
    }

    public function exists(string $code, Instance $instance): bool
    {
        return null !== $this->mpkRepository->findByCodeAndInstanceId($code, $instance->id);
    }

    public function list(PageInterface $page, Instance $instance): PaginatorInterface
    {
        return $this->mpkRepository->getForCurrentInstance($page, $instance);
    }
}
