<?php

declare(strict_types=1);

namespace Modules\TripPlanner\Priv\Repositories;

use App\Repositories\Repository;
use App\Request;
use App\Services\SlugGeneratorService;
use Illuminate\Database\Eloquent\Model;
use Modules\TripPlanner\Priv\Entities\RequestTraveler;

class RequestTravelerRepository extends Repository
{
    /**
     * @var SlugGeneratorService
     */
    protected $slugGenerator;

    /**
     * RequestTravelerRepository constructor.
     * @param SlugGeneratorService $slugGenerator
     */
    public function __construct(SlugGeneratorService $slugGenerator)
    {
        parent::__construct();
        $this->slugGenerator = $slugGenerator;
    }

    public static function model()
    {
        return RequestTraveler::class;
    }

    public function findForRequestSlug(Request $request): array
    {
        return $this->builder->where('request_id', $request->id)->get()->all();
    }

    protected function beforeCreate(RequestTraveler $model, $data): RequestTraveler
    {
        $model->slug = $this->slugGenerator->generate();

        return $model;
    }

    public function findBySlugAndRequestId(string $slug, int $requestId): ?RequestTraveler
    {
        $this->prepare();

        return $this->builder->where([
            'slug' => $slug,
            'request_id' => $requestId

        ])->first();
    }

    public function remove(Model $model): Model
    {
        $model->delete();

        return $model;
    }

    public function findByUserIdAndRequestId(int $userId, int $requestId): ?RequestTraveler
    {
        $this->prepare();

        return $this->builder->where([
            'request_traveler_id' => $userId,
            'request_id' => $requestId

        ])->first();
    }

}