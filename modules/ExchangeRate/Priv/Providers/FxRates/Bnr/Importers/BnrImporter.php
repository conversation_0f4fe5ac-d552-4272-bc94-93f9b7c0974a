<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Priv\Providers\FxRates\Bnr\Importers;

use Carbon\Carbon;
use Illuminate\Support\Collection;
use Modules\Common\Enums\SupportedCurrencyCodesEnum;
use Modules\ExchangeRate\Priv\Providers\FxRates\Bnr\Dtos\BnrRate;
use Modules\ExchangeRate\Priv\Providers\FxRates\Bnr\Dtos\BnrTable;
use Modules\ExchangeRate\Priv\Repositories\ExchangeRateBnrRepository;

class BnrImporter
{
    private const REQUEST_URL = 'https://www.bnr.ro/files/xml/curs_%s.xml';
    private const REQUEST_DATE_FORMAT = 'Y_n_j'; //required format e.g. 2022_9_1

    private const BASE_MULTIPLIER = "1";

    private ExchangeRateBnrRepository $exchangeRateBnrRepository;

    public function __construct(ExchangeRateBnrRepository $exchangeRateBnrRepository)
    {
        $this->exchangeRateBnrRepository = $exchangeRateBnrRepository;
    }

    public function run(Carbon $from, Carbon $to): void
    {
        for ($date = $from; $date->lte($to); $date->addDay()) {
            $xmlElement = $this->getExchangesRatesXML($date);

            if (!$xmlElement) {
                continue;
            }

            $this->saveExchangeRates($xmlElement, $date);
        }
    }

    protected function getExchangesRatesXML(Carbon $date): \SimpleXMLElement
    {
        return new \SimpleXMLElement(
            sprintf(self::REQUEST_URL, $date->format(self::REQUEST_DATE_FORMAT)),
            0,
            true
        );
    }

    protected function saveExchangeRates(\SimpleXMLElement $xmlElement, Carbon $date): void
    {
        $rates = new Collection();

        $rates->push(
            new BnrRate(
                SupportedCurrencyCodesEnum::RON()->getValue(),
                SupportedCurrencyCodesEnum::RON()->getValue(),
                '1',
                '1',
            )
        );

        foreach ($xmlElement->Body->Cube->Rate as $rate) {
            $rates->push(
                new BnrRate(
                    $rate->attributes()->currency->__toString(),
                    $rate->attributes()->currency->__toString(),
                    $rate->__toString(),
                    (string)$rate->attributes()->multiplier ?: self::BASE_MULTIPLIER,
                )
            );
        }

        $this->exchangeRateBnrRepository->saveTable(
            new BnrTable(
                Carbon::parse($date->format('Y-m-d')),
                $rates
            )
        );
    }
}