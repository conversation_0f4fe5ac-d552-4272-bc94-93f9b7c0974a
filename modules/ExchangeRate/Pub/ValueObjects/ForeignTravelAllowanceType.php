<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Pub\ValueObjects;

use App\RequestAccountingTravelExpenses;
use App\Services\AccessLumpSum\AccessLumpSumItem\ForeignAccessLumpSumItem;
use App\Services\AccommodationLumpSum\AccommodationLumpSumItem\ForeignAccommodationLumpSumItem;
use App\Services\AccommodationLumpSum\AccommodationLumpSumItem\NationalAccommodationLumpSumItem;
use App\Services\DriveLumpSum\DriveLumpSumItem\ForeignDriveLumpSumItem;
use App\Services\DriveLumpSum\DriveLumpSumItem\NationalDriveLumpSumItem;
use App\Services\TravelExpenses\TravelExpensesItem\ForeignTravelExpenseItem;
use App\Services\TravelExpenses\TravelExpensesItem\NationalTravelExpenseItem;
use Modules\Common\ValueObjects\BaseValueObject;
use Modules\ExchangeRate\Pub\Exceptions\InvalidForeignTravelAllowanceTypeException;

class ForeignTravelAllowanceType extends BaseValueObject
{
    public const AVAILABLE_VALUES = [
        ForeignAccessLumpSumItem::class,
        ForeignTravelExpenseItem::class,
        NationalTravelExpenseItem::class,
        ForeignDriveLumpSumItem::class,
        NationalDriveLumpSumItem::class,
        ForeignAccommodationLumpSumItem::class,
        NationalAccommodationLumpSumItem::class,
        RequestAccountingTravelExpenses::class,
    ];

    public function __construct(string $value)
    {
        if (static::isValid($value) === false) {
            throw InvalidForeignTravelAllowanceTypeException::create();
        }

        $this->value = $value;
    }

    protected function isValid(string $value): bool
    {
        return in_array($value, static::AVAILABLE_VALUES);
    }
}
