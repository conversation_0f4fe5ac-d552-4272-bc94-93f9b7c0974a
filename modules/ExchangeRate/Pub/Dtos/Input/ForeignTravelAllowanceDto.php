<?php

declare(strict_types=1);

namespace Modules\ExchangeRate\Pub\Dtos\Input;

use Modules\Common\ValueObjects\CurrencyCode;
use Modules\ExchangeRate\Pub\Dtos\Internal\RequestDto;
use Modules\ExchangeRate\Pub\Dtos\Internal\UserDto;
use Modules\ExchangeRate\Pub\Dtos\UniqueDtoInterface;
use Modules\ExchangeRate\Pub\ValueObjects\ForeignTravelAllowanceType;

class ForeignTravelAllowanceDto implements UniqueDtoInterface
{
    /** @var ForeignTravelAllowanceType */
    protected $type;

    /** @var CurrencyCode */
    protected $currencyCode;

    /** @var RequestDto */
    protected $requestDto;

    /** @var UserDto */
    protected $userDto;

    public function __construct(string $type, string $currencyCode, RequestDto $requestDto , UserDto $userDto)
    {
        $this->type = new ForeignTravelAllowanceType($type);
        $this->currencyCode = new CurrencyCode($currencyCode);
        $this->requestDto = $requestDto;
        $this->userDto = $userDto;
    }

    public function getCurrencyCode(): CurrencyCode
    {
        return $this->currencyCode;
    }

    public function getRequestDto(): RequestDto
    {
        return $this->requestDto;
    }

    public function getUserDto(): UserDto
    {
        return $this->userDto;
    }

    public function getType(): ForeignTravelAllowanceType
    {
        return $this->type;
    }

    public function getMd5(): string
    {
        return md5(serialize($this));
    }
}
