<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Dtos;

use Modules\Common\ValueObjects\Slug;
use Modules\IntegrationFILE\Pub\Enums\ImportStatusEnum;
use Modules\IntegrationFILE\Pub\Enums\ImportTypeEnum;

class InitializeExportDto
{
    protected ImportTypeEnum $type;
    protected ImportStatusEnum $status;
    protected Slug $userSlug;
    protected ?Slug $companySlug;
    protected Slug $instanceSlug;
    private ?string $subtype;

    public function __construct(
        string $type,
        ?string $subtype,
        string $status,
        string $userSlug,
        ?string $companySlug,
        string $instanceSlug
    ) {
        $this->type = new ImportTypeEnum($type);
        $this->status = new ImportStatusEnum($status);
        $this->userSlug = new Slug($userSlug);
        $this->companySlug = empty($companySlug) === false ? new Slug($companySlug) : null;
        $this->instanceSlug = new Slug($instanceSlug);
        $this->subtype = $subtype;
    }

    public function getType(): ImportTypeEnum
    {
        return $this->type;
    }

    public function getStatus(): ImportStatusEnum
    {
        return $this->status;
    }

    public function getUserSlug(): Slug
    {
        return $this->userSlug;
    }

    public function getCompanySlug(): ?Slug
    {
        return $this->companySlug;
    }

    public function getInstanceSlug(): Slug
    {
        return $this->instanceSlug;
    }

    public function getSubtype(): ?string
    {
        return $this->subtype;
    }
}