<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Dtos;

use Carbon\Carbon;
use Illuminate\Contracts\Support\Arrayable;
use Modules\Common\ValueObjects\Slug;
use Modules\IntegrationFILE\Pub\Enums\ImportStatusEnum;
use Modules\IntegrationFILE\Pub\Enums\ImportTypeEnum;

class ImportUpdateDto implements Arrayable
{
    protected Slug $slug;
    protected ?Carbon $startDate;
    protected ?Carbon $endDate;
    protected ImportTypeEnum $type;
    protected ImportStatusEnum $status;
    protected string $fileName;
    protected Carbon $createdAt;
    protected ?string $companyName;
    protected string $userFullName;
    protected bool $isExport;

    public function __construct(
        Slug $slug,
        ?Carbon $startDate,
        ?Carbon $endDate,
        ImportTypeEnum $type,
        ImportStatusEnum $status,
        string $fileName,
        Carbon $createdAt,
        ?string $companyName,
        string $userFullName,
        bool $isExport
    ) {
        $this->slug = $slug;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->type = $type;
        $this->status = $status;
        $this->fileName = $fileName;
        $this->createdAt = $createdAt;
        $this->companyName = $companyName;
        $this->userFullName = $userFullName;
        $this->isExport = $isExport;
    }

    public function isExport(): bool
    {
        return $this->isExport;
    }

    public function getSlug(): Slug
    {
        return $this->slug;
    }

    public function getStartDate(): ?Carbon
    {
        return $this->startDate;
    }

    public function getEndDate(): ?Carbon
    {
        return $this->endDate;
    }

    public function getType(): ImportTypeEnum
    {
        return $this->type;
    }

    public function getStatus(): ImportStatusEnum
    {
        return $this->status;
    }

    public function getFileName(): string
    {
        return $this->fileName;
    }
    public function getCreatedAt(): Carbon
    {
        return $this->createdAt;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function getUserFullName(): string
    {
        return $this->userFullName;
    }

    public function toArray(): array
    {
        $response = [
            'slug'                  => (string)$this->getSlug(),
            'startDate'             => $this->getStartDate() instanceof Carbon ? $this->getStartDate()->toDateTimeString() : null,
            'endDate'               => $this->getEndDate() instanceof Carbon ? $this->getEndDate()->toDateTimeString() : null,
            'type'                  => (string)$this->getType(),
            'status'                => (string)$this->getStatus(),
            'fileName'              => $this->getFileName(),
            'createdAt'             => $this->getCreatedAt()->toDateTimeString(),
            'isExport'              => $this->isExport(),
            'user'                  => [
                'fullName'              => $this->getUserFullName()
            ]
        ];

        if (empty($this->getCompanyName()) === false) {
            $response['company'] = [
                'name'                  => $this->getCompanyName(),
            ];
        }

        return $response;
    }
}
