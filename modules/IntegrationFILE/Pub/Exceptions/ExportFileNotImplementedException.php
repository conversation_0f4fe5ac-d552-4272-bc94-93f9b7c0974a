<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Exceptions;

use Illuminate\Http\Response;
use Modules\IntegrationFILE\Pub\Enums\ImportTypeEnum;

class ExportFileNotImplementedException extends \Exception
{
    public static function create(ImportTypeEnum $importTypeEnum): ExportFileNotImplementedException
    {
        return new static(sprintf('Export %s is not implemented!', (string)$importTypeEnum), Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}