<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Exceptions;

use Illuminate\Http\Response;
use Modules\IntegrationFILE\Pub\Enums\ExportFormatEnum;

class ExportTypeNotImplementedException extends \Exception
{
    public static function create(ExportFormatEnum $exportFormatEnum): ExportTypeNotImplementedException
    {
        return new static(sprintf('Export format %s is not implemented!', (string)$exportFormatEnum), Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}