<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Enums;

use App\Enum\AbstractEnum;

/**
 * Class ImportStatusEnum
 * @package Modules\IntegrationFILE\Pub\Enums
 * @method static ImportStatusEnum PENDING
 * @method static ImportStatusEnum PROCESSING
 * @method static ImportStatusEnum PROCESSED
 * @method static ImportStatusEnum SUCCESS
 * @method static ImportStatusEnum ERROR
 */
class ImportStatusEnum extends AbstractEnum
{
    private const PENDING = 'pending';
    private const PROCESSING = 'processing';
    private const PROCESSED = 'processed';
    private const SUCCESS = 'success';
    private const ERROR = 'error';
}