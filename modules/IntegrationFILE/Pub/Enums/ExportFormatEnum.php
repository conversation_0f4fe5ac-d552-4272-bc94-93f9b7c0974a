<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Pub\Enums;

use My<PERSON>abs\Enum\Enum;

/**
 * Class ExportFormatEnum
 * @package Modules\IntegrationFILE\Pub\Enums
 * @method static ExportFormatEnum STALGAST_XML
 * @method static ExportFormatEnum GWR_TXT
 * @method static ExportFormatEnum TRIUMPH_CSV
 * @method static ExportFormatEnum WEG_CSV
 * @method static ExportFormatEnum PIR_XLSX
 * @method static ExportFormatEnum MXZ_XLSX
 * @method static ExportFormatEnum NVT_XLSX
 * @method static ExportFormatEnum STC_XLSX
 * @method static ExportFormatEnum ASE381_XLSX
 * @method static ExportFormatEnum TMH_XLSX
 * @method static ExportFormatEnum FRE036_XLSX
 * @method static ExportFormatEnum JWA_TXT
 * @method static ExportFormatEnum BLT_XLSX
 * @method static ExportFormatEnum ALT_XLSX
 * @method static ExportFormatEnum EGO_TXT
 * @method static ExportFormatEnum FLX_XLSX
 * @method static ExportFormatEnum KMT_XML
 * @method static ExportFormatEnum CLG_XML
 * @method static ExportFormatEnum MND_XLSX_ENOVA
 */
class ExportFormatEnum extends Enum
{
    private const STALGAST_XML = 'STALGAST_XML';
    private const GWR_TXT = 'GWR_TXT';
    private const TRIUMPH_CSV = 'TRIUMPH_CSV';
    private const WEG_CSV = 'WEG_CSV';
    private const PIR_XLSX = 'PIR_XLSX';
    private const MXZ_XLSX = 'MXZ_XLSX';
    private const NVT_XLSX = 'NVT_XLSX';
    private const STC_XLSX = 'STC_XLSX';
    private const ASE381_XLSX = 'ASE381_XLSX';
    private const TMH_XLSX = 'TMH_XLSX';
    private const FRE036_XLSX = 'FRE036_XLSX';
    private const JWA_TXT = 'JWA_TXT';
    private const BLT_XLSX = 'BLT_XLSX';
    private const ALT_XLSX = 'ALT_XLSX';
    private const EGO_TXT = 'EGO_TXT';
    private const FLX_XLSX = 'FLX_XLSX';
    private const KMT_XML = 'KMT_XML';
    private const CLG_XML = 'CLG_XML';
    private const MND_XLSX_ENOVA = 'MND_XLSX_ENOVA';
}
