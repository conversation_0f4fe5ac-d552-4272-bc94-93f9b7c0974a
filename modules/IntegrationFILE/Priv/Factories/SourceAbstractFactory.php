<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Factories;

use Cache\Bridge\SimpleCache\SimpleCacheBridge;
use Illuminate\Redis\RedisManager;
use Iterator;
use Modules\Common\Iterators\AssocCsvIterator;
use Modules\Common\Iterators\AssocSpreadsheetIterator;
use Cache\Adapter\Predis\PredisCachePool;
use Modules\IntegrationFILE\Priv\Services\Transformers\TransformerInterface;

class SourceAbstractFactory
{
    public const SPREADSHEET_ITERATOR = 'SPREADSHEET_ITERATOR';
    public const CSV_ITERATOR = 'CSV_ITERATOR';

    protected RedisManager $redisManager;

    public function __construct(RedisManager $redisManager)
    {
        $this->redisManager = $redisManager;
    }

    public function createByFileType(
        string $filePath,
        string $iteratorType = self::SPREADSHEET_ITERATOR,
        ?TransformerInterface $transformer = null,
        array $config = []
    ): Iterator {
        if ($iteratorType === self::CSV_ITERATOR) {
            return new AssocCsvIterator($filePath, $config, $transformer);
        }

        $cache = $this->getCacheObject();
        return new AssocSpreadsheetIterator($cache, $filePath, $transformer);
    }

    private function getCacheObject(): SimpleCacheBridge
    {
        $client = $this->redisManager->connection()->client();
        $pRedisCachePool = new PredisCachePool($client);

        return new SimpleCacheBridge($pRedisCachePool);
    }
}
