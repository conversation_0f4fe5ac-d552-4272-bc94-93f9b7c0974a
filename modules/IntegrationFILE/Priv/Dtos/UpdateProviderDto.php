<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Dtos;

use App\Provider;
use Modules\Accounting\Pub\Interfaces\CreateProviderRequestInterface;
use Modules\Accounting\Pub\Interfaces\UpdateProviderRequestInterface;

class UpdateProviderDto extends CreateProviderDto implements UpdateProviderRequestInterface
{
    protected Provider $provider;

    public function __construct(
        Provider $provider,
        string $slug,
        ?string $name,
        ?string $taxId,
        ?string $address,
        ?string $postCode,
        ?string $city,
        ?string $countryId,
        ?string $erpId,
        ?string $companyId
    ) {
        parent::__construct($provider->instance, $slug,  $name, $taxId, $address, $postCode, $city, $countryId, $erpId, $companyId);
        $this->provider = $provider;
    }

    public function getSlug(): string
    {
        return $this->provider->slug;
    }

    public function getProviderToChange(): Provider
    {
        return $this->provider;
    }

    public static function createFromProviderAndArray(Provider $provider, array $row): UpdateProviderDto
    {
        return new self(
            $provider,
            !empty($row[CreateProviderRequestInterface::KEY_NEW_SLUG]) ? trim($row[CreateProviderRequestInterface::KEY_NEW_SLUG]) : Provider::generateSlug(),
            !empty($row[CreateProviderRequestInterface::KEY_NAME]) ? trim($row[CreateProviderRequestInterface::KEY_NAME]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_TAX_ID]) ? trim($row[CreateProviderRequestInterface::KEY_TAX_ID]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_ADDRESS]) ? trim($row[CreateProviderRequestInterface::KEY_ADDRESS]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_POST_CODE]) ? trim($row[CreateProviderRequestInterface::KEY_POST_CODE]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_CITY]) ? trim($row[CreateProviderRequestInterface::KEY_CITY]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_COUNTRY]) ? trim($row[CreateProviderRequestInterface::KEY_COUNTRY]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_ERP_ID]) ? trim($row[CreateProviderRequestInterface::KEY_ERP_ID]) : null,
            !empty($row[CreateProviderRequestInterface::KEY_COMPANY_ID]) ? trim($row[CreateProviderRequestInterface::KEY_COMPANY_ID]) : null
        );
    }
}
