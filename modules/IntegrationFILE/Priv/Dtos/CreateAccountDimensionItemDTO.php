<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Dtos;

use App\Company;
use App\Instance;
use App\User;
use Modules\Analytics\Pub\Interfaces\StoreAccountDimensionItemInterface;

class CreateAccountDimensionItemDTO implements StoreAccountDimensionItemInterface
{
    private int $instanceId;
    private int $accountDimensionId;
    private string $code;
    private string $name;
    private int $isActive;
    private int $order;
    private ?int $companyId;
    private User $createdBy;
    private ?string $requestType;

    private function __construct(
        int $instanceId,
        int $accountDimensionId,
        string $code,
        string $name,
        User $createdBy,
        int $isActive = 0,
        int $order = 0,
        ?int $companyId = null,
        ?string $requestType = null
    ) {
        $this->instanceId = $instanceId;
        $this->accountDimensionId = $accountDimensionId;
        $this->code = $code;
        $this->name = $name;
        $this->isActive = $isActive;
        $this->order = $order;
        $this->companyId = $companyId;
        $this->createdBy = $createdBy;
        $this->requestType = $requestType;
    }

    public static function fromArrayAndInstanceAndUser(
        array $row,
        int $accountDimensionId,
        Instance $instance,
        User $user,
        ?Company $company = null
    ): CreateAccountDimensionItemDTO {
        return new self(
            $instance->id,
            $accountDimensionId,
            $row[self::KEY_CODE],
            $row[self::KEY_NAME],
            $user,
            (int)$row[self::KEY_IS_ACTIVE],
            (int)$row[self::KEY_ORDER],
            $company->id ?? null,
            $row[self::KEY_REQUEST_TYPE] ?? null,
        );
    }


    public function getAccountDimensionId(): int
    {
        return $this->accountDimensionId;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function getIsActive(): int
    {
        return $this->isActive;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function getRequestType(): ?string
    {
        return $this->requestType;
    }

    public function getAccountDimensionIdKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_ACCOUNT_DIMENSION_ID;
    }

    public function getCodeKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_CODE;
    }

    public function getNameKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_NAME;
    }

    public function getCompanyIdKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_COMPANY_ID;
    }

    public function getOrderKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_ORDER;
    }

    public function getIsActiveKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_IS_ACTIVE;
    }

    public function getRequestTypeKey(): string
    {
        return StoreAccountDimensionItemInterface::KEY_REQUEST_TYPE;
    }

    public function getActor(): User
    {
        return $this->createdBy;
    }
}
