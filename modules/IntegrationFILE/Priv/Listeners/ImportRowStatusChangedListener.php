<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\IntegrationFILE\Priv\Events\ImportRowStatusChangedEvent;
use Modules\IntegrationFILE\Priv\Services\ImportService;

class ImportRowStatusChangedListener implements ShouldQueue
{
    use InteractsWithQueue;

    public $tries = 1;

    protected ImportService $importService;

    public function __construct(ImportService $importService)
    {
        $this->importService = $importService;
    }

    public function handle(ImportRowStatusChangedEvent $event): void
    {
        $this->importService->changeStatusIfIsFinished($event->getImportId());
    }
}
