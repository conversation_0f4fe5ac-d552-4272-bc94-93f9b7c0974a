<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Importers;

use App\Company;
use App\Instance;
use Modules\Accounting\Pub\Facades\ProviderFacade;
use Modules\Accounting\Pub\Interfaces\CreateProviderRequestInterface;
use Modules\IntegrationFILE\Priv\Dtos\CreateProviderDto;
use Modules\IntegrationFILE\Priv\Dtos\UpdateProviderDto;
use Modules\IntegrationFILE\Priv\Entities\Import;
use Modules\IntegrationFILE\Priv\Entities\ImportRow;
use Modules\Users\Pub\Facades\CompanyFacade;

class ProviderImporterService extends AbstractImporter implements ImporterInterface
{
    protected ProviderFacade $providerFacade;

    public function __construct(CompanyFacade $companyFacade, ProviderFacade $providerFacade)
    {
        parent::__construct($companyFacade);

        $this->providerFacade = $providerFacade;
    }

    public function importRow(ImportRow $importRow, array $row): int
    {
        $instance = $importRow->import->instance;
        $row = $this->addCompanyToRow($instance, $row);

        $provider = $this->providerFacade->match(
            $row[CreateProviderRequestInterface::KEY_TAX_ID] ?? null,
            $row[CreateProviderRequestInterface::KEY_ERP_ID] ?? null,
            $row[CreateProviderRequestInterface::KEY_COUNTRY] ?? null,
            empty($row[CreateProviderRequestInterface::KEY_CURRENT_SLUG]) ? null : $row[CreateProviderRequestInterface::KEY_CURRENT_SLUG],
            $instance->id,
        );

        if ($provider === null) {
            $provider = $this->providerFacade->create(
                CreateProviderDto::createFromArray($instance, $row),
                $importRow->import->createdBy,
            );
        } else {
            $provider = $this->providerFacade->update(
                UpdateProviderDto::createFromProviderAndArray($provider, $row),
                $importRow->import->createdBy,
            );
        }

        return $provider->id;
    }

    public function afterRows(Import $import, \Iterator $iterator): void {}


    public function errorFileHeaders(): array
    {
        return [
            CreateProviderRequestInterface::KEY_NAME,
            CreateProviderRequestInterface::KEY_TAX_ID,
            'error',
        ];
    }

    public function getIdentifiers(array $data): array
    {
        return [
            $data[CreateProviderRequestInterface::KEY_NAME] ?? self::UNAVAILABLE,
            $data[CreateProviderRequestInterface::KEY_TAX_ID] ?? self::UNAVAILABLE,
        ];
    }

    private function addCompanyToRow(Instance $instance, array $row): array
    {
        $companyCode = $row[CreateProviderRequestInterface::KEY_COMPANY] ?? '';
        $company = $this->companyFacade->findCompanyByCodeAndInstanceId(
            $companyCode,
            $instance->id,
        );

        if ($company instanceof Company) {
            $row[CreateProviderRequestInterface::KEY_COMPANY_ID] = $company->slug;
        }

        return $row;
    }
}
