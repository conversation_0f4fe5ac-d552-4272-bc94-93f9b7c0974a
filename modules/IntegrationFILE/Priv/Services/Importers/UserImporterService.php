<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Importers;

use App\BillingAPI\ValueObjects\Email;
use App\Company;
use App\Helpers\ArrayHelper;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;
use Modules\Common\ValueObjects\Slug;
use Modules\IntegrationFILE\Priv\Entities\Import;
use Modules\IntegrationFILE\Priv\Entities\ImportRow;
use Modules\IntegrationFILE\Priv\Repositories\ImportRowRepository;
use Modules\IntegrationFILE\Pub\Enums\ImportStatusEnum;
use Modules\IntegrationFILE\Pub\Exceptions\UserExistsInAnotherInstanceException;
use Modules\Users\Priv\Rules\SupervisorByEmailRule;
use Modules\Users\Pub\Facades\CompanyFacade;
use Modules\Users\Pub\Facades\UserFacade;
use Modules\Users\Pub\Factories\CreateOrUpdateUserRequestFactory;
use Modules\Users\Pub\Requests\CreateOrUpdateUserRequest;

class UserImporterService extends AbstractImporter implements ImporterInterface
{
    protected const ASSISTANTS_DELIMITER = ';';

    protected UserFacade $userFacade;

    protected CreateOrUpdateUserRequestFactory $createOrUpdateUserRequestFactory;

    protected ImportRowRepository $importRowRepository;

    public function __construct(
        UserFacade $userFacade,
        CompanyFacade $companyFacade,
        CreateOrUpdateUserRequestFactory $createOrUpdateUserRequestFactory,
        ImportRowRepository $importRowRepository
    ) {
        parent::__construct($companyFacade);

        $this->userFacade = $userFacade;
        $this->createOrUpdateUserRequestFactory = $createOrUpdateUserRequestFactory;
        $this->importRowRepository = $importRowRepository;
    }

    public function importRow(ImportRow $importRow, array $row): int
    {
        $import = $importRow->import;

        // company alias
        $companyCode = $row[CreateOrUpdateUserRequest::KEY_COMPANY]
            ?? $row[CreateOrUpdateUserRequest::KEY_COMPANY_CODE]
            ?? '';
        $row[CreateOrUpdateUserRequest::KEY_COMPANY] = $companyCode;

        $company = $this->getCompany($companyCode, $import->instance_id);

        // mpk, cost center alias
        $row[CreateOrUpdateUserRequest::KEY_MPK_CODE] = $row[CreateOrUpdateUserRequest::KEY_MPK_CODE]
            ?? $row[CreateOrUpdateUserRequest::KEY_COST_CENTER_CODE]
            ?? '';

        // grade alias
        $row[CreateOrUpdateUserRequest::KEY_GRADE] = $row[CreateOrUpdateUserRequest::KEY_GRADE]
            ?? $row[CreateOrUpdateUserRequest::KEY_EMPLOYEE_GRADE]
            ?? '';

        // worklocation alias
        $row[CreateOrUpdateUserRequest::KEY_WORK_LOCATION] = $row[CreateOrUpdateUserRequest::KEY_WORK_LOCATION] ?? $row[CreateOrUpdateUserRequest::KEY_WORK_LOCATION_CITY] ?? '';

        $emailString = strtolower($row[CreateOrUpdateUserRequest::KEY_EMAIL]);
        $email = new Email($emailString);
        $user = $this->userFacade->findUserByEmailAndCompanyId($emailString, $company->id);

        $createOrUpdateRequest = $this->createOrUpdateUserRequestFactory->createFromArrayByUserAndCompany(
            $row,
            $import->user,
            $company,
            $user
        );

        if ($user === null && $this->userFacade->checkIsUserExistsAnywhere(
                $email,
                $importRow->import->instance_id
            ) === true) {
            throw UserExistsInAnotherInstanceException::create();
        } else {
            if ($user === null) {
                $user = $this->userFacade->create($createOrUpdateRequest);
            } else {
                $user = $this->userFacade->update($createOrUpdateRequest);
            }
        }

        if ($createOrUpdateRequest->isActive() !== $user->isActive()) {
            $createOrUpdateRequest->isActive()
                ? $this->userFacade->activate($user->slug, $import->user)
                : $this->userFacade->deactivate($user->slug, Carbon::now(), $import->user);
        }

        return $user->id;
    }

    public function errorFileHeaders(): array
    {
        return [
            CreateOrUpdateUserRequest::KEY_EMAIL,
            CreateOrUpdateUserRequest::KEY_ERP_ID,
            'error'
        ];
    }

    public function getIdentifiers(array $data): array
    {
        return [
            isset($data[CreateOrUpdateUserRequest::KEY_EMAIL]) ? strtolower(
                $data[CreateOrUpdateUserRequest::KEY_EMAIL]
            ) : self::UNAVAILABLE,
            isset($data[CreateOrUpdateUserRequest::KEY_ERP_ID]) ? strtolower(
                $data[CreateOrUpdateUserRequest::KEY_ERP_ID]
            ) : self::UNAVAILABLE,
        ];
    }

    public function afterRows(Import $import, \Iterator $iterator): void
    {
        $iterator->next();

        $instanceId = $import->instance_id;
        $instanceSlug = new Slug($import->instance->slug);

        while ($iterator->valid()) {
            $row = $iterator->current();

            if (ArrayHelper::isNotEmpty($row)) {
                $company = $this->getCompany($row[CreateOrUpdateUserRequest::KEY_COMPANY] ?? '', $import->instance_id);
                $companyId = $company->id;

                $user = $this->userFacade->findUserByEmailAndCompanyId(
                    strtolower($row[CreateOrUpdateUserRequest::KEY_EMAIL]),
                    $companyId
                );
                if ($user !== null) {
                    try {
                        $rowAssistants = str_replace(' ', '', $row[CreateOrUpdateUserRequest::KEY_ASSISTANTS]);

                        $assistants = explode(self::ASSISTANTS_DELIMITER, $rowAssistants);
                        $assistants = new Collection($assistants ?? []);
                        $assistants = $assistants->map(function ($asssistant) {
                            return strtolower($asssistant);
                        })->filter(function ($asssistant) {
                            return !empty($asssistant);
                        })->toArray();

                        $this->validateSupervisorAndAssistants(
                            strtolower($row[CreateOrUpdateUserRequest::KEY_SUPERVISOR_EMAIL]),
                            $assistants,
                            $instanceId,
                            $companyId,
                            $user
                        );

                        $this->updateSupervisor($company, $user, $row);
                        $this->setAssistants($company, $user, $assistants);
                    } catch (ValidationException $exception) {
                        if ($user) {
                            $this->userFacade->deactivate($user->slug, Carbon::now(), $import->createdBy);
                        }
                        $importRow = $this->importRowRepository->findByImportIdAndRowNumber(
                            $import->id,
                            $iterator->key()
                        );
                        $importRow->status = ImportStatusEnum::ERROR();
                        $importRow->message = implode(', ', Arr::flatten($exception->errors()));
                        $this->importRowRepository->persist($importRow);
                    } catch (\Throwable $exception) {
                        \Log::error($exception);
                        if ($user) {
                            $this->userFacade->deactivate($user->slug, Carbon::now(), $import->createdBy);
                        }
                        $importRow = $this->importRowRepository->findByImportIdAndRowNumber(
                            $import->id,
                            $iterator->key()
                        );
                        $importRow->status = ImportStatusEnum::ERROR();
                        $importRow->message = $exception->getMessage();
                        $this->importRowRepository->persist($importRow);
                    }
                }
            }

            $iterator->next();
        }
    }

    private function updateSupervisor(Company $company, User $user, array $row): void
    {
        $supervisorEmail = $row[CreateOrUpdateUserRequest::KEY_SUPERVISOR_EMAIL];
        if (!empty($supervisorEmail)) {
            $supervisor = $this->userFacade->findUserByEmailAndCompanyOrInstance(
                strtolower($supervisorEmail),
                $company->id,
                $company->instance_id
            );

            $this->userFacade->changeSupervisor($user, $supervisor);
        } elseif ($user->user_id !== null && empty($supervisorEmail)) {
            $this->userFacade->changeSupervisor($user, null);
        }
    }

    private function setAssistants(Company $company, User $user, array $assistants): void
    {
        $assistantCollection = new Collection();
        foreach ($assistants as $assistantEmail) {
            $assistantCollection->push(
                $this->userFacade->findUserByEmailAndCompanyOrInstance(
                    $assistantEmail,
                    $company->id,
                    $company->instance_id
                )
            );
        }

        $this->userFacade->setAssistants($user, $assistantCollection);
    }

    /**
     * @param $row
     * @param $assistants
     * @param int $instanceId
     * @param int $companyId
     * @param User $user
     * @throws ValidationException
     */
    private function validateSupervisorAndAssistants(
        $row,
        $assistants,
        int $instanceId,
        int $companyId,
        User $user
    ): void {
        $validator = \Validator::make([
            'supervisor' => $row,
            'assistants' => $assistants,
        ],
            [
                'supervisor' => [
                    'nullable',
                    'string',
                    'exists:users,email,instance_id,' . $instanceId,
                    new SupervisorByEmailRule($user)
                ],
                'assistants' => [
                    'nullable',
                    'array'
                ],
                'assistants.*' => [
                    'nullable',
                    'string',
                    'distinct',
                    'exists:users,email,instance_id,' . $instanceId . ',company_id,' . $companyId
                ]
            ],
            [
                'supervisor.exists' => trans('integrationFile::errors.supervisor-not-found'),
                'assistants.*.exists' => trans('integrationFile::errors.assistant-not-found'),
            ]);

        $validator->validate();
    }
}
