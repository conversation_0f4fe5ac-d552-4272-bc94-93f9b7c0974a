<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Importers;

use Modules\IntegrationFILE\Priv\Entities\Import;
use Modules\IntegrationFILE\Priv\Entities\ImportRow;
use Modules\Users\Pub\Facades\CompanyFacade;
use Modules\Users\Pub\Facades\UserAccountDimensionFacade;
use Modules\Users\Pub\Factories\NewUserDimensionItemRequestFactory;
use Modules\Users\Pub\Requests\NewUserDimensionItemRequest;

class UserDimensionItemImporterService implements ImporterInterface
{
    private NewUserDimensionItemRequestFactory $newUserDimensionItemRequestFactory;

    private UserAccountDimensionFacade $userAccountDimensionFacade;
    private CompanyFacade $companyFacade;

    public function __construct(
        CompanyFacade $companyFacade,
        NewUserDimensionItemRequestFactory $newUserDimensionItemRequestFactory,
        UserAccountDimensionFacade $userAccountDimensionFacade
    ) {
        $this->newUserDimensionItemRequestFactory = $newUserDimensionItemRequestFactory;
        $this->userAccountDimensionFacade = $userAccountDimensionFacade;
        $this->companyFacade = $companyFacade;
    }

    public function importRow(ImportRow $importRow, array $row): int
    {
        $row[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS] ??= $importRow->import->subtype;

        $company = null;
        $companyCode = $row[NewUserDimensionItemRequest::KEY_COMPANY_CODE] ?? '';
        if (!empty($companyCode)) {
            $company = $this->companyFacade->findCompanyByCodeAndInstanceId(
                $companyCode,
                $importRow->import->instance->id,
            );
        }

        $newUserDimensionItemRequest = $this->newUserDimensionItemRequestFactory->createFromImportRowData(
            $importRow->import->instance,
            $company,
            $row,
        );

        $userAccountDimensionItem = $this->userAccountDimensionFacade->create($newUserDimensionItemRequest);

        return $userAccountDimensionItem->user->id;
    }

    public function errorFileHeaders(): array
    {
        return [
            NewUserDimensionItemRequest::KEY_EMAIL,
            NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS,
            NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSION_ITEMS,
            'error',
        ];
    }

    public function getIdentifiers(array $data): array
    {
        return [
            isset($data[NewUserDimensionItemRequest::KEY_EMAIL]) ? strtolower(
                $data[NewUserDimensionItemRequest::KEY_EMAIL],
            ) : self::UNAVAILABLE,
            isset($data[NewUserDimensionItemRequest::KEY_COMPANY_CODE]) ? strtolower(
                $data[NewUserDimensionItemRequest::KEY_COMPANY_CODE],
            ) : self::UNAVAILABLE,
            isset($data[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS]) ? strtolower(
                $data[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS],
            ) : self::UNAVAILABLE,
            isset($data[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSION_ITEMS]) ? strtolower(
                $data[NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSION_ITEMS],
            ) : self::UNAVAILABLE,
        ];
    }

    public function afterRows(Import $import, \Iterator $iterator): void {}
}
