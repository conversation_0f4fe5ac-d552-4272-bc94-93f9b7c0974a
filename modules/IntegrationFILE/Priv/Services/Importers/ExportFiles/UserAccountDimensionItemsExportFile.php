<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Importers\ExportFiles;

use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;
use Modules\Users\Pub\Requests\NewUserDimensionItemRequest;

class UserAccountDimensionItemsExportFile implements ExportFileInterface
{
    private string $accountDimensionCode;
    private int $instanceId;
    private ?int $companyId;

    public function __construct(string $accountDimensionCode, int $instanceId, ?int $companyId = null)
    {
        $this->accountDimensionCode = $accountDimensionCode;
        $this->instanceId = $instanceId;
        $this->companyId = $companyId;
    }

    public function collection(): Collection
    {
        $builder = UserAccountDimensionItem::query()
            ->whereHas('accountDimension', function ($query) {
                $query->where('code', $this->accountDimensionCode);
            })
            ->whereHas('user', function ($query) {
                $query->where('instance_id', $this->instanceId);
                if ($this->companyId) {
                    $query->where('company_id', $this->companyId);
                }
            });

        return $builder
            ->get()
            ->map(function (UserAccountDimensionItem $userAccountDimensionItem) {
                return [
                    NewUserDimensionItemRequest::KEY_EMAIL => $userAccountDimensionItem->user->email,
                    NewUserDimensionItemRequest::KEY_COMPANY_CODE => $userAccountDimensionItem->user->company->code,
                    NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS => $userAccountDimensionItem->accountDimension->code,
                    NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSION_ITEMS => $userAccountDimensionItem->accountDimensionItem->code,
                    NewUserDimensionItemRequest::KEY_RELATIONSHIP_TYPE => $userAccountDimensionItem->type,
                    NewUserDimensionItemRequest::KEY_UNIQUE_DIMENSION_ITEM_FOR_USER => '0',
                ];
            });
    }

    public function headings(): array
    {
        return [
            NewUserDimensionItemRequest::KEY_EMAIL,
            NewUserDimensionItemRequest::KEY_COMPANY_CODE,
            NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSIONS,
            NewUserDimensionItemRequest::KEY_ACCOUNT_DIMENSION_ITEMS,
            NewUserDimensionItemRequest::KEY_RELATIONSHIP_TYPE,
            NewUserDimensionItemRequest::KEY_UNIQUE_DIMENSION_ITEM_FOR_USER,
        ];
    }
}