<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Importers\ExportFiles;

use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;

class AccountDimensionItemsExportFile implements ExportFileInterface
{
    private string $accountDimensionCode;
    private int $instanceId;
    private ?int $companyId;

    public function __construct(string $accountDimensionCode, int $instanceId, ?int $companyId = null)
    {
        $this->accountDimensionCode = $accountDimensionCode;
        $this->instanceId = $instanceId;
        $this->companyId = $companyId;
    }

    public function collection(): Collection
    {
        $builder = AccountDimensionItem::query()
            ->whereHas('accountDimension', function ($query) {
                $query->where('code', $this->accountDimensionCode);
            })
            ->where('instance_id', $this->instanceId);

        if ($this->companyId) {
            $builder->where('company_id', $this->companyId);
        }

        return $builder
            ->get()
            ->map(function (AccountDimensionItem $accountDimensionItem) {
                return [
                    'code' => $accountDimensionItem->code,
                    'name' => $accountDimensionItem->name,
                    'is_active' => (string)$accountDimensionItem->is_active,
                    'order' => (string)$accountDimensionItem->order,
                    'request_type' => $accountDimensionItem->request_type,
                    'company_code' => $accountDimensionItem->company->code ?? ''
                ];
            });
    }

    public function headings(): array
    {
        return [
            'code',
            'name',
            'is_active',
            'order',
            'request_type',
            'company_code',
        ];
    }
}