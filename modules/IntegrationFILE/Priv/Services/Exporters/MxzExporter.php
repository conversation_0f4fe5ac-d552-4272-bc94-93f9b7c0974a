<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Enum\AvailableLanguagesEnum;
use App\FileSystem\Filesystem;
use App\Services\SlugGeneratorService;
use Carbon\Carbon;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;
use Excel;

class MxzExporter implements SingleFileExporterInterface
{
    private const FILE_DATE_FORMAT = 'Ymd';
    private const SHORT_DATE_FORMAT = 'm/y';

    protected const TMP_DIR_PATH = 'tmp/mxz';

    private const LINE_TYPE_CREDIT = 'Credit';
    private const LINE_TYPE_DEBIT = 'Debit';

    private const LINE_ACCOUNT_TYPE_CREDIT = 'K';
    private const LINE_ACCOUNT_TYPE_DEBIT = 'S';

    private const COLUMN_LINE_TYPE = 0;
    private const COLUMN_ACCOUNT_TYPE = 1;
    private const COLUMN_MOVEMENT_TYPE = 2;
    private const COLUMN_ACCOUNT_NUMBER = 3;
    private const COLUMN_AMOUNT_IN_DOCUMENT_CURRENCY = 4;
    private const COLUMN_TAX_CODE = 5;
    private const COLUMN_ASSIGNMENT = 6;
    private const COLUMN_TEXT_LOCAL = 7;
    private const COLUMN_TEXT_ENGLISH = 8;
    private const COLUMN_PROFIT_CENTER = 9;
    private const COLUMN_COST_CENTER = 10;
    private const COLUMN_TRADING_PARTNER = 11;
    private const COLUMN_CALCULATE_TAX = 12;

    private const SPECIAL_VAT_CODE = 'V9';

    protected Filesystem $filesystem;

    protected SlugGeneratorService $slugGeneratorService;

    private array $fileNames;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
        $this->fileNames = [];
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();

        /** @var SettlementSummaryViewObject $settlementSummaryViewObject */
        foreach ($claimSummaryCollection as $settlementSummaryViewObject) {
            $content = $this->getHeaders();

            $content = array_merge($content, $this->processClaimSummary($settlementSummaryViewObject));

            $relativeTmpPath = $this->persist($content);

            $fileName = $this->getFileName($settlementSummaryViewObject);

            $exportFileDtoCollection->push(
                new ExportFileDto(
                    $fileName,
                    ExportFileTypeEnum::XLSX(),
                    $relativeTmpPath
                )
            );
        }

        return $exportFileDtoCollection;
    }

    private function getFileName(SettlementSummaryViewObject $settlementSummaryViewObject): string
    {
        $fileName = $fileNameWithoutCounter = sprintf(
            '%s_%s',
            'Br_rozl',
            $settlementSummaryViewObject->getCompletionDate()
                ? $settlementSummaryViewObject->getCompletionDate()->format(self::FILE_DATE_FORMAT)
                : Carbon::now()->format(self::FILE_DATE_FORMAT)
        );

        if (!isset($this->fileNames[$fileNameWithoutCounter])) {
            $this->fileNames[$fileNameWithoutCounter] = 0;
        }

        if ($this->fileNames[$fileNameWithoutCounter] > 0) {
            $fileName = sprintf(
                '%s (%d)',
                $fileNameWithoutCounter,
                $this->fileNames[$fileNameWithoutCounter]
            );
        }

        $this->fileNames[$fileNameWithoutCounter] += 1;

        return $fileName;
    }

    protected function persist(array $content): string
    {
        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());

        $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);
        Excel::store(new ExcelArrayExporter($content), $filePath);

        return $filePath;
    }

    protected function processClaimSummary(SettlementSummaryViewObject $settlement): array
    {
        return array_merge(
            $this->prepareDocumentSummaryLine($settlement),
            $this->prepareDocumentsLines($settlement)
        );
    }

    private function prepareDocumentsLines(SettlementSummaryViewObject $settlement): array
    {
        $documentLines = [];

        /**
         * @var DocumentSummaryViewObject $document
         */
        foreach ($settlement->getDocuments() as $document) {
            $documentLine = $this->prepareBaseDocumentLineRow($settlement);
            $date = $document->getIssueDate() ? $document->getIssueDate()->format(self::SHORT_DATE_FORMAT) : '';

            /**
             * @var DocumentElementViewObject $documentElement
             */
            $documentElement = $document->getDocumentElements()->first();
            $documentLine[self::COLUMN_ACCOUNT_NUMBER] = $documentElement->getAccountingAccount()->getCode();
            $documentLine[self::COLUMN_AMOUNT_IN_DOCUMENT_CURRENCY] = $document->getInstanceCurrencyGrossAmount();
            $documentLine[self::COLUMN_TAX_CODE] = $documentElement->getVatNumber()->getCode();
            $documentLine[self::COLUMN_ASSIGNMENT] = sprintf(
                '%s_%s',
                $date,
                $settlement->getNumber()
            );
            $documentLine[self::COLUMN_TEXT_LOCAL] = sprintf(
                '%s_%s',
                $date,
                trans($documentElement->getType()->getShortName(), [], (string)AvailableLanguagesEnum::PL())
            );
            $documentLine[self::COLUMN_TEXT_ENGLISH] = sprintf(
                '%s_%s',
                $date,
                trans($documentElement->getType()->getShortName(), [], (string)AvailableLanguagesEnum::EN())
            );
            $documentLine[self::COLUMN_COST_CENTER] = $documentElement->getMpk()->getCode();
            $documentLine[self::COLUMN_CALCULATE_TAX] =
                $documentElement->getVatNumber()->getCode() === self::SPECIAL_VAT_CODE
                    ? ''
                    : 'x';

            $documentLines[] = $documentLine;
        }

        return $documentLines;
    }

    protected function prepareDocumentSummaryLine(SettlementSummaryViewObject $settlement): array
    {
        $preparedLine = $this->prepareBaseDocumentLineRow($settlement);


        /**
         * @var DocumentSummaryViewObject $document
         */
        $document = $settlement->getDocuments()->first();

        $preparedLine[self::COLUMN_LINE_TYPE] = self::LINE_TYPE_CREDIT;
        $preparedLine[self::COLUMN_ACCOUNT_TYPE] = self::LINE_ACCOUNT_TYPE_CREDIT;
        $preparedLine[self::COLUMN_ACCOUNT_NUMBER] = $document->getProvider() ? $document->getProvider()->getErpId() : '';
        $preparedLine[self::COLUMN_AMOUNT_IN_DOCUMENT_CURRENCY] =
            $settlement->getDocuments()->getTotalAmountInInstanceCurrency();
        $preparedLine[self::COLUMN_TEXT_LOCAL] = $preparedLine[self::COLUMN_ASSIGNMENT];
        $preparedLine[self::COLUMN_TEXT_ENGLISH] = $preparedLine[self::COLUMN_ASSIGNMENT];

        return [$preparedLine];
    }

    protected function prepareBaseDocumentLineRow(SettlementSummaryViewObject $settlement): array
    {
        return [
            self::LINE_TYPE_DEBIT, // Debit/Credit
            self::LINE_ACCOUNT_TYPE_DEBIT, // Account type
            520, // Movement type
            '', // Account number
            '', // Amount in document currency
            '', // Tax code
            sprintf(
                '%s_%s',
                $settlement->getCompletionDate()
                    ? $settlement->getCompletionDate()->format(self::SHORT_DATE_FORMAT)
                    : Carbon::now()->format(self::SHORT_DATE_FORMAT),
                $settlement->getNumber()
            ), // Assignment
            '', // Text in local language
            '', // Text in English language
            '', // Profit Center
            '', // Cost Center
            '', // Trading Partner
            '', // Calculate Tax
        ];
    }

    private function getHeaders(): array
    {
        return [
            [
                'Debit/Credit',
                'Account type',
                'Movement type',
                'Account number',
                'Amount in document currency',
                'Tax code',
                'Assignment',
                'Text in local language',
                'Text in English language',
                'Profit Center',
                'Cost Center',
                'Trading Partner',
                'Calculate Tax',
            ],
        ];
    }
}
