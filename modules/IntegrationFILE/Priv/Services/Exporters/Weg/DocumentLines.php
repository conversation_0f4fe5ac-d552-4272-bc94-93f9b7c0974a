<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters\Weg;

class DocumentLines
{
    private array $creditLine = [];
    private array $documentLines = [];

    public function addCreditLine(array $creditLine): void
    {
        $this->creditLine = $creditLine;
    }

    public function addDocumentLine(array $documentLine): void
    {
        $this->documentLines[] = $documentLine;
    }

    public function toArray(): array
    {
        $rows = $this->documentLines;

        if (empty($rows) && empty($this->creditLine)) {
            return [];
        }

        // put credit line as the first element
        array_unshift($rows, $this->creditLine);

        return $rows;
    }

    /**
     * @param mixed $fieldName
     * @param mixed $value
     * @return void
     */
    public function updateCreditLineField($fieldName, $value): void
    {
        $this->creditLine[$fieldName] = $value;
    }
}
