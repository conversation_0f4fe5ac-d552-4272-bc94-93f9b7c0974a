<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\FileSystem\Filesystem;
use App\Services\SlugGeneratorService;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;
use SimpleXMLElement;

class KMTExporter implements SingleFileExporterInterface
{
    private Filesystem $filesystem;
    private SlugGeneratorService $slugGeneratorService;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();

        $content = $this->export($claimSummaryCollection);

        $relativeTmpPath = $this->persist($content);

        /** @var SettlementSummaryViewObject $claim */
        $claim = $claimSummaryCollection->first();

        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s_%s',
                    $claim->getCompany()->getCode(),
                    $claim->getSettledAt()->format('Y-m-d'),
                ),
                ExportFileTypeEnum::XML(),
                $relativeTmpPath,
            ),
        );

        return $exportFileDtoCollection;
    }

    protected function persist(string $content): string
    {
        $this->init();

        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XML());

        $filePath = sprintf('/tmp/%s', $fileName);

        $absoluteTmpPath = storage_path(sprintf('app/%s', $filePath));

        $this->filesystem->put($absoluteTmpPath, $content);

        return $filePath;
    }

    private function init(): void
    {
        if ($this->filesystem->exists(storage_path('app/tmp')) === false) {
            $this->filesystem->makeDirectory(storage_path('app/tmp'));
        }
    }

    private function export(ClaimSummaryCollection $claimSummaryCollection): string
    {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><Dokumenty/>');

        /** @var SettlementSummaryViewObject $claim */
        foreach ($claimSummaryCollection as $claim) {
            /** @var DocumentSummaryViewObject $doc */
            foreach ($claim->getDocuments() as $doc) {
                $xmlDoc = $xml->addChild('Dokument');

                $headerFields = [
                    'Kategoria' => 'PKEwidencja',
                    'Symbol' => 'PKE',
                    'NumerObcy' => $doc->getNumber(),
                    'DataWplywu' => $doc->getReceivedDate()->format('Y-m-d'),
                    'DataWystawienia' => $doc->getIssueDate()->format('Y-m-d'),
                    'DataOperacji' => $doc->vatDate()->format('Y-m-d'),
                    'Opis' => $claim->getNumber(),
                ];

                if (!empty($description = $doc->getDescription())){
                    $headerFields['Opis'] .= ' - ';
                    $headerFields['Opis'] .= $description;
                }

                $header = $xmlDoc->addChild('Naglowek');
                foreach ($headerFields as $key => $value) {
                    $header->addChild($key, htmlspecialchars($value));
                }

                $podmiot = $xmlDoc->addChild('Podmiot');
                $podmiot->addChild('Pracownik', $doc->getProvider()->getErpId());

                $podsumowanie = $xmlDoc->addChild('Podsumowanie')->addChild('Razem');
                $podsumowanie->addChild('Waluta', 'PLN');
                $podsumowanie->addChild('WartoscBrutto', $doc->getInstanceCurrencyGrossAmount());

                $xmlDoc->addChild('Cechy');
            }
        }

        return $xml->asXML();
    }

}