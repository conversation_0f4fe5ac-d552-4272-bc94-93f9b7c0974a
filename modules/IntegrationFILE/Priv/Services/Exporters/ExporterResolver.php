<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use Modules\IntegrationFILE\Priv\Interfaces\Exporters\ExporterInterface;
use Modules\IntegrationFILE\Pub\Enums\ExportFormatEnum;
use Modules\IntegrationFILE\Pub\Exceptions\ExportTypeNotImplementedException;

class ExporterResolver
{
    public function resolve(ExportFormatEnum $exportFormatEnum): ExporterInterface
    {
        switch ($exportFormatEnum) {
            case ExportFormatEnum::STALGAST_XML():
                return resolve(StalgastExporter::class);
            case ExportFormatEnum::GWR_TXT():
                return resolve(GwrExporter::class);
            case ExportFormatEnum::TRIUMPH_CSV():
                return resolve(TriumphExporter::class);
            case ExportFormatEnum::WEG_CSV():
                return resolve(WegExporter::class);
            case ExportFormatEnum::PIR_XLSX():
                return resolve(PirExporter::class);
            case ExportFormatEnum::MXZ_XLSX():
                return resolve(MxzExporter::class);
            case ExportFormatEnum::NVT_XLSX():
                return resolve(NvtExporter::class);
            case ExportFormatEnum::STC_XLSX():
                return resolve(StcExporter::class);
            case ExportFormatEnum::ASE381_XLSX():
                return resolve(Ase381Exporter::class);
            case ExportFormatEnum::TMH_XLSX():
                return resolve(TmhExporter::class);
            case ExportFormatEnum::FRE036_XLSX():
                return resolve(FRE036Exporter::class);
            case ExportFormatEnum::JWA_TXT():
                return resolve(JWAExporter::class);
            case ExportFormatEnum::BLT_XLSX():
                return resolve(BLTExporter::class);
            case ExportFormatEnum::ALT_XLSX():
                return resolve(ALTExporter::class);
            case ExportFormatEnum::EGO_TXT():
                return resolve(EGOExporter::class);
            case ExportFormatEnum::FLX_XLSX():
                return resolve(FLXExporter::class);
            case ExportFormatEnum::KMT_XML():
                return resolve(KMTExporter::class);
            case ExportFormatEnum::CLG_XML():
                return resolve(CLGExporter::class);
            case ExportFormatEnum::MND_XLSX_ENOVA():
                return resolve(MNDExporter::class);
            default:
                throw ExportTypeNotImplementedException::create($exportFormatEnum);
        }
    }
}
