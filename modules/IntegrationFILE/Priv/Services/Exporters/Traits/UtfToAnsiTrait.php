<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters\Traits;

trait UtfToAnsiTrait
{
    protected function convertArray(array $line): array
    {
       return \array_map(function ($value) {
            return $value !== null
                ? iconv(
                    'utf-8',
                    'windows-1250//TRANSLIT//IGNORE',
                    (string)$value
                )
                : null;
        }, $line);
    }
}
