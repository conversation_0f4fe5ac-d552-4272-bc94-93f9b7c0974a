<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Services\SlugGeneratorService;
use Carbon\Carbon;
use Excel;
use Modules\Accounting\Pub\ViewObjects\CompanyViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\ProviderViewObject;
use Modules\Accounting\Pub\ViewObjects\SelectedAccountDimensionViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Priv\Services\Exporters\MND\Line;
use Modules\IntegrationFILE\Priv\Services\Exporters\MND\LineCollection;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;

/**
 * SAP Format
 */
class MNDExporter implements SingleFileExporterInterface
{
    private const TMP_DIR_PATH = 'tmp/mnd';

    private SlugGeneratorService $slugGeneratorService;

    public function __construct(SlugGeneratorService $slugGeneratorService)
    {
        $this->slugGeneratorService = $slugGeneratorService;
    }

    /**
     * Processes the claim collection and generates an export file
     */
    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();
        $lines = new LineCollection();

        /** @var SettlementSummaryViewObject $settlementSummaryViewObject */
        foreach ($claimSummaryCollection as $settlementSummaryViewObject) {
            /** @var CompanyViewObject $company */
            $company = $settlementSummaryViewObject->getCompany();
            $instanceCountryCode = $company->getInstance()->getCurrency()->getCode();

            /** @var DocumentSummaryViewObject $documentSummaryViewObject */
            foreach ($settlementSummaryViewObject->getDocuments() as $documentSummaryViewObject) {
                $provider = $documentSummaryViewObject->getProvider();

                /** @var DocumentElementViewObject $documentElementViewObject */
                foreach ($documentSummaryViewObject->getDocumentElements() as $documentElementViewObject) {
                    $line = new Line($settlementSummaryViewObject->getNumber());

                    $this->setDocumentData($line, $documentSummaryViewObject, $provider, $instanceCountryCode);
                    $this->setExpenseTypeData($line, $documentElementViewObject);
                    $this->setPaymentData($line, $documentSummaryViewObject);
                    $this->setVatData($line, $documentElementViewObject, $documentSummaryViewObject);
                    $this->setAnalyticalDescriptionData($line, $documentElementViewObject, $documentSummaryViewObject);
                    $this->setAdditionalData($line, $documentElementViewObject);

                    $lines->push($line);
                }
            }
        }

        $formattedData = $this->formatDataForExport($lines);
        $relativeTmpPath = $this->persist($formattedData);

        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s-%s',
                    'mnd048',
                    Carbon::now()->format('Y-m-d_H-i-s'),
                ),
                ExportFileTypeEnum::XLSX(),
                $relativeTmpPath,
            ),
        );

        return $exportFileDtoCollection;
    }

    /**
     * Finds accounting dimension by name or code
     */
    private function findAccountDimension(
        DocumentElementViewObject $documentElementViewObject,
        string $dimensionNameOrCode,
        string $searchBy = 'name'
    ): ?SelectedAccountDimensionViewObject {
        return $documentElementViewObject->getAccountingDimensions()->first(
            function (SelectedAccountDimensionViewObject $accountDimensionViewObject) use (
                $dimensionNameOrCode,
                $searchBy
            ) {
                if ($searchBy === 'name') {
                    $dimensionValue = $accountDimensionViewObject->getAccountDimensionViewObject()->getName();
                } else {
                    $dimensionValue = $accountDimensionViewObject->getAccountDimensionViewObject()->getCode();
                }

                return $dimensionValue === $dimensionNameOrCode;
            }
        );
    }

    /**
     * Finds category dimension (checks various possible names and codes)
     */
    private function findCategoryDimension(DocumentElementViewObject $documentElementViewObject
    ): ?SelectedAccountDimensionViewObject {
        return $documentElementViewObject->getAccountingDimensions()->first(
            function (SelectedAccountDimensionViewObject $accountDimensionViewObject) {
                $dimensionCode = $accountDimensionViewObject->getAccountDimensionViewObject()->getCode();
                $dimensionName = $accountDimensionViewObject->getAccountDimensionViewObject()->getName();

                return $dimensionCode === 'Kategoria' ||
                    $dimensionName === 'Kategoria' ||
                    $dimensionCode === 'kategoria' ||
                    $dimensionName === 'kategoria' ||
                    $dimensionCode === 'WA4' ||
                    $dimensionName === 'WA4';
            }
        );
    }

    /**
     * Formats date to Y-m-d format or returns null
     */
    private function formatDate(?Carbon $date): ?string
    {
        return $date ? $date->format('Y-m-d') : null;
    }

    /**
     * Sets document data
     */
    private function setDocumentData(
        Line $line,
        DocumentSummaryViewObject $documentSummaryViewObject,
        ProviderViewObject $provider,
        string $instanceCountryCode
    ): void {
        $line->setDefinitionSymbol($provider->getCountryCode() !== $instanceCountryCode ? 'IU' : 'ZR');
        $line->setDocumentNumber($documentSummaryViewObject->getNumber());
        $line->setDocumentNumberK($documentSummaryViewObject->getCorrectedNumber() ?? '');

        $issueDate = $documentSummaryViewObject->getIssueDate();
        $line->setDocumentDate($this->formatDate($issueDate));
        $line->setOperationDate($this->formatDate($issueDate));

        $receivedDate = $documentSummaryViewObject->getReceivedDate();
        $line->setReceiptDate($this->formatDate($receivedDate));

        $line->setEntityCode($provider->getErpId());
    }

    /**
     * Sets expense type data
     */
    private function setExpenseTypeData(Line $line, DocumentElementViewObject $documentElementViewObject): void
    {
        $expenseType = $documentElementViewObject->getType()
            ? trans($documentElementViewObject->getType()->getShortName())
            : '';

        $line->setDescription($expenseType);

        if ($documentElementViewObject->getType()) {
            $line->setVatRegistryHeaderElementsType($expenseType);
        }

        $line->setPaymentsDescription($expenseType);
        $line->setAnalyticalDescriptionDescription($expenseType);
    }

    /**
     * Sets payment data
     */
    private function setPaymentData(Line $line, DocumentSummaryViewObject $documentSummaryViewObject): void
    {
        $isCorrection = $documentSummaryViewObject->getCorrectedNumber() !== null
            && $documentSummaryViewObject->getCorrectedNumber() !== '';

        $line->setPaymentsClass($isCorrection ? 'Naleznosc' : 'Zobowiazanie');
        $line->setPaymentsMethodName(trans('document.' . $documentSummaryViewObject->getPaymentMethod()));

        $issueDate = $documentSummaryViewObject->getIssueDate();
        $line->setPaymentsDueDate($issueDate ? $issueDate->format('Y-m-d') : '');

        $grossAmount = $documentSummaryViewObject->getForeignCurrencyGrossAmount();
        $currencyCode = $documentSummaryViewObject->getCurrency()->getCode();
        $line->setPaymentsAmount($grossAmount . ' ' . $currencyCode);

        $grossAmountPLN = $documentSummaryViewObject->getInstanceCurrencyGrossAmount();
        $line->setPaymentsBookAmount($grossAmountPLN);
    }

    /**
     * Sets VAT data
     */
    private function setVatData(
        Line $line,
        DocumentElementViewObject $documentElementViewObject,
        DocumentSummaryViewObject $documentSummaryViewObject
    ): void {
        $grossAmountPLN = $documentSummaryViewObject->getInstanceCurrencyGrossAmount();
        $line->setVatRegistryHeaderElementsNet($grossAmountPLN);

        $vatCode = '';
        if ($documentElementViewObject->getVatNumber()) {
            $vatCode = $documentElementViewObject->getVatNumber()->getCode();
        }
        $line->setVatRegistryHeaderElementsRateDefinitionCode($vatCode);

        if ($vatCode !== 'ND') {
            $line->setVatRegistryHeaderElementsDeductions('Tak');
        }

        $hasMultipleLines = $documentSummaryViewObject->getDocumentElements()->count() > 1;
        if (!$hasMultipleLines) {
            if ($vatCode === 'ND') {
                $line->setAnalyticalDescriptionClass('-');
            } else {
                $line->setAnalyticalDescriptionClass('ElementOpisuEwidencji');
            }
        }
    }

    /**
     * Sets analytical description data
     */
    private function setAnalyticalDescriptionData(
        Line $line,
        DocumentElementViewObject $documentElementViewObject,
        DocumentSummaryViewObject $documentSummaryViewObject
    ): void {
        if ($documentElementViewObject->getAccountingAccount()) {
            $line->setAnalyticalDescriptionSymbol($documentElementViewObject->getAccountingAccount()->getCode());
        }

        $netAmountPLN = $documentElementViewObject->getInstanceCurrencyNetAmount();
        $line->setAnalyticalDescriptionAmount($netAmountPLN);

        // Set field AnalyticalDescription:Features.TaxCode
        $taxCode = $this->findAccountDimension($documentElementViewObject, 'Kod podatku', 'name');
        if ($taxCode !== null) {
            $line->setAnalyticalDescriptionFeaturesTaxCode($taxCode->getAccountDimensionItemViewObject()->getName());
        }

        // Set field AnalyticalDescription:Features.CostType:Symbol
        $costType = $this->findAccountDimension($documentElementViewObject, 'Rodzaj kosztów', 'name');
        if ($costType !== null) {
            $line->setAnalyticalDescriptionFeaturesCostTypeSymbol(
                $costType->getAccountDimensionItemViewObject()->getCode()
            );
        }

        // Set fields AnalyticalDescription:Features.Department:Symbol and AnalyticalDescription:CostCenter:Name
        $costCenter = $this->findAccountDimension($documentElementViewObject, 'Centrum Kosztów', 'code');
        if ($costCenter !== null) {
            $line->setAnalyticalDescriptionFeaturesDepartmentSymbol(
                $costCenter->getAccountDimensionItemViewObject()->getCode()
            );
            $line->setAnalyticalDescriptionCostCenterName($costCenter->getAccountDimensionItemViewObject()->getCode());
        }

        // Set field AnalyticalDescription:Features.Report:ID
        if ($documentElementViewObject->getAccountingAccount()
            && strpos($documentElementViewObject->getAccountingAccount()->getCode(), '402-03') === 0
        ) {
            $category = $this->findCategoryDimension($documentElementViewObject);
            if ($category !== null) {
                $line->setAnalyticalDescriptionFeaturesReportId(
                    $category->getAccountDimensionItemViewObject()->getCode()
                );
            }
        }
    }

    /**
     * Sets additional data
     */
    private function setAdditionalData(Line $line, DocumentElementViewObject $documentElementViewObject): void
    {
        if ($documentElementViewObject->getDescription()) {
            $line->setFeaturesReservationNumber($documentElementViewObject->getDescription());
        }
    }

    /**
     * Formats data for export with specific column order
     */
    private function formatDataForExport(LineCollection $lines): array
    {
        // Define the exact order of headers as required
        $headers = [
            'Id wniosku',
            'Class',
            'Definicja:Symbol',
            'NumerDokumentu',
            'NrDokumentuK',
            'DataWplywu',
            'DataDokumentu',
            'DataOperacji',
            'Opis',
            'Podmiot:Class',
            'Podmiot:Kod',
            'Platnosci:Class',
            'Platnosci:SposobZaplaty:Nazwa',
            'Platnosci:Termin',
            'Platnosci:Kwota',
            'Platnosci:KwotaKsiegi',
            'Platnosci:Opis',
            'NagEwidencjiVAT.Elementy:Class',
            'NagEwidencjiVAT.Elementy:DefinicjaStawki:Kod',
            'NagEwidencjiVAT.Elementy:Netto',
            'NagEwidencjiVAT.Elementy:VAT',
            'NagEwidencjiVAT.Elementy:Odliczenia',
            'NagEwidencjiVAT.Elementy:Rodzaj',
            'OpisAnalityczny:Class',
            'OpisAnalityczny:Wymiar',
            'OpisAnalityczny:Symbol',
            'OpisAnalityczny:Kwota',
            'OpisAnalityczny:Opis',
            'OpisAnalityczny:Features.Kod podatku',
            'OpisAnalityczny:Features.Kod raportu:ID',
            'OpisAnalityczny:Features.Rodzaj_kosztu:Symbol',
            'OpisAnalityczny:Features.Dzial:Symbol',
            'OpisAnalityczny:Features.Raport:ID',
            'OpisAnalityczny:CentrumKosztow:Nazwa',
            'Features.Numer rezerwacji'
        ];

        $result = [$headers]; // Always include headers

        foreach ($lines as $item) {
            if ($item instanceof Line) {
                $lineArray = $item->toArray();
                $rowData = [];

                foreach ($headers as $header) {
                    $rowData[] = $lineArray[$header] ?? null;
                }

                $result[] = $rowData;
            }
        }

        return $result;
    }

    /**
     * Saves data to Excel file and returns file path
     */
    private function persist(array $content): string
    {
        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());
        $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);

        Excel::store(new ExcelArrayExporter($content), $filePath);

        return $filePath;
    }
}
