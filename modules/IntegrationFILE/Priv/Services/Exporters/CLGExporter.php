<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Document;
use App\Services\SlugGeneratorService;
use Carbon\Carbon;
use Excel;
use Modules\Accounting\Pub\ViewObjects\AllowanceLineViewObject;
use Modules\Accounting\Pub\ViewObjects\AllowanceViewObject;
use Modules\Accounting\Pub\ViewObjects\CompanyViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\LocationViewObject;
use Modules\Accounting\Pub\ViewObjects\ProviderViewObject;
use Modules\Accounting\Pub\ViewObjects\SelectedAccountDimensionViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\UserViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Priv\Services\Exporters\CLG\CLGExcelExporter;
use Modules\IntegrationFILE\Priv\Services\Exporters\CLG\LineCollection;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;

/**
 * CLG Exporter for SAP integration
 *
 * This class exports financial data in the SAP CLG format.
 * It processes settlement summaries, documents, and allowances,
 * generating a structured Excel file compatible with SAP import requirements.
 */
class CLGExporter implements SingleFileExporterInterface
{
    private const TMP_DIR_PATH = 'tmp/clg';
    private const DATE_FORMAT = 'dmy';
    private const LINE_TYPE_HEADER = 'H';
    private const LINE_TYPE_DOCUMENT = 'D';
    private const ACCOUNT_TYPE_40 = '40';
    private const ACCOUNT_TYPE_50 = '50';
    private const ACCOUNT_TYPE_21 = '21';
    private const ACCOUNT_TYPE_31 = '31';
    private const DEFAULT_CODE = '0001';
    private const HEADER_TYPE_SA = 'SA';
    private const INTERNAL_ORDER_CODE = 'WA2';

    private SlugGeneratorService $slugGeneratorService;

    public function __construct(SlugGeneratorService $slugGeneratorService)
    {
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();
        $lines = new LineCollection();

        /** @var SettlementSummaryViewObject $settlementSummaryViewObject */
        foreach ($claimSummaryCollection as $settlementSummaryViewObject) {
            /** @var UserViewObject $employeeViewObject */
            $employeeViewObject = $settlementSummaryViewObject->getEmployee();
            /** @var CompanyViewObject $company */
            $company = $settlementSummaryViewObject->getCompany();
            $companyCode = $company->getCode();

            /** @var DocumentSummaryViewObject $documentSummaryViewObject */
            foreach ($settlementSummaryViewObject->getDocuments() as $documentSummaryViewObject) {
                $lines->push(
                    $this->generateHeaderLine($settlementSummaryViewObject, $documentSummaryViewObject, $companyCode)
                );

                $documentElements = $documentSummaryViewObject->getDocumentElements();
                $firstElement = $documentElements->first();
                $costCenter = $firstElement && $firstElement->getMpk() ? $firstElement->getMpk()->getCode() : '';

                $provider = $documentSummaryViewObject->getProvider();
                /** @var DocumentElementViewObject $documentElementViewObject */
                foreach ($documentSummaryViewObject->getDocumentElements() as $documentElementViewObject) {
                    $lines = $lines->merge(
                        $this->generateDocumentElementLines(
                            $documentElementViewObject,
                            $provider,
                            $costCenter,
                            $documentSummaryViewObject
                        )
                    );
                    if (null === $provider->getEmployee()) {
                        $lines = $lines->merge(
                            $this->generateProviderToEmployeeLines(
                                $provider,
                                $employeeViewObject,
                                $documentElementViewObject,
                                $documentSummaryViewObject
                            )
                        );
                    }
                }
            }
            /** @var AllowanceViewObject $allowance */
            foreach ($settlementSummaryViewObject->getAllowances() as $allowance) {
                /** @var AllowanceLineViewObject $allowanceLine */
                foreach ($allowance->getAllowanceLines() as $allowanceLine) {
                    $lines = $lines->merge(
                        $this->generateAllowanceLines($allowanceLine, $employeeViewObject, $settlementSummaryViewObject)
                    );
                }
            }
        }

        $relativeTmpPath = $this->persist($lines->all());
        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s-%s',
                    'sap-clg',
                    Carbon::now()->format('Y-m-d_H-i-s'),
                ),
                ExportFileTypeEnum::XLSX(),
                $relativeTmpPath,
            ),
        );
        return $exportFileDtoCollection;
    }

    private function generateHeaderLine(
        SettlementSummaryViewObject $settlementSummaryViewObject,
        DocumentSummaryViewObject $documentSummaryViewObject,
        string $companyCode
    ): array {
        $completionDate = $settlementSummaryViewObject->getCompletionDate(
        ) ? $settlementSummaryViewObject->getCompletionDate()->format(self::DATE_FORMAT) : '';

        return [
            self::LINE_TYPE_HEADER,
            $documentSummaryViewObject->getNumber(),
            $documentSummaryViewObject->getNumber(),
            $documentSummaryViewObject->getIssueDate()->format(self::DATE_FORMAT),
            $completionDate,
            $companyCode,
            'PLN',
            self::HEADER_TYPE_SA,
            '',
        ];
    }

    private function generateDocumentElementLines(
        DocumentElementViewObject $documentElementViewObject,
        ProviderViewObject $provider,
        string $costCenter,
        DocumentSummaryViewObject $documentSummaryViewObject
    ): array {
        $vatNumber = $documentElementViewObject->getVatNumber()->getCode();
        $netAmountRaw = $documentElementViewObject->getInstanceCurrencyNetAmount();
        $isCorrection = floatval($netAmountRaw) < 0;
        $netAmount = $isCorrection ? ltrim($netAmountRaw, '-') : $netAmountRaw; // Remove minus sign for corrections
        $accountingCode = $documentElementViewObject->getAccountingAccount()->getCode() ?? '';
        $providerErpId = $provider->getErpId();
        $issueDate = $documentSummaryViewObject->getIssueDate()->format(self::DATE_FORMAT);

        /** @var SelectedAccountDimensionViewObject $internalOrder */
        $internalOrder = $documentElementViewObject->getAccountingDimensions()->first(
            function (SelectedAccountDimensionViewObject $accountDimensionViewObject) {
                return $accountDimensionViewObject->getAccountDimensionViewObject()->getCode(
                    ) === self::INTERNAL_ORDER_CODE;
            }
        );

        $internalOrderItemValue = null !== $internalOrder ? $internalOrder->getAccountDimensionItemViewObject(
        )->getCode() : '';

        $description = $documentElementViewObject->getDescription() ?? '';

        // For correction invoices
        if ($isCorrection) {
            return [
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_50,
                    $accountingCode,
                    $netAmount,
                    $costCenter,
                    $internalOrderItemValue,
                    $description,
                    $vatNumber,
                    $issueDate
                ),
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_21,
                    $providerErpId,
                    $netAmount,
                    '',
                    '',
                    $description,
                    '',
                    $issueDate
                ),
            ];
        }

        // For regular invoices
        $rows = [];

        $rows[] = $this->generateDocumentLine(
            self::ACCOUNT_TYPE_40,
            $accountingCode,
            $netAmount,
            $costCenter,
            $internalOrderItemValue,
            $description,
            $vatNumber,
            $issueDate
        );

        $description = $documentSummaryViewObject->getPaymentMethod() === Document::PAYMENT_TYPE_OWN
            ? 'DO WYP PRAC - ' . $documentSummaryViewObject->getDescription()
            : $documentSummaryViewObject->getDescription();

        $rows[] = $this->generateDocumentLine(
            self::ACCOUNT_TYPE_31,
            $providerErpId,
            $netAmount,
            '',
            '',
            $description,
            '',
            $issueDate
        );

        return $rows;
    }

    private function generateProviderToEmployeeLines(
        ProviderViewObject $provider,
        UserViewObject $employeeViewObject,
        DocumentElementViewObject $documentElementViewObject,
        DocumentSummaryViewObject $documentSummaryViewObject
    ): array {
        $netAmountRaw = $documentElementViewObject->getInstanceCurrencyNetAmount();
        $isCorrection = floatval($netAmountRaw) < 0;
        $netAmount = $isCorrection ? ltrim($netAmountRaw, '-') : $netAmountRaw; // Remove minus sign for corrections
        $providerErpId = $provider->getErpId();
        $issueDate = $documentSummaryViewObject->getIssueDate()->format(self::DATE_FORMAT);

        $description = $documentElementViewObject->getDescription() ?? '';
        $employeeErpId = $employeeViewObject->getErpId() ?? '';

        // For correction invoices
        if ($isCorrection) {
            return [
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_50,
                    $providerErpId,
                    $netAmount,
                    '',
                    '',
                    $description,
                    '',
                    $issueDate
                ),
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_21,
                    $employeeErpId,
                    $netAmount,
                    '',
                    '',
                    $description,
                    '',
                    $issueDate
                ),
            ];
        }

        // For regular invoices
        return [
            $this->generateDocumentLine(
                self::ACCOUNT_TYPE_40,
                $providerErpId,
                $netAmount,
                '',
                '',
                $description,
                '',
                $issueDate
            ),
            $this->generateDocumentLine(
                self::ACCOUNT_TYPE_31,
                $employeeErpId,
                $netAmount,
                '',
                '',
                $description,
                '',
                $issueDate
            ),
        ];
    }

    private function generateAllowanceLines(
        AllowanceLineViewObject $allowanceLine,
        UserViewObject $employeeViewObject,
        SettlementSummaryViewObject $settlementSummaryViewObject
    ): array {
        $date = $settlementSummaryViewObject->getSettledAt()->format(self::DATE_FORMAT);
        $amountRaw = $allowanceLine->getAmount();
        $isCorrection = floatval($amountRaw) < 0;
        $amount = $isCorrection ? ltrim($amountRaw, '-') : $amountRaw; // Remove minus sign for corrections
        $accountingCode = $allowanceLine->getAccountingAccount()->getCode();
        $mpk = $allowanceLine->getMpk()->getCode();
        $employeeErpId = $employeeViewObject->getErpId();

        // For corrections
        if ($isCorrection) {
            return [
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_50,
                    $accountingCode,
                    $amount,
                    $mpk,
                    '',
                    '',
                    '',
                    $date
                ),
                $this->generateDocumentLine(
                    self::ACCOUNT_TYPE_21,
                    $employeeErpId,
                    $amount,
                    '',
                    '',
                    '',
                    '',
                    $date
                ),
            ];
        }

        $description = $settlementSummaryViewObject->isNationalTrip() ? 'RDK' : 'RDZ';
        $requestNumber = $settlementSummaryViewObject->getNumber();
        $description .= '/' . $requestNumber;

        // Get location information
        $locationInfo = '';
        $locations = $settlementSummaryViewObject->getLocations();
        if ($locations && $locations->isNotEmpty()) {
            // Get the first location as the main destination
            /** @var LocationViewObject $mainLocation */
            $mainLocation = $locations->first();

            // Use LocationViewObject methods
            $locationName = $mainLocation->getCity();
            $countryName = $mainLocation->getCountry();

            if ($countryName) {
                $locationInfo = ' - ' . $locationName . ' (' . $countryName . ')';
            } else {
                $locationInfo = ' - ' . $locationName;
            }
        }

        $description .= '- Diety i ryczałty';

        // Add location information
        if ($locationInfo) {
            $description .= $locationInfo;
        }

        $returnDescription = 'DO WYP PRAC - ' . $description;

        $rows = [];

        $rows[] = $this->generateDocumentLine(
            self::ACCOUNT_TYPE_40,
            $accountingCode,
            $amount,
            $mpk,
            '',
            $description,
            '',
            $date
        );

        $rows[] = $this->generateDocumentLine(
            self::ACCOUNT_TYPE_31,
            $employeeErpId,
            $amount,
            '',
            '',
            $returnDescription,
            '',
            $date
        );

        return $rows;
    }

    /**
     * Helper method to generate a document line with common structure
     *
     * @param string $accountType The account type code
     * @param string $accountCode The account code
     * @param string $amount The amount
     * @param string $costCenter The cost center code
     * @param string $internalOrderValue The internal order value
     * @param string|null $description The description
     * @param string $vatNumber The VAT number
     * @param string $date The date
     * @return array The generated document line
     */
    private function generateDocumentLine(
        string $accountType,
        string $accountCode,
        string $amount,
        string $costCenter = '',
        string $internalOrderValue = '',
        ?string $description = '',
        string $vatNumber = '',
        string $date = ''
    ): array {
        return [
            self::LINE_TYPE_DOCUMENT,
            $accountType,
            $accountCode,
            $amount,
            '',
            $costCenter,
            $internalOrderValue,
            $description ?? '',
            '',
            $vatNumber,
            '',
            '',
            '',
            $date,
            self::DEFAULT_CODE,
        ];
    }

    private function persist(array $content): string
    {
        try {
            $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::XLSX());
            $filePath = sprintf(self::TMP_DIR_PATH . '/%s', $fileName);

            // Ensure directory exists
            $dirPath = dirname($filePath);
            if (!is_dir($dirPath)) {
                if (!mkdir($dirPath, 0755, true)) {
                    throw new \RuntimeException("Failed to create directory: {$dirPath}");
                }
            }

            Excel::store(new CLGExcelExporter($content), $filePath);

            return $filePath;
        } catch (\Exception $e) {
            // Log error
            \Log::error("Failed to persist export file: " . $e->getMessage());
            throw $e;
        }
    }
}
