<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters;

use App\Document;
use App\FileSystem\Filesystem;
use App\Services\SlugGeneratorService;
use Modules\Accounting\Pub\ViewObjects\AllowanceLineViewObject;
use Modules\Accounting\Pub\ViewObjects\AllowanceViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentElementViewObject;
use Modules\Accounting\Pub\ViewObjects\DocumentSummaryViewObject;
use Modules\Accounting\Pub\ViewObjects\SettlementSummaryViewObject;
use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Dtos\ExportFileDto;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;
use Modules\IntegrationFILE\Pub\Enums\ExportFileTypeEnum;

class JWAExporter implements SingleFileExporterInterface
{
    private Filesystem $filesystem;
    private SlugGeneratorService $slugGeneratorService;

    public function __construct(Filesystem $filesystem, SlugGeneratorService $slugGeneratorService)
    {
        $this->filesystem = $filesystem;
        $this->slugGeneratorService = $slugGeneratorService;
    }

    public function processCollection(ClaimSummaryCollection $claimSummaryCollection): ExportFileDtoCollection
    {
        $exportFileDtoCollection = new ExportFileDtoCollection();

        $content = "INFO{\n\tWersja szablonu =2\n\tNazwa programu ='Sage Symfonia Handel 2011.a' Symfonia Handel 2011.a\n\tWersja_programu =108\n\tBSwersja =1.0\n\tKontrahent{\n\t\ttel1 =\n\t\tkod =JW_A_spzoo\n\t\tulica =Skorupki 11/1\n\t\tnip =675-154-43-53\n\t\tmiejscowosc =Kraków\n\t\tid =647306\n\t\tkodpocz =31-519\n\t\tnazwa =JW_A sp. z o.o.\n\t}\n}\n";

        $content = $this->addContractors($content, $claimSummaryCollection);
        $content = $this->addDocuments($content, $claimSummaryCollection);

        $relativeTmpPath = $this->persist($content);

        /** @var SettlementSummaryViewObject $claim */
        $claim = $claimSummaryCollection->first();

        $exportFileDtoCollection->push(
            new ExportFileDto(
                sprintf(
                    '%s_%s',
                    $claim->getCompany()->getCode(),
                    $claim->getSettledAt()->format('Y-m-d'),
                ),
                ExportFileTypeEnum::TXT(),
                $relativeTmpPath,
            ),
        );

        return $exportFileDtoCollection;
    }

    protected function persist(string $content): string
    {
        $this->init();

        $fileName = sprintf('%s.%s', $this->slugGeneratorService->generate(), ExportFileTypeEnum::TXT());

        $filePath = sprintf('/tmp/%s', $fileName);

        $absoluteTmpPath = storage_path(sprintf('app/%s', $filePath));

        $this->filesystem->put($absoluteTmpPath, $content);

        return $filePath;
    }

    private function init(): void
    {
        if ($this->filesystem->exists(storage_path('app/tmp')) === false) {
            $this->filesystem->makeDirectory(storage_path('app/tmp'));
        }
    }

    private function addContractors(string $content, ClaimSummaryCollection $claimSummaryCollection): string
    {
        /** @var SettlementSummaryViewObject $claim */
        foreach ($claimSummaryCollection as $claim) {
            /** @var DocumentSummaryViewObject $document */
            foreach ($claim->getDocuments() as $document) {
                $provider = $document->getProvider();
                $content .= "Kontrahent{";
                $content .= "\n\ttel1 =";
                $content .= "\n\tkod =" . $provider->getErpId();
                $content .= "\n\tosfiz =0";
                $content .= "\n\tdom =";
                $content .= "\n\trejon =";
                $content .= "\n\tznacznik =0";
                $content .= "\n\tmiejscowosc =" . $provider->getCity();
                $content .= "\n\tkodpocz =" . $provider->getPostCode();
                $content .= "\n\tnazwa =" . $provider->getName();
                $content .= "\n\tpozycja =" . $provider->getErpId();
                $content .= "\n\tautoinsert =1";
                $content .= "\n\tulica =";
                $content .= "\n\tlimitKwota =0.0";
                $content .= "\n\tnip =" . $provider->getTaxReferenceNo();
                $content .= "\n\tlokal =";
                $content .= "\n\tlimit =0";
                $content .= "\n\tid =" . $provider->getErpId();
                $content .= "\n\tkrajKod =" . $provider->getCountryCode();
                $content .= "\n\tstatusUE =0";
                $content .= "\n\taktywny =1";
                $content .= "\n}\n";
            }

            if ($claim->getAllowances()->isNotEmpty()) {
                $employee = $claim->getEmployee();
                $content .= "Kontrahent{";
                $content .= "\n\ttel1 =";
                $content .= "\n\tkod =" . $employee->getErpId();
                $content .= "\n\tosfiz =0";
                $content .= "\n\tdom =";
                $content .= "\n\trejon =";
                $content .= "\n\tznacznik =0";
                $content .= "\n\tmiejscowosc =";
                $content .= "\n\tkodpocz =";
                $content .= "\n\tnazwa =";
                $content .= "\n\tpozycja =" . $employee->getErpId();
                $content .= "\n\tautoinsert =1";
                $content .= "\n\tulica =";
                $content .= "\n\tlimitKwota =0.0";
                $content .= "\n\tnip =";
                $content .= "\n\tlokal =";
                $content .= "\n\tlimit =0";
                $content .= "\n\tid =" . $employee->getErpId();
                $content .= "\n\tkrajKod = PL";
                $content .= "\n\tstatusUE =0";
                $content .= "\n\taktywny =1";
                $content .= "\n}\n";
            }
        }

        return $content;
    }

    private function addDocuments(string $content, ClaimSummaryCollection $claimSummaryCollection): string
    {
        /** @var SettlementSummaryViewObject $claim */
        foreach ($claimSummaryCollection as $claim) {
            /** @var DocumentSummaryViewObject $document */
            foreach ($claim->getDocuments() as $document) {
                $vatDate = $document->vatDate();
                if ($vatDate) {
                    $vatDate = $vatDate->format('Y-m-d');
                }
                $settledAt = $document->getIssueDate();
                if ($settledAt) {
                    $settledAt = $settledAt->format('Y-m-d');
                }
                $receivedDate = $document->getReceivedDate();
                if ($receivedDate) {
                    $receivedDate = $receivedDate->format('Y-m-d');
                }
                $number = $document->getNumber();
                $provider = $document->getProvider();
                $erpId = $provider->getErpId();
                $content .= "Dokument{";
                $content .= "\n\tkod =" . $number;
                $content .= "\n\tdata =" . $settledAt;
                $content .= "\n\tiddokkoryg =0";
                $content .= "\n\tKwotaPozaRej =0.00000";
                $content .= "\n\tanulowany =0";
                $content .= "\n\tdatarej =" . $receivedDate;
                $content .= "\n\tdataokr =" . $vatDate;
                $content .= "\n\tFK nazwa =" . $number;
                $content .= "\n\tkhid =" . $erpId;
                $content .= "\n\trodzaj_dok =zakupu";
                $content .= "\n\tkwota =" . $document->getInstanceCurrencyGrossAmount();
                $content .= "\n\tobsluguj jak =FVZ";
                $content .= "\n\tsymbol FK =FVZP";
                $content .= "\n\trejestr_platnosci =BANK";
                $content .= "\n\topis FK =" . $document->getDescription();
                $content .= "\n\tdatasp =" . $vatDate;
                $content .= "\n\tDatawpl =" . $receivedDate;
                $content .= "\n\tforma_platnosci =przelew 0 dni";
                $content .= "\n\tplattermin =" . $settledAt;
                $content .= "\n\trozrachunek =" . $document->getInstanceCurrencyGrossAmount();

                $content .= "\n\tDane nabywcy{";
                $content .= "\n\t\tkhulica =" . $provider->getAddress();
                $content .= "\n\t\tkhkod =" . $erpId;
                $content .= "\n\t\tkhid =" . $erpId;
                $content .= "\n\t\tkhnazwa =" . $provider->getName();
                $content .= "\n\t\tkhnip =" . $provider->getTaxReferenceNo();
                $content .= "\n\t}";

                $idDlaRozliczen = 1;
                $pozycja = 0;
                /** @var DocumentElementViewObject $documentElement */
                foreach ($document->getDocumentElements() as $documentElement) {
                    $accountingAccount = $documentElement->getAccountingAccount();
                    $instanceCurrencyNetAmount = $documentElement->getInstanceCurrencyNetAmount();
                    $instanceCurrencyGrossAmount = $documentElement->getInstanceCurrencyGrossAmount();
                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $accountingAccount->getCode();
                    $content .= "\n\t\tstrona =WN";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $instanceCurrencyNetAmount;
                    $content .= "\n\t\tZapisRownolegly =0";
                    $content .= "\n\t\topis =" . $documentElement->getDescription();
                    $content .= "\n\t}";

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $document->getCreditAccountNumber() . '-K';
                    $content .= "\n\t\tstrona =MA";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $instanceCurrencyGrossAmount;
                    $content .= "\n\t\tZapisRownolegly =0";
                    $content .= "\n\t\topis =" . $documentElement->getDescription();
                    $content .= "\n\t}";

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $documentElement->getVatNumber()->getAccountingAccount()->getCode();
                    $content .= "\n\t\tstrona =WN";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $documentElement->getInstanceCurrencyVatAmount();
                    $content .= "\n\t\tZapisRownolegly =0";
                    $content .= "\n\t\topis =" . $documentElement->getDescription();
                    $content .= "\n\t}";

                    $pozycja++;

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $documentElement->getMpk()->getCode();
                    $content .= "\n\t\tstrona =WN";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $instanceCurrencyNetAmount;
                    $content .= "\n\t\tZapisRownolegly =1";
                    $content .= "\n\t\topis =" . $documentElement->getDescription();
                    $content .= "\n\t}";

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =490";
                    $content .= "\n\t\tstrona =MA";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $instanceCurrencyNetAmount;
                    $content .= "\n\t\tZapisRownolegly =1";
                    $content .= "\n\t\topis =" . $documentElement->getDescription();
                    $content .= "\n\t}";

                    $pozycja++;

                    if (Document::PAYMENT_TYPE_OWN === $document->getPaymentMethod()) {
                        $content .= "\n\tZapis{";
                        $content .= "\n\t\tPozycja =" . $pozycja;
                        $content .= "\n\t\tkonto =" . $document->getCreditAccountNumber() . '-K';
                        $content .= "\n\t\tstrona =WN";
                        $content .= "\n\t\tNumerDok =" . $number;
                        $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                        $content .= "\n\t\tkwota =" . $instanceCurrencyGrossAmount;
                        $content .= "\n\t\tZapisRownolegly =1";
                        $content .= "\n\t\topis =" . $documentElement->getDescription();
                        $content .= "\n\t}";

                        $content .= "\n\tZapis{";
                        $content .= "\n\t\tPozycja =" . $pozycja;
                        $content .= "\n\t\tkonto =234-3-P";
                        $content .= "\n\t\tstrona =MA";
                        $content .= "\n\t\tNumerDok =" . $number;
                        $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                        $content .= "\n\t\tkwota =" . $instanceCurrencyGrossAmount;
                        $content .= "\n\t\tZapisRownolegly =1";
                        $content .= "\n\t\topis =" . $documentElement->getDescription();
                        $content .= "\n\t}";

                        $pozycja++;
                    }
                }

                $content .= "\n\tTransakcja{";
                $content .= "\n\t\tUstawowe =1";
                $content .= "\n\t\tStopa =";
                $content .= "\n\t\tIdDlaRozliczen =2";
                $content .= "\n\t\tZaliczka =";
                $content .= "\n\t\tTermin =" . $settledAt;
                $content .= "\n\t}";

                $vatCode = $documentElement->getVatNumber()->getCode();
                $content .= "\n\tRejestr{";
                $content .= "\n\t\tstawka =" . $vatCode;
                $content .= "\n\t\tABC =1";
                $content .= "\n\t\tnetto1 =" . $document->getInstanceCurrencyNetAmount();
                $content .= "\n\t\tRodzaj =2000";
                $content .= "\n\t\tnetto =" . $document->getInstanceCurrencyNetAmount();;
                $content .= "\n\t\tNazwa =Zakup VAT";
                $content .= "\n\t\tvat =" . $document->getInstanceCurrencyVatAmount();
                $content .= "\n\t\tstawka1 =" . $vatCode;
                $content .= "\n\t\tokres =" . $receivedDate;
                $content .= "\n\t\tsumanetto =" . $document->getInstanceCurrencyNetAmount();
                $content .= "\n\t\tUE =0";
                $content .= "\n\t\tbrutto =" . $document->getInstanceCurrencyGrossAmount();
                $content .= "\n\t\tsumavat =" . $document->getInstanceCurrencyVatAmount();
                $content .= "\n\t\tbrutto1 =" . $document->getInstanceCurrencyGrossAmount();
                $content .= "\n\t\tSkrot =Zakup VAT";
                $content .= "\n\t\tvat1 =" . $document->getInstanceCurrencyVatAmount();
                $content .= "\n\t}";

                $content .= "\n}\n";
            }

            /** @var AllowanceViewObject $allowance */
            foreach ($claim->getAllowances() as $allowance) {
                $idDlaRozliczen = 1;
                $pozycja = 0;
                /** @var AllowanceLineViewObject $allowanceLine */
                foreach ($allowance->getAllowanceLines() as $allowanceLine) {
                    $employee = $claim->getEmployee();
                    $number = $claim->getNumber();
                    $settledAt = $claim->getSettledAt()->format('Y-m-d');
                    $amount = $allowanceLine->getAmount();
                    $purpose = $claim->getPurpose();
                    $content .= "Dokument{";
                    $content .= "\n\tkod =" . $number;
                    $content .= "\n\tdata =" . $settledAt;
                    $content .= "\n\tiddokkoryg =0";
                    $content .= "\n\tKwotaPozaRej =0.00000";
                    $content .= "\n\tanulowany =0";
                    $content .= "\n\tdatarej =" . $settledAt;
                    $content .= "\n\tdataokr =" . $settledAt;
                    $content .= "\n\tFK nazwa =" . $number;
                    $content .= "\n\tkhid =" . $erpId;
                    $content .= "\n\trodzaj_dok =zakupu";
                    $content .= "\n\tkwota =" . $amount;
                    $content .= "\n\tobsluguj jak =DEL";
                    $content .= "\n\tsymbol FK =DEL";
                    $content .= "\n\trejestr_platnosci =BANK";
                    $content .= "\n\topis FK =" . $purpose;
                    $content .= "\n\tdatasp =" . $settledAt;
                    $content .= "\n\tDatawpl =" . $settledAt;
                    $content .= "\n\tforma_platnosci =przelew 0 dni";
                    $content .= "\n\tplattermin =" . $settledAt;
                    $content .= "\n\trozrachunek =" . $amount;

                    $content .= "\n\tDane nabywcy{";
                    $content .= "\n\t\tkhkod =" . $employee->getErpId();
                    $content .= "\n\t\tkhid =" . $employee->getErpId();
                    $content .= "\n\t\tkhnazwa =" . $employee->getFullName();
                    $content .= "\n\t}";

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $allowanceLine->getAccountingAccount()->getCode();
                    $content .= "\n\t\tstrona =WN";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $amount;
                    $content .= "\n\t\tZapisRownolegly =1";
                    $content .= "\n\t\topis =" . $purpose;
                    $content .= "\n\t}";

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $allowance->getAccountingAccount()->getCode() . '-' . $number;
                    $content .= "\n\t\tstrona =MA";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $amount;
                    $content .= "\n\t\tZapisRownolegly =1";
                    $content .= "\n\t\topis =" . $purpose;
                    $content .= "\n\t}";

                    $pozycja++;

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =" . $documentElement->getMpk()->getCode();
                    $content .= "\n\t\tstrona =WN";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $amount;
                    $content .= "\n\t\tZapisRownolegly =1";
                    $content .= "\n\t\topis =" . $purpose;
                    $content .= "\n\t}";

                    $content .= "\n\tZapis{";
                    $content .= "\n\t\tPozycja =" . $pozycja;
                    $content .= "\n\t\tkonto =490";
                    $content .= "\n\t\tstrona =MA";
                    $content .= "\n\t\tNumerDok =" . $number;
                    $content .= "\n\t\tIdDlaRozliczen =" . $idDlaRozliczen++;
                    $content .= "\n\t\tkwota =" . $amount;
                    $content .= "\n\t\tZapisRownolegly =1";
                    $content .= "\n\t\topis =" . $purpose;
                    $content .= "\n\t}";

                    $pozycja++;

                    $content .= "\n\tTransakcja{";
                    $content .= "\n\t\tUstawowe =1";
                    $content .= "\n\t\tStopa =";
                    $content .= "\n\t\tIdDlaRozliczen =2";
                    $content .= "\n\t\tZaliczka =";
                    $content .= "\n\t\tTermin =" . $settledAt;
                    $content .= "\n\t}";

                    $content .= "\n}\n";
                }
            }
        }

        return $content;
    }
}