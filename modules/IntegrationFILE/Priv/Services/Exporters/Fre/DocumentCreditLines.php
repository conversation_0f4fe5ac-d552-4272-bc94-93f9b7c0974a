<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters\Fre;

use App\Vendors\Math;
use Illuminate\Support\Collection;

class DocumentCreditLines extends Collection
{
    public function getGroupedByCreditAccountNumber(): DocumentCreditLines
    {
        return $this->groupBy(function ($item) {
            return $item->getCreditAccountNumber();
        })->map(function ($group) {
            $sum = $group->reduce(function ($carry, $item) {
                return Math::add($carry, $item->getInstanceCurrencyGrossAmount());
            }, Math::ZERO_AMOUNT);

            return new DocumentCreditLineGroup($sum, $group, $group->first()->getCreditAccountNumber());
        });
    }
}
