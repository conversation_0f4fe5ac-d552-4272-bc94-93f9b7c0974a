<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters\Processors;

use Modules\IntegrationFILE\Priv\Dtos\Collections\ExportFileDtoCollection;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\ExporterInterface;
use Modules\IntegrationFILE\Priv\Interfaces\Exporters\SingleFileExporterInterface;
use Modules\IntegrationFILE\Pub\Dtos\Collections\ClaimSummaryCollection;

class SingleFileProcessor implements ProcessorInterface
{
    /**
     * @param SingleFileExporterInterface $exporter
     * @param ClaimSummaryCollection $claimSummaryCollection
     * @return ExportFileDtoCollection
     */
    public function process(
        ExporterInterface $exporter,
        ClaimSummaryCollection $claimSummaryCollection
    ): ExportFileDtoCollection {
        return $exporter->processCollection($claimSummaryCollection);
    }

    public function supports(ExporterInterface $exporter): bool
    {
        return $exporter instanceof SingleFileExporterInterface;
    }
}
