<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Services\Exporters\MND;

class Line
{
    public const COL_REQ_NUMBER = 'Id wniosku';
    public const COL_CLASS = 'Class';
    public const COL_DEFINICJA_SYMBOL = 'Definicja:Symbol';
    public const COL_NUMER_DOKUMENTU = 'NumerDokumentu';
    public const COL_NR_DOKUMENTU_K = 'NrDokumentuK';
    public const COL_DATA_WPLYWU = 'DataWplywu';
    public const COL_DATA_DOKUMENTU = 'DataDokumentu';
    public const COL_DATA_OPERACJI = 'DataOperacji';
    public const COL_OPIS = 'Opis';
    public const COL_PODMIOT_CLASS = 'Podmiot:Class';
    public const COL_PODMIOT_KOD = 'Podmiot:Kod';
    public const COL_PLATNOSCI_CLASS = 'Platnosci:Class';
    public const COL_PLATNOSCI_SPOSOB_ZAPLATY_NAZWA = 'Platnosci:SposobZaplaty:Nazwa';
    public const COL_PLATNOSCI_TERMIN = 'Platnosci:Termin';
    public const COL_PLATNOSCI_KWOTA = 'Platnosci:Kwota';
    public const COL_PLATNOSCI_KWOTA_KSIEGI = 'Platnosci:KwotaKsiegi';
    public const COL_PLATNOSCI_OPIS = 'Platnosci:Opis';
    public const COL_NAG_EWIDENCJI_VAT_ELEMENTY_CLASS = 'NagEwidencjiVAT.Elementy:Class';
    public const COL_NAG_EWIDENCJI_VAT_ELEMENTY_DEFINICJA_STAWKI_KOD = 'NagEwidencjiVAT.Elementy:DefinicjaStawki:Kod';
    public const COL_NAG_EWIDENCJI_VAT_ELEMENTY_NETTO = 'NagEwidencjiVAT.Elementy:Netto';
    public const COL_NAG_EWIDENCJI_VAT_ELEMENTY_VAT = 'NagEwidencjiVAT.Elementy:VAT';
    public const COL_NAG_EWIDENCJI_VAT_ELEMENTY_ODLICZENIA = 'NagEwidencjiVAT.Elementy:Odliczenia';
    public const COL_NAG_EWIDENCJI_VAT_ELEMENTY_RODZAJ = 'NagEwidencjiVAT.Elementy:Rodzaj';
    public const COL_OPIS_ANALITYCZNY_CLASS = 'OpisAnalityczny:Class';
    public const COL_OPIS_ANALITYCZNY_WYMIAR = 'OpisAnalityczny:Wymiar';
    public const COL_OPIS_ANALITYCZNY_SYMBOL = 'OpisAnalityczny:Symbol';
    public const COL_OPIS_ANALITYCZNY_KWOTA = 'OpisAnalityczny:Kwota';
    public const COL_OPIS_ANALITYCZNY_OPIS = 'OpisAnalityczny:Opis';
    public const COL_OPIS_ANALITYCZNY_FEATURES_KOD_PODATKU = 'OpisAnalityczny:Features.Kod podatku';
    public const COL_OPIS_ANALITYCZNY_FEATURES_KOD_RAPORTU_ID = 'OpisAnalityczny:Features.Kod raportu:ID';
    public const COL_OPIS_ANALITYCZNY_FEATURES_RODZAJ_KOSZTU_ID = 'OpisAnalityczny:Features.Rodzaj_kosztu:ID';
    public const COL_OPIS_ANALITYCZNY_FEATURES_RODZAJ_KOSZTU_SYMBOL = 'OpisAnalityczny:Features.Rodzaj_kosztu:Symbol';
    public const COL_OPIS_ANALITYCZNY_FEATURES_DZIAL_ID = 'OpisAnalityczny:Features.Dzial:ID';
    public const COL_OPIS_ANALITYCZNY_FEATURES_DZIAL_SYMBOL = 'OpisAnalityczny:Features.Dzial:Symbol';
    public const COL_OPIS_ANALITYCZNY_FEATURES_RAPORT_ID = 'OpisAnalityczny:Features.Raport:ID';
    public const COL_OPIS_ANALITYCZNY_CENTRUM_KOSZTOW_NAZWA = 'OpisAnalityczny:CentrumKosztow:Nazwa';
    public const COL_FEATURES_NUMER_REZERWACJI = 'Features.Numer rezerwacji';

    public const DEFAULT_CLASS = 'ZakupEwidencja';
    public const DEFAULT_ENTITY_CLASS = 'Kontrahent';
    public const DEFAULT_VAT_REGISTRY_HEADER_ELEMENTS_CLASS = 'ElemEwidencjiVATZakup';
    public const DEFAULT_ANALYTICAL_DESCRIPTION_DIMENSION = 'WN';
    public const DEFAULT_ANALYTICAL_DESCRIPTION_FEATURES_REPORT_CODE_ID = '';
    public const DEFAULT_ANALYTICAL_DESCRIPTION_FEATURES_COST_TYPE_SYMBOL = 'WA2';
    public const DEFAULT_ANALYTICAL_DESCRIPTION_FEATURES_DEPARTMENT_SYMBOL = '';

    private string $requestNumber;
    private ?string $class = self::DEFAULT_CLASS;
    private ?string $definitionSymbol = null;
    private ?string $documentNumber = null;
    private ?string $documentNumberK = null;
    private ?string $receiptDate = null;
    private ?string $documentDate = null;
    private ?string $operationDate = null;
    private ?string $description = null;
    private ?string $entityClass = self::DEFAULT_ENTITY_CLASS;
    private ?string $entityCode = null;
    private ?string $paymentsClass = null;
    private ?string $paymentsMethodName = null;
    private ?string $paymentsDueDate = null;
    private ?string $paymentsAmount = null;
    private ?string $paymentsBookAmount = null;
    private ?string $paymentsDescription = null;
    private ?string $vatRegistryHeaderElementsClass = self::DEFAULT_VAT_REGISTRY_HEADER_ELEMENTS_CLASS;
    private ?string $vatRegistryHeaderElementsRateDefinitionCode = null;
    private ?string $vatRegistryHeaderElementsNet = null;
    private ?string $vatRegistryHeaderElementsVat = null;
    private ?string $vatRegistryHeaderElementsDeductions = null;
    private ?string $vatRegistryHeaderElementsType = null;
    private ?string $analyticalDescriptionClass = null;
    private ?string $analyticalDescriptionDimension = self::DEFAULT_ANALYTICAL_DESCRIPTION_DIMENSION;
    private ?string $analyticalDescriptionSymbol = null;
    private ?string $analyticalDescriptionAmount = null;
    private ?string $analyticalDescriptionDescription = null;
    private ?string $analyticalDescriptionFeaturesTaxCode = null;
    private ?string $analyticalDescriptionFeaturesReportCodeId = self::DEFAULT_ANALYTICAL_DESCRIPTION_FEATURES_REPORT_CODE_ID;
    private ?string $analyticalDescriptionFeaturesCostTypeId = null;
    private ?string $analyticalDescriptionFeaturesCostTypeSymbol = self::DEFAULT_ANALYTICAL_DESCRIPTION_FEATURES_COST_TYPE_SYMBOL;
    private ?string $analyticalDescriptionFeaturesDepartmentId = null;
    private ?string $analyticalDescriptionFeaturesDepartmentSymbol = self::DEFAULT_ANALYTICAL_DESCRIPTION_FEATURES_DEPARTMENT_SYMBOL;
    private ?string $analyticalDescriptionFeaturesReportId = null;
    private ?string $analyticalDescriptionCostCenterName = null;
    private ?string $featuresReservationNumber = null;

    public function __construct(
        string $requestNumber
    ) {
        $this->requestNumber = $requestNumber;
    }

    public function setClass(?string $class): self
    {
        $this->class = $class;
        return $this;
    }

    public function setDefinitionSymbol(?string $definitionSymbol): self
    {
        $this->definitionSymbol = $definitionSymbol;
        return $this;
    }

    public function setDocumentNumber(?string $documentNumber): self
    {
        $this->documentNumber = $documentNumber;
        return $this;
    }

    public function setDocumentNumberK(?string $documentNumberK): self
    {
        $this->documentNumberK = $documentNumberK;
        return $this;
    }

    public function setReceiptDate(?string $receiptDate): self
    {
        $this->receiptDate = $receiptDate;
        return $this;
    }

    public function setDocumentDate(?string $documentDate): self
    {
        $this->documentDate = $documentDate;
        return $this;
    }

    public function setOperationDate(?string $operationDate): self
    {
        $this->operationDate = $operationDate;
        return $this;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function setEntityClass(?string $entityClass): self
    {
        $this->entityClass = $entityClass;
        return $this;
    }

    public function setEntityCode(?string $entityCode): self
    {
        $this->entityCode = $entityCode;
        return $this;
    }

    public function setPaymentsClass(?string $paymentsClass): self
    {
        $this->paymentsClass = $paymentsClass;
        return $this;
    }

    public function setPaymentsMethodName(?string $paymentsMethodName): self
    {
        $this->paymentsMethodName = $paymentsMethodName;
        return $this;
    }

    public function setPaymentsDueDate(?string $paymentsDueDate): self
    {
        $this->paymentsDueDate = $paymentsDueDate;
        return $this;
    }

    public function setPaymentsAmount(?string $paymentsAmount): self
    {
        $this->paymentsAmount = $paymentsAmount;
        return $this;
    }

    public function setPaymentsBookAmount(?string $paymentsBookAmount): self
    {
        $this->paymentsBookAmount = $paymentsBookAmount;
        return $this;
    }

    public function setPaymentsDescription(?string $paymentsDescription): self
    {
        $this->paymentsDescription = $paymentsDescription;
        return $this;
    }

    public function setVatRegistryHeaderElementsClass(?string $vatRegistryHeaderElementsClass): self
    {
        $this->vatRegistryHeaderElementsClass = $vatRegistryHeaderElementsClass;
        return $this;
    }

    public function setVatRegistryHeaderElementsRateDefinitionCode(?string $vatRegistryHeaderElementsRateDefinitionCode
    ): self {
        $this->vatRegistryHeaderElementsRateDefinitionCode = $vatRegistryHeaderElementsRateDefinitionCode;
        return $this;
    }

    public function setVatRegistryHeaderElementsNet(?string $vatRegistryHeaderElementsNet): self
    {
        $this->vatRegistryHeaderElementsNet = $vatRegistryHeaderElementsNet;
        return $this;
    }

    public function setVatRegistryHeaderElementsVat(?string $vatRegistryHeaderElementsVat): self
    {
        $this->vatRegistryHeaderElementsVat = $vatRegistryHeaderElementsVat;
        return $this;
    }

    public function setVatRegistryHeaderElementsDeductions(?string $vatRegistryHeaderElementsDeductions): self
    {
        $this->vatRegistryHeaderElementsDeductions = $vatRegistryHeaderElementsDeductions;
        return $this;
    }

    public function setVatRegistryHeaderElementsType(?string $vatRegistryHeaderElementsType): self
    {
        $this->vatRegistryHeaderElementsType = $vatRegistryHeaderElementsType;
        return $this;
    }

    public function setAnalyticalDescriptionClass(?string $analyticalDescriptionClass): self
    {
        $this->analyticalDescriptionClass = $analyticalDescriptionClass;
        return $this;
    }

    public function setAnalyticalDescriptionDimension(?string $analyticalDescriptionDimension): self
    {
        $this->analyticalDescriptionDimension = $analyticalDescriptionDimension;
        return $this;
    }

    public function setAnalyticalDescriptionSymbol(?string $analyticalDescriptionSymbol): self
    {
        $this->analyticalDescriptionSymbol = $analyticalDescriptionSymbol;
        return $this;
    }

    public function setAnalyticalDescriptionAmount(?string $analyticalDescriptionAmount): self
    {
        $this->analyticalDescriptionAmount = $analyticalDescriptionAmount;
        return $this;
    }

    public function setAnalyticalDescriptionDescription(?string $analyticalDescriptionDescription): self
    {
        $this->analyticalDescriptionDescription = $analyticalDescriptionDescription;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesTaxCode(?string $analyticalDescriptionFeaturesTaxCode): self
    {
        $this->analyticalDescriptionFeaturesTaxCode = $analyticalDescriptionFeaturesTaxCode;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesReportCodeId(?string $analyticalDescriptionFeaturesReportCodeId
    ): self {
        $this->analyticalDescriptionFeaturesReportCodeId = $analyticalDescriptionFeaturesReportCodeId;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesCostTypeId(?string $analyticalDescriptionFeaturesCostTypeId): self
    {
        $this->analyticalDescriptionFeaturesCostTypeId = $analyticalDescriptionFeaturesCostTypeId;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesCostTypeSymbol(?string $analyticalDescriptionFeaturesCostTypeSymbol
    ): self {
        $this->analyticalDescriptionFeaturesCostTypeSymbol = $analyticalDescriptionFeaturesCostTypeSymbol;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesDepartmentId(?string $analyticalDescriptionFeaturesDepartmentId
    ): self {
        $this->analyticalDescriptionFeaturesDepartmentId = $analyticalDescriptionFeaturesDepartmentId;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesDepartmentSymbol(
        ?string $analyticalDescriptionFeaturesDepartmentSymbol
    ): self {
        $this->analyticalDescriptionFeaturesDepartmentSymbol = $analyticalDescriptionFeaturesDepartmentSymbol;
        return $this;
    }

    public function setAnalyticalDescriptionFeaturesReportId(?string $analyticalDescriptionFeaturesReportId): self
    {
        $this->analyticalDescriptionFeaturesReportId = $analyticalDescriptionFeaturesReportId;
        return $this;
    }

    public function setAnalyticalDescriptionCostCenterName(?string $analyticalDescriptionCostCenterName): self
    {
        $this->analyticalDescriptionCostCenterName = $analyticalDescriptionCostCenterName;
        return $this;
    }

    public function setFeaturesReservationNumber(?string $featuresReservationNumber): self
    {
        $this->featuresReservationNumber = $featuresReservationNumber;
        return $this;
    }

    public function toArray(): array
    {
        $result = [
            self::COL_REQ_NUMBER => $this->requestNumber,
            self::COL_CLASS => $this->class,
            self::COL_DEFINICJA_SYMBOL => $this->definitionSymbol,
            self::COL_NUMER_DOKUMENTU => $this->documentNumber,
            self::COL_NR_DOKUMENTU_K => $this->documentNumberK,
            self::COL_DATA_WPLYWU => $this->receiptDate,
            self::COL_DATA_DOKUMENTU => $this->documentDate,
            self::COL_DATA_OPERACJI => $this->operationDate,
            self::COL_OPIS => $this->description,
            self::COL_PODMIOT_CLASS => $this->entityClass,
            self::COL_PODMIOT_KOD => $this->entityCode,
            self::COL_PLATNOSCI_CLASS => $this->paymentsClass,
            self::COL_PLATNOSCI_SPOSOB_ZAPLATY_NAZWA => $this->paymentsMethodName,
            self::COL_PLATNOSCI_TERMIN => $this->paymentsDueDate,
            self::COL_PLATNOSCI_KWOTA => $this->paymentsAmount,
            self::COL_PLATNOSCI_KWOTA_KSIEGI => $this->paymentsBookAmount,
            self::COL_PLATNOSCI_OPIS => $this->paymentsDescription,
            self::COL_NAG_EWIDENCJI_VAT_ELEMENTY_CLASS => $this->vatRegistryHeaderElementsClass,
            self::COL_NAG_EWIDENCJI_VAT_ELEMENTY_DEFINICJA_STAWKI_KOD => $this->vatRegistryHeaderElementsRateDefinitionCode,
            self::COL_NAG_EWIDENCJI_VAT_ELEMENTY_NETTO => $this->vatRegistryHeaderElementsNet,
            self::COL_NAG_EWIDENCJI_VAT_ELEMENTY_VAT => $this->vatRegistryHeaderElementsVat,
            self::COL_NAG_EWIDENCJI_VAT_ELEMENTY_ODLICZENIA => $this->vatRegistryHeaderElementsDeductions,
            self::COL_NAG_EWIDENCJI_VAT_ELEMENTY_RODZAJ => $this->vatRegistryHeaderElementsType,
            self::COL_OPIS_ANALITYCZNY_CLASS => $this->analyticalDescriptionClass,
            self::COL_OPIS_ANALITYCZNY_WYMIAR => $this->analyticalDescriptionDimension,
            self::COL_OPIS_ANALITYCZNY_SYMBOL => $this->analyticalDescriptionSymbol,
            self::COL_OPIS_ANALITYCZNY_KWOTA => $this->analyticalDescriptionAmount,
            self::COL_OPIS_ANALITYCZNY_OPIS => $this->analyticalDescriptionDescription,
            self::COL_OPIS_ANALITYCZNY_FEATURES_KOD_PODATKU => $this->analyticalDescriptionFeaturesTaxCode,
            self::COL_OPIS_ANALITYCZNY_FEATURES_KOD_RAPORTU_ID => $this->analyticalDescriptionFeaturesReportCodeId,
            self::COL_OPIS_ANALITYCZNY_FEATURES_RODZAJ_KOSZTU_ID => $this->analyticalDescriptionFeaturesCostTypeId,
            self::COL_OPIS_ANALITYCZNY_FEATURES_RODZAJ_KOSZTU_SYMBOL => $this->analyticalDescriptionFeaturesCostTypeSymbol,
            self::COL_OPIS_ANALITYCZNY_FEATURES_DZIAL_ID => $this->analyticalDescriptionFeaturesDepartmentId,
            self::COL_OPIS_ANALITYCZNY_FEATURES_DZIAL_SYMBOL => $this->analyticalDescriptionFeaturesDepartmentSymbol,
            self::COL_OPIS_ANALITYCZNY_FEATURES_RAPORT_ID => $this->analyticalDescriptionFeaturesReportId,
            self::COL_OPIS_ANALITYCZNY_CENTRUM_KOSZTOW_NAZWA => $this->analyticalDescriptionCostCenterName,
            self::COL_FEATURES_NUMER_REZERWACJI => $this->featuresReservationNumber,
        ];

        return $result;
    }
}
