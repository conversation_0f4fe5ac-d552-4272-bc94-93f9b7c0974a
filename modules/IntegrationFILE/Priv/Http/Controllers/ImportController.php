<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Responses\Response2;
use App\Http\Responses\Response2Paginated;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Common\Dtos\OffsetPageDto;
use Modules\IntegrationFILE\Priv\Entities\Import;
use Modules\IntegrationFILE\Priv\Factories\InitializeImportDtoFactory;
use Modules\IntegrationFILE\Priv\Http\Requests\ImportCreateRequest;
use Modules\IntegrationFILE\Priv\Http\Responses\ImportIndexResponse;
use Modules\IntegrationFILE\Priv\Http\Responses\ImportItemResponse;
use Modules\IntegrationFILE\Priv\Repositories\ImportRepository;
use Modules\IntegrationFILE\Priv\Services\ImportService;

class ImportController extends Controller
{
    private ImportRepository $importRepository;
    private InitializeImportDtoFactory $initializeImportDtoFactory;
    private ImportService $importService;

    public function __construct(
        ImportRepository $importRepository,
        InitializeImportDtoFactory $initializeImportDtoFactory,
        ImportService $importService
    ) {
        $this->importRepository = $importRepository;
        $this->initializeImportDtoFactory = $initializeImportDtoFactory;
        $this->importService = $importService;
    }

    public function index(Request $request): Response2Paginated
    {
        return ImportIndexResponse::collection(
            $this->importRepository->getImportIndex(
                $request->user(),
                OffsetPageDto::fromRequestAndModelClass(get_class($this->importRepository->getModel())),
            ),
        );
    }

    public function store(ImportCreateRequest $request): Response2
    {
        $import = $this->importService->initialize($this->initializeImportDtoFactory->create($request));

        if ($import !== null) {
            return ImportItemResponse::item($import)->addSuccess(trans('import.import-scheduled-successfully'));
        }

        return Response2::item()->addError(trans('import.import-scheduling-error'));
    }

    public function errorReport(string $slug): void
    {
        $import = $this->importRepository->findBySlug($slug);

        if ($import !== null) {
            $fileContent = file_get_contents(
                storage_path(sprintf('app/%s/%s', $import->file_path, $import->error_file_name)),
            );
            $outputName = sprintf('import_errors_%s.csv', $import->created_at->format('Y_m_d_H_i_s'));

            $this->responseAsBlob($fileContent, $outputName, 'text/csv');
        }

        abort(Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));
    }

    public function downloadExport(string $slug): void
    {
        /** @var Import $export */
        $export = $this->importRepository->findBySlug($slug);

        if ($export !== null) {
            $fileContent = file_get_contents(
                $export->getFilePath(),
            );

            $this->responseAsBlob($fileContent, $export->file_name, 'application/xlsx');
        }

        abort(Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));
    }
}
