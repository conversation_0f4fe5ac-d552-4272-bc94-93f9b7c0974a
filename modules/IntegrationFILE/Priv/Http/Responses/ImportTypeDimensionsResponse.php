<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Http\Responses;

use App\Http\Responses\Response2;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\IntegrationFILE\Pub\Enums\ImportTypeEnum;

class ImportTypeDimensionsResponse extends Response2
{
    protected function transform(AccountDimension $dimension): array
    {
        return [
            'label' => $dimension->name,
            'value' => $dimension->code,
            'moreOptions' => null,
        ];
    }
}
