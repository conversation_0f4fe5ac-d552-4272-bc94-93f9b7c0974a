<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Http\Responses;

use App\Http\Responses\Response2;
use Carbon\Carbon;
use Modules\IntegrationFILE\Priv\Entities\Import;

class ExportItemResponse extends Response2
{
    protected function transform(Import $export): array
    {
        $response = [
            'slug' => (string)$export->slug,
            'startDate' => $export->start_date instanceof Carbon ? $export->start_date->toDateTimeString() : null,
            'endDate' => $export->end_date instanceof Carbon ? $export->end_date->toDateTimeString() : null,
            'type' => (string)$export->type,
            'status' => (string)$export->status,
            'fileName' => $export->file_name,
            'createdAt' => $export->created_at->toDateTimeString(),
            'user' => [
                'fullName' => $export->user->full_name,
            ],
        ];

        if (empty($export->company) === false) {
            $response['company'] = [
                'name' => $export->company->name,
            ];
        }

        return $response;
    }
}