<?php

declare(strict_types=1);

namespace Modules\IntegrationFILE\Priv\Http\Responses;

use App\Http\Responses\Response2Paginated;

class ImportIndexResponse extends Response2Paginated
{
    protected function transform(\stdClass $import): array
    {
        $response = [
            'slug' => $import->slug,
            'startDate' => $import->start_date,
            'endDate' => $import->end_date,
            'type' => $import->type,
            'status' => $import->status,
            'fileName' => $import->file_name,
            'createdAt' => $import->created_at,
            'progress' => $import->progress,
            'isExport' => $import->is_export,
            'user' => [
                'fullName' => $import->added_by_user_full_name,
            ],
        ];

        if (empty($import->company_name) === false) {
            $response['company'] = [
                'name' => $import->company_name,
            ];
        }

        return $response;
    }
}