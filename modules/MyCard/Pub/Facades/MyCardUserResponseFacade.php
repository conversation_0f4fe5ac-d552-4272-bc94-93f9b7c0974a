<?php

declare(strict_types=1);

namespace Modules\MyCard\Pub\Facades;

use App\Permission;
use App\User;
use Modules\MyCard\Priv\Repositories\CardholderRepository;

class MyCardUserResponseFacade
{
    public const STATUS_NONE = 'my-card.status.none';
    public const STATUS_INVITED = 'my-card.status.invited';
    public const STATUS_CARD_ISSUED = 'my-card.status.card_issued';
    public const STATUS_DONE = 'my-card.status.done';

    private CardholderRepository $cardholderRepository;

    public function __construct(
        CardholderRepository $cardholderRepository
    ) {
        $this->cardholderRepository = $cardholderRepository;
    }

    public function getDataForUser(int $userId): array
    {
        $cardholder = $this->cardholderRepository->findByUserId($userId);

        $response = [
            'status' => $this->statusTranslated(self::STATUS_NONE),
            'cards' => [],
        ];

        if (null === $cardholder) {
            return $response;
        }

        $response['status'] = $this->statusTranslated(self::STATUS_INVITED);
        if (empty($cardholder->cardholder_id)) {
            return $response;
        }

        if (!$cardholder->hasCards()) {
            return $response;
        }

        $response['status'] = $this->statusTranslated(self::STATUS_CARD_ISSUED);

        foreach ($cardholder->getCards() as $card) {
            if (!empty($card->card_number)) {
                $response['status'] = $this->statusTranslated(self::STATUS_DONE);
                $response['cards'][] = sprintf('%s', $card->card_number);
            }
        }

        return $response;
    }

    public function prepareLinks(User $user): array
    {
        $company = $user->company;
        $tenantId = $company->mycard_tenant_id;
        $cardholder = $this->cardholderRepository->findByUserId($user->id);
        $links = [];

        if ($cardholder && $cardholder->cardholder_id) {
            $links['transactions'] = $this->transactions($cardholder->cardholder_id, $tenantId);

            if ($cardholder->hasCards()) {
                $links['statements'] = $this->statements($cardholder->cardholder_id, $tenantId);
                $links['cards'] = $this->cards($cardholder->cardholder_id, $tenantId);
            }
        }

        if ($user->hasAbility(Permission::MYCARD_ACCOUNT_STATEMENTS)) {
            $links['exports'] = $this->exports($tenantId);
            $links['accounts'] = $this->accounts($tenantId);
        }

        return $links;
    }

    private function statusTranslated(string $status): string
    {
        return trans($status);
    }

    private function transactions(string $cardholderId, string $tenantId): array
    {
        $href = MyCardProxy::generateSecuredLink(sprintf('owners/%s/transactions', $cardholderId));

        return [
            'name' => 'transactions',
            'href' => $href,
            'method' => 'GET',
            'headers' => [
                'Tenant' => $tenantId,
            ],
            'body' => [],
        ];
    }

    private function exports(string $tenantId): array
    {
        $href = MyCardProxy::generateSecuredLink('statements/export');

        return [
            'name' => 'exports',
            'href' => $href,
            'method' => 'GET',
            'headers' => [
                'Tenant' => $tenantId,
            ],
            'body' => [],
        ];
    }

    private function statements(string $cardholderId, string $tenantId): array
    {
        $href = MyCardProxy::generateSecuredLink(sprintf('owners/%s/statements', $cardholderId));

        return [
            'name' => 'statements',
            'href' => $href,
            'method' => 'GET',
            'headers' => [
                'Tenant' => $tenantId,
            ],
            'body' => [],
        ];
    }

    private function accounts(string $tenantId): array
    {
        $href = MyCardProxy::generateSecuredLink('accounts');

        return [
            'name' => 'accounts',
            'href' => $href,
            'method' => 'GET',
            'headers' => [
                'Tenant' => $tenantId,
            ],
            'body' => [],
        ];
    }

    private function cards(string $cardholderId, string $tenantId): array
    {
        $href = MyCardProxy::generateSecuredLink(sprintf('cards/%s', $cardholderId));

        return [
            'name' => 'cards',
            'href' => $href,
            'method' => 'GET',
            'headers' => [
                'Tenant' => $tenantId,
            ],
            'body' => [],
        ];
    }
}
