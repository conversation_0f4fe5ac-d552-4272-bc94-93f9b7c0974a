<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\V1\Resources;

use Modules\Accounting\Pub\ViewObjects\VatSummaryViewObject;

/**
 * @OA\Schema(
 *     title="VatSummaryResource",
 *     description="VAT Summary by tax rate",
 *     @OA\Xml(
 *         name="VatSummaryResource"
 *     ),
 *     nullable=true
 * )
 */
class VatSummaryResource extends AbstractResource
{

    /**
     *
     * @OA\Property(
     *     property="vat_code",
     *     ref="#/components/schemas/VatResource",
     * )
     *
     */
    protected VatResource $vat;

    /**
     * @OA\Property(
     *  property="accounting_account_id",
     *  type="string",
     *  example="5a3bd9f9-9d0e-4533-9d73-d11b96d5ccd1"
     * )
     *
     * @var string
     */
    protected $accountingAccountId;

    /**
     * @OA\Property(
     *     title="net amount in document currency",
     *     property="foreign_currency_net_amount",
     *     example="10.0000",
     *     description="net amount in document currency"
     * )
     *
     */
    protected string $foreignCurrencyNetAmount;

    /**
     * @OA\Property(
     *     title="vat tax amount in document currency",
     *     property="foreign_currency_vat_amount",
     *     example="2.3000",
     *     description="vat tax amount in document currency"
     * )
     *
     * @var string
     */
    protected string $foreignCurrencyVatAmount;

    /**
     * @OA\Property(
     *     title="gross amount in document currency",
     *     property="foreign_currency_gross_amount",
     *     example="12.3000",
     *     description="gross amount in document currency"
     * )
     *
     * @var string
     */
    protected $foreignCurrencyGrossAmount;

    /**
     * @OA\Property(
     *     title="net amount in instance currency",
     *     property="local_currency_net_amount",
     *     example="27.7000",
     *     description="net amount in instance currency"
     * )
     *
     */
    protected string $localCurrencyNetAmount;

    /**
     * @OA\Property(
     *     title="vat tax amount in instance currency",
     *     property="local_currency_vat_amount",
     *     example="5.18",
     *     description="vat tax amount in instance currency"
     * )
     *
     * @var string
     */
    protected string $localCurrencyVatAmount;

    /**
     * @OA\Property(
     *     title="gross amount in instance currency",
     *     property="local_currency_gross_amount",
     *     example="22.52",
     *     description="gross amount in instance currency"
     * )
     *
     * @var string
     */
    protected string $localCurrencyGrossAmount;

     /**
     * @OA\Property(
     *  property="reverse_accounting_account_id",
     *  type="string",
     *  example="5a3bd9f9-9d0e-4533-9d73-d11b96d5ccd1"
     * )
     *
     * @var string|null
     */
    protected ?string $reverseAccountingAccountId;

    /**
     * @OA\Property(
     *  property="local_currency_reverse_vat_amount",
     *  type="string",
     *  example="33.45"
     * )
     *
     * @var string|null
     */
    private ?string $localCurrencyReverseVatAmount;
    
    /**
     * @OA\Property(
     *  property="foreign_currency_reverse_vat_amount",
     *  type="string",
     *  example="10.45",
     *  description="reverse vat amount in document currency"
     * )
     *
     * @var string|null
     */
    private ?string $foreignCurrencyReverseVatAmount;

    public function getIdentifierValue()
    {
        // TODO: Implement getIdentifierValue() method.
    }

    /**
     * @param VatSummaryViewObject $entity
     */
    public static function map($entity): VatSummaryResource
    {
        $reverseAccountingAccount = $entity->getVatNumber()->getReverseAccountingAccount();
        $resource = new static();
        $resource->vat = VatResource::map($entity->getVatNumber());
        $resource->accountingAccountId = $entity->getAccountingAccount()->getSlug();
        $resource->reverseAccountingAccountId = $reverseAccountingAccount ?  $reverseAccountingAccount->getSlug() : null;
        $resource->foreignCurrencyNetAmount = $entity->getForeignNetAmount();
        $resource->foreignCurrencyVatAmount = $entity->getForeignVatAmount();
        $resource->foreignCurrencyGrossAmount = $entity->getForeignGrossAmount();
        $resource->localCurrencyNetAmount = $entity->getNetAmount();
        $resource->localCurrencyVatAmount = $entity->getVatAmount();
        $resource->localCurrencyGrossAmount = $entity->getGrossAmount();
        $resource->localCurrencyReverseVatAmount = $entity->getLocalCurrencyReverseTaxAmount();
        $resource->foreignCurrencyReverseVatAmount = $entity->getForeignCurrencyReverseTaxAmount();

        return $resource;
    }

    /**
     * @inheritDoc
     */
    public function toArray()
    {
        return [
            'vat_code' => $this->vat !== null ? $this->vat->toArray() : null,
            'accounting_account_id' => $this->accountingAccountId,
            'local_currency_net_amount' => sprintf('%.4f', $this->localCurrencyNetAmount),
            'local_currency_vat_amount' => sprintf('%.4f', $this->localCurrencyVatAmount),
            'local_currency_gross_amount' => sprintf('%.4f', $this->localCurrencyGrossAmount),
            'foreign_currency_net_amount' => sprintf('%.4f', $this->foreignCurrencyNetAmount),
            'foreign_currency_vat_amount' => sprintf('%.4f', $this->foreignCurrencyVatAmount),
            'foreign_currency_gross_amount' => sprintf('%.4f', $this->foreignCurrencyGrossAmount),
            'reverse_accounting_account_id' => $this->reverseAccountingAccountId,
            'local_currency_reverse_vat_amount' => sprintf('%.4f', $this->localCurrencyReverseVatAmount ?? '0'),
            'foreign_currency_reverse_vat_amount' => sprintf('%.4f', $this->foreignCurrencyReverseVatAmount ?? '0'),
        ];
    }
}
