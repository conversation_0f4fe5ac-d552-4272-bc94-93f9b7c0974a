<?php

declare(strict_types=1);

namespace Modules\IntegrationAPI\Priv\Http\Middleware;

use App\Http\Requests\CurrentInstanceRequest;
use App\User;
use Closure;
use Illuminate\Auth\Middleware\AuthenticateWithBasicAuth;
use Illuminate\Http\Response;

class DocumentationAuthenticateAuth extends AuthenticateWithBasicAuth
{
    /**
     * @param CurrentInstanceRequest $request
     * @param Closure $next
     * @param null $guard
     * @param null $field
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null, $field = null)
    {
        $admin = $request->user('admin');
        if ($admin && $admin->isAdmin()) {
            return $next($request);
        }

        parent::handle($request, $next, $guard, $field);

        $currentInstanceId = $request->getCurrentInstance()->id;

        /** @var User $user */
        $user = $this->auth->user();

        abort_unless(
            $user->isIntegrationAPIUser() || $user->instance_id !== $currentInstanceId,
            Response::HTTP_FORBIDDEN
        );

        return $next($request);
    }

}
