<?php

declare(strict_types=1);

namespace Modules\FeatureSwitcher\Priv\Repositories;

use App\Company;
use App\Instance;
use Modules\FeatureSwitcher\Priv\Entities\Feature;
use App\Repositories\Repository;
use Modules\FeatureSwitcher\Pub\Enums\FeatureEnum;

/**
 * @method findBySlug Feature
 *
 * Class FeatureRepository
 * @package Modules\FeatureSwitcher\Priv\Repositories
 */
class FeatureRepository extends Repository
{
    public function __construct()
    {
        parent::__construct();
    }
    protected function allowedWith(): array
    {
        return [
            'featureSettings',
        ];
    }

    static function model()
    {
        return Feature::class;
    }

    public function findBySlugWithSettings(FeatureEnum $featureEnum, Company $company, Instance $instance): Feature
    {
        return $this->with(['featureSettings' => function ($query) use ($company, $instance) {
            $query->where(function ($query) use ($instance) {
                $query->where('instance_id', '=', $instance->id);
            })->orWhere(function ($query) use ($company) {
                $query->where('company_id', $company->id);
            });
        }])->findBySlug($featureEnum->getValue());
    }
}