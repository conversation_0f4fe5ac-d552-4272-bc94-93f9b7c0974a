<?php

declare(strict_types=1);

namespace Modules\DecisionMaker\Priv\Conditions;

use Mo<PERSON>les\DecisionMaker\Pub\DataSets\DataSetInterface;
use Mo<PERSON>les\DecisionMaker\Pub\Interfaces\ConditionInterface;

class NegatedCondition implements ConditionInterface
{
    /** @var ConditionInterface */
    protected $condition;

    public function __construct(ConditionInterface $condition)
    {
        $this->condition = $condition;
    }

    public function check(DataSetInterface $data): bool
    {
        return !$this->condition->check($data);
    }
}