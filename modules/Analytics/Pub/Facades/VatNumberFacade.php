<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Facades;

use App\Instance;
use Modules\Accounting\Priv\Entities\VatNumber;
use Modules\Accounting\Priv\Repositories\VatNumberRepository;
use Modules\Accounting\Priv\Services\VatNumberService;
use Modules\Analytics\Pub\Interfaces\VatNumberDTO;

class VatNumberFacade
{
    private VatNumberRepository $vatNumberRepository;
    private VatNumberService $vatNumberService;

    public function __construct(VatNumberRepository $vatNumberRepository, VatNumberService $vatNumberService)
    {
        $this->vatNumberRepository = $vatNumberRepository;
        $this->vatNumberService = $vatNumberService;
    }

    public function create(VatNumberDTO $vatNumberDTO): VatNumber
    {
        return $this->vatNumberService->create($vatNumberDTO);
    }

    public function edit(VatNumberDTO $vatNumberDTO, VatNumber $vatNumber): VatNumber
    {
        return $this->vatNumberService->edit($vatNumberDTO, $vatNumber);
    }

    public function findByCodeAndInstance(string $code, Instance $instance): ?VatNumber
    {
        return $this->vatNumberRepository->findByCodeAndInstanceId($code, $instance->id);
    }
}