<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Facades;

use App\Instance;
use Modules\Analytics\Priv\Entities\Project;
use Modules\Analytics\Priv\Repositories\ProjectRepository;
use Modules\Analytics\Priv\Services\ProjectService;
use Modules\Analytics\Pub\Interfaces\ProjectDTO;

class ProjectFacade
{
    private ProjectRepository $projectRepository;
    private ProjectService $projectService;

    public function __construct(
        ProjectRepository $projectRepository,
        ProjectService $projectService
    ) {
        $this->projectRepository = $projectRepository;
        $this->projectService = $projectService;
    }

    public function create(ProjectDTO $projectDTO): Project
    {
        return $this->projectService->create($projectDTO);
    }

    public function edit(ProjectDTO $projectDTO, Project $project): Project
    {
        return $this->projectService->edit($projectDTO, $project);
    }

    public function findByCodeAndInstance(string $code, Instance $instance): ?Project
    {
        return $this->projectRepository->findByCodeAndInstanceId($code, $instance->id);
    }
}