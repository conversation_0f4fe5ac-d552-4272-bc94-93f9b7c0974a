<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Interfaces;

use App\Enum\ActiveEnum;
use App\Instance;

interface ProjectDTO
{
    public const KEY_NAME = 'name';
    public const KEY_CODE = 'code';
    public const KEY_IS_ACTIVE = 'is_active';

    public function getCode(): string;

    public function getName(): string;

    public function getIsActive(): ActiveEnum;

    public function getInstance(): Instance;
}