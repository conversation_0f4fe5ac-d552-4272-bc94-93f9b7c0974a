<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Dtos;

use App\DocumentElement;

class DocumentElementDto
{
    protected int $id;

    private int $userId;

    protected int $documentId;

    protected int $documentElementTypeId;

    protected int $requestId;

    protected int $companyId;

    protected int $instanceId;

    private bool $isSibling;

    public function __construct(
        int $id,
        int $userId,
        int $documentId,
        int $documentElementTypeId,
        int $requestId,
        int $companyId,
        int $instanceId,
        bool $isSibling
    ) {
        $this->id = $id;
        $this->userId = $userId;
        $this->documentId = $documentId;
        $this->documentElementTypeId = $documentElementTypeId;
        $this->requestId = $requestId;
        $this->companyId = $companyId;
        $this->instanceId = $instanceId;
        $this->isSibling = $isSibling;
    }

    public static function createFromDocumentElement(DocumentElement $documentElement): DocumentElementDto
    {
        return new static(
            $documentElement->id,
            $documentElement->document->user_id,
            $documentElement->document->id,
            $documentElement->type_id,
            $documentElement->document->request->id,
            $documentElement->document->request->company_id,
            $documentElement->document->request->instance_id,
            $documentElement->isSplitLine(),
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getDocumentId(): int
    {
        return $this->documentId;
    }

    public function getDocumentElementTypeId(): int
    {
        return $this->documentElementTypeId;
    }

    public function getRequestId(): int
    {
        return $this->requestId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public function isSplitLine(): bool
    {
        return $this->isSibling;
    }
}
