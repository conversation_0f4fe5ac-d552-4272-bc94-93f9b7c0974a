<?php

declare(strict_types=1);

namespace Modules\Analytics\Pub\Dtos;

use App\Request;

class RequestDto
{
    protected int $id;
    protected int $companyId;
    protected int $instanceId;
    protected UserDto $userDto;
    private string $slug;
    private string $type;

    private function __construct(
        int $id,
        string $slug,
        int $companyId,
        int $instanceId,
        UserDto $userDto,
        string $type
    ) {
        $this->id = $id;
        $this->slug = $slug;
        $this->companyId = $companyId;
        $this->instanceId = $instanceId;
        $this->userDto = $userDto;
        $this->type = $type;
    }

    public static function createFromRequest(Request $request): RequestDto
    {
        return new static(
            $request->id,
            $request->slug,
            $request->company_id,
            $request->instance_id,
            UserDto::createFromUser($request->user),
            $request->type
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getInstanceId(): int
    {
        return $this->instanceId;
    }

    public function getUserDto(): UserDto
    {
        return $this->userDto;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getRequestType(): string
    {
        return $this->type;
    }
}
