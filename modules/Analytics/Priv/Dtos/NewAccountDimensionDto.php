<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Dtos;

use App\Enum\ActiveEnum;
use App\Instance;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class NewAccountDimensionDto
{
    protected string $code;

    protected string $name;

    protected ?int $companyId;

    protected int $order;

    protected AccountDimensionVisibilityEnum $accountDimensionVisibilityEnum;

    protected Instance $instance;

    protected ActiveEnum $isActive;

    public function __construct(
        string $code,
        string $name,
        ?int $companyId,
        int $order,
        AccountDimensionVisibilityEnum $accountDimensionVisibilityEnum,
        Instance $instance,
        ActiveEnum $isActive
    ) {
        $this->code = $code;
        $this->name = $name;
        $this->companyId = $companyId;
        $this->order = $order;
        $this->accountDimensionVisibilityEnum = $accountDimensionVisibilityEnum;
        $this->instance = $instance;
        $this->isActive = $isActive;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function getInstance(): Instance
    {
        return $this->instance;
    }

    public function getAccountDimensionVisibilityEnum(): AccountDimensionVisibilityEnum
    {
        return $this->accountDimensionVisibilityEnum;
    }

    public function getIsActive(): ActiveEnum
    {
        return $this->isActive;
    }
}