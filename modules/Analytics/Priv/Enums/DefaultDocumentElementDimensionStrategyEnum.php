<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Enums;

use App\Enum\AbstractEnum;
use Modules\FeatureSwitcher\Pub\Interfaces\FeatureEnumClassInterface;

/**
 * @method static DefaultDocumentElementDimensionStrategyEnum BY_COMPANY
 * @method static DefaultDocumentElementDimensionStrategyEnum BY_DEFAULT_FIELD
 */
class DefaultDocumentElementDimensionStrategyEnum extends AbstractEnum implements FeatureEnumClassInterface
{
    private const BY_COMPANY = 'by_company';
    private const BY_DEFAULT_FIELD = 'by_default_field';
}
