<?php
use Illuminate\Support\Facades\Route;
Route::group([
    'namespace' => '\\Modules\\Users\Priv\\Http\\Controllers',
    'middleware' => [
        'auth:api',
        'auth.locale',
        'auth.instance',
        'auth.loggedAs',
    ]
], function () {
    Route::post('users/{slug}/sensitive-data', 'UserController@sensitiveDataUpdate');
    Route::resource('users', 'UserController', ['only' => ['store']]);
    Route::resource('grades', 'GradeController', ['only' => ['index']]);
});
