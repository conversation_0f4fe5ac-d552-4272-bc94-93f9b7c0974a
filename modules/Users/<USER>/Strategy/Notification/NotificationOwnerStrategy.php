<?php

namespace Modules\Users\Priv\Strategy\Notification;

use Illuminate\Notifications\Notification;
use App\User;

class NotificationOwnerStrategy extends NotificationStrategy
{
    public function notify(User $user, Notification $notification, bool $queued, ?array $channels = null): void
    {
        $user->notifyInternal($notification, $channels, $queued);
    }

    public function shouldNotify(User $user, Notification $notification): bool
    {
        return $user->isActive();
    }
}