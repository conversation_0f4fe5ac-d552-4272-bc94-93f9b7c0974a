<?php

declare(strict_types=1);

namespace Modules\Users\Pub\Facades;

use App\BillingAPI\ValueObjects\Email;
use App\Instance;
use App\Repositories\Pagination\Paginator;
use App\Repositories\Pagination\PaginatorInterface;
use App\Repositories\UserRepository;
use App\User;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\UserViewObject;
use Modules\Common\Dtos\PageInterface;
use Modules\Common\ValueObjects\Slug;
use Modules\Users\Priv\Factory\NewUserDtoFactory;
use Modules\Users\Priv\Factory\UpdateUserDtoFactory;
use Modules\Users\Priv\Services\UserService;
use Modules\Users\Pub\Interfaces\NewUserRequestInterface;
use Modules\Users\Pub\Interfaces\UpdateUserRequestInterface;

class UserFacade
{
    /**
     * @var Guard
     */
    protected $auth;

    /**
     * @var UserService
     */
    protected $userService;

    /**
     * @var NewUserDtoFactory
     */
    protected $newUserDtoFactory;

    /**
     * @var UpdateUserDtoFactory
     */
    protected $updateUserDtoFactory;

    /**
     * @var UserRepository
     */
    protected $userRepository;

    /**
     * UserFacade constructor.
     * @param Guard $auth
     * @param UserService $userService
     * @param NewUserDtoFactory $newUserDtoFactory
     * @param UpdateUserDtoFactory $updateUserDtoFactory
     * @param UserRepository $userRepository
     */
    public function __construct(
        Guard $auth,
        UserService $userService,
        NewUserDtoFactory $newUserDtoFactory,
        UpdateUserDtoFactory $updateUserDtoFactory,
        UserRepository $userRepository
    ) {
        $this->auth = $auth;
        $this->userService = $userService;
        $this->newUserDtoFactory = $newUserDtoFactory;
        $this->updateUserDtoFactory = $updateUserDtoFactory;
        $this->userRepository = $userRepository;
    }

    public function create(NewUserRequestInterface $request): User
    {
        $newUserDto = $this->newUserDtoFactory->create($request);

        return $this->userService->create($newUserDto, $this->userRepository->findUserById($request->getAuthUserId()));
    }

    public function update(UpdateUserRequestInterface $request)
    {
        $updateUserDto = $this->updateUserDtoFactory->create($request);

        return $this->userService->update($request->getUserToChange(), $updateUserDto, $this->userRepository->findUserById($request->getAuthUserId()));
    }

    public function deactivate(string $slug, Carbon $deactivationDate, ?User $currentUser = null): bool
    {
        /** @var User $currentUser */
        $currentUser = $currentUser !== null ? $currentUser : $this->auth->user();

        /** @var User $user */
        $user = $this->userRepository->findBySlugAndInstanceId($slug, $currentUser->instance->id);

        return $this->userService->deactivate($user, $deactivationDate, $currentUser);
    }

    public function activate(string $slug, ?User $currentUser = null): bool
    {
        /** @var User $currentUser */
        $currentUser = $currentUser !== null ? $currentUser : $this->auth->user();

        /** @var User $user */
        $user = $this->userRepository->findBySlugAndInstanceId($slug, $currentUser->instance->id);

        return $this->userService->activate($user, $currentUser);
    }

    public function findUserByEmailAndCompanyId(string $email, int $companyId): ?User
    {
        return $this->userRepository->findUserByEmailAndCompanyId($email, $companyId);
    }

    public function findUserByErpIdAndInstanceId(string $erpId, int $instanceId): ?User
    {
        return $this->userRepository->findUserByErpIdAndInstanceId($erpId, $instanceId);
    }

    public function findUserByEmailAndCompanyOrInstance(string $email, int $companyId, int $instanceId): ?User
    {
        if (($user = $this->userRepository->findUserByEmailAndCompanyId($email, $companyId)) !== null) {
            return $user;
        }

        if (($user = $this->userRepository->findUserByEmailAndInstanceId($email, $instanceId)) !== null) {
            return $user;
        }

        return null;
    }

    public function findUserByEmailAndInstanceSlug(Email $email, Slug $instanceSlug): ?User
    {
        return $this->userRepository->findUserByEmailAndInstanceSlug($email, $instanceSlug);
    }

    public function findUserBySlugAndInstanceSlug(Slug $slug, Slug $instanceSlug): ?User
    {
        return $this->userRepository->findUserBySlugAndInstanceSlug($slug, $instanceSlug);
    }

    public function checkIsUserExistsAnywhere(Email $email, int $instanceId): bool
    {
        return $this->userRepository->checkIsUserExistsAnywhere($email, $instanceId);
    }

    public function list(PageInterface $page, Instance $instance, bool $withInternals): PaginatorInterface
    {
        $paginator = $this->userRepository->getUsersForCurrentInstance($page, $instance, $withInternals);

        $items = $paginator->getItems()->map(function(User $user) {
            return UserViewObject::createFromUser($user)
                ->setSupervisorFromUser($user)
                ->setAssistantFromUser($user);
        });

        return Paginator::create($items, $paginator->getTotalCount(), $page);
    }

    public function changeSupervisor(User $user, ?User $supervisor): User
    {
        return $this->userService->changeSupervisor($user, $supervisor);
    }

    public function setAssistants(User $user, Collection $assistants): User
    {
        return $this->userService->setAssistants($user, $assistants);
    }

    public function welcomeSingleUser(string $slug): void
    {
        /** @var User $currentUser */
        $currentUser = $this->auth->user();

        /** @var User $user */
        $user = $this->userRepository->findBySlugAndInstanceId($slug, $currentUser->instance_id);

        $this->userService->welcomeSingleUser($user);
    }

    public function welcomeInstanceUsers(Instance $instance): void
    {
        $this->userService->welcomeInstanceUsers($instance, true);
    }
}