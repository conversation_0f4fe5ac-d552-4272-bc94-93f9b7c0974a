<?php

declare(strict_types=1);

namespace Modules\Users\Pub\Facades;

use App\Company;
use App\Instance;
use App\Repositories\CompanyRepository;
use App\Repositories\Pagination\Paginator;
use App\Repositories\Pagination\PaginatorInterface;
use Modules\Accounting\Pub\ViewObjects\CompanyViewObject;
use Modules\Common\Dtos\PageInterface;
use Modules\Common\ValueObjects\Slug;

class CompanyFacade
{
    protected CompanyRepository $companyRepository;

    public function __construct(CompanyRepository $companyRepository)
    {
        $this->companyRepository = $companyRepository;
    }

    public function findCompanyByCodeAndInstanceId(string $companyCode, int $instanceId): ?Company
    {
        return $this->companyRepository->findCompanyByCodeAndInstanceId($companyCode, $instanceId);
    }

    public function findCompanyBySlugAndInstanceSlug(Slug $slug, Slug $instanceSlug): ?Company
    {
        return $this->companyRepository->findCompanyBySlugAndInstanceSlug($slug, $instanceSlug);
    }

    public function findByInstanceId(int $instanceId): ?Company
    {
        return $this->companyRepository->findCompanyByInstanceId($instanceId);
    }

    public function list(PageInterface $page, Instance $instance): PaginatorInterface
    {
        $paginator = $this->companyRepository->getForCurrentInstance($page, $instance);
        $items = $paginator->getItems()->map(function(Company $company) {
            return CompanyViewObject::createFromCompany($company);
        });

        return Paginator::create($items, $paginator->getTotalCount(), $page);
    }
}
