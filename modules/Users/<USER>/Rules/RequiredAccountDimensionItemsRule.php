<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Rules;

use App\Company;
use App\Instance;
use Illuminate\Contracts\Validation\Rule;
use Modules\Analytics\Priv\Services\AccountDimensionService;

class RequiredAccountDimensionItemsRule implements Rule
{
    protected AccountDimensionService $accountDimensionService;
    protected Instance $instance;
    protected ?Company $company;
    protected array $missingDimensions = [];

    public function __construct(AccountDimensionService $accountDimensionService)
    {
        $this->accountDimensionService = $accountDimensionService;
    }

    public function setInstance(Instance $instance): self
    {
        $this->instance = $instance;
        return $this;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    public function passes($attribute, $value)
    {
        // Pobierz wszystkie wymiary analityczne dla instancji
        $allAccountDimensions = $this->accountDimensionService->getForInstance($this->instance);
        
        // Filtruj tylko wymagane wymiary
        $requiredDimensions = $allAccountDimensions->filter(function ($dimension) {
            return $dimension->is_required && $dimension->is_active;
        });

        // Sprawdź czy wszystkie wymagane wymiary zostały podane
        $this->missingDimensions = [];
        
        foreach ($requiredDimensions as $requiredDimension) {
            $found = false;
            
            if (is_array($value)) {
                foreach ($value as $item) {
                    if (isset($item['account_dimension_slug']) 
                        && $item['account_dimension_slug'] === $requiredDimension->slug
                        && !empty($item['account_dimension_item_id'])) {
                        $found = true;
                        break;
                    }
                }
            }

            if (!$found) {
                $this->missingDimensions[] = $requiredDimension->name;
            }
        }

        return empty($this->missingDimensions);
    }

    public function message()
    {
        if (!empty($this->missingDimensions)) {
            return trans('users::error.required-account-dimensions-missing-specific', [
                'dimensions' => implode(', ', $this->missingDimensions)
            ]);
        }
        
        return trans('users::error.required-account-dimensions-missing');
    }
}
