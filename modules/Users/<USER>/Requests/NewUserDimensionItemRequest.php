<?php

namespace Modules\Users\Pub\Requests;

use App\User;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;

class NewUserDimensionItemRequest
{
    public const KEY_EMAIL = 'email';

    public const KEY_ACCOUNT_DIMENSIONS = 'account_dimensions';

    public const KEY_ACCOUNT_DIMENSION_ITEMS = 'account_dimension_items';

    public const KEY_UNIQUE_DIMENSION_ITEM_FOR_USER = 'unique_dimension_item_for_user';
    public const KEY_RELATIONSHIP_TYPE = 'relationship_type';
    public const KEY_COMPANY_CODE = 'company_code';

    private User $user;

    private AccountDimensionItem $accountDimensionItem;
    private bool $uniqueDimensionItemForUser;
    private string $type;

    public function __construct(
        User $user,
        AccountDimensionItem $accountDimensionItem,
        bool $uniqueDimensionItemForUser,
        string $type
    ) {
        $this->user = $user;
        $this->accountDimensionItem = $accountDimensionItem;
        $this->uniqueDimensionItemForUser = $uniqueDimensionItemForUser;
        $this->type = $type;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getAccountDimensionItem(): AccountDimensionItem
    {
        return $this->accountDimensionItem;
    }

    public function isUniqueDimensionItemForUser(): bool
    {
        return $this->uniqueDimensionItemForUser;
    }

    public function getRelationType(): string
    {
        return $this->type;
    }
}
