<?php

namespace Modules\Report\Priv\Generator;

use App\Services\Storage\StorageServiceDto;
use Modules\Report\Priv\Entity\Report;
use Modules\Report\Priv\Entity\ReportInstance;
use Modules\Report\Priv\Exception\NoDataFoundException;
use Modules\Report\Priv\Factory\OutputReportDataTransformerFactory;
use Modules\Report\Priv\Mapper\FilterMapper;
use Modules\Report\Priv\Repository\ReportRepository;
use Modules\Report\Pub\Facade\ReportFacade;

class ReportGenerator
{
    private ReportRepository $reportRepository;
    private ReportFacade $reportFacade;
    private FilterMapper $filterMapper;
    private OutputReportDataTransformerFactory $outputReportDataTransformerFactory;
    private FileGenerator $fileGenerator;

    public function __construct(
        ReportRepository $reportRepository,
        ReportFacade $reportFacade,
        FilterMapper $filterMapper,
        OutputReportDataTransformerFactory $outputReportDataTransformerFactory,
        FileGenerator $fileGenerator
    ) {
        $this->reportRepository = $reportRepository;
        $this->reportFacade = $reportFacade;
        $this->filterMapper = $filterMapper;
        $this->outputReportDataTransformerFactory = $outputReportDataTransformerFactory;
        $this->fileGenerator = $fileGenerator;
    }

    public function generateReport(int $instanceId, int $reportId, array $filters): StorageServiceDto
    {
        /** @var Report $report */
        $report = $this->reportRepository->findById($reportId);

        /** @var ReportInstance $instanceSettings */
        $instanceSettings = $report->instances->filter(
            fn(ReportInstance $reportInstance) => $reportInstance->instance_id === $instanceId,
        )->first();

        $mappedFilters = $this->filterMapper->map($report, $filters);

        $reportData = $this->reportFacade->search($report->name, $instanceId, $mappedFilters);

        $reportData = array_map(static function ($item) {
            if (isset($item['instance_id'])) {
                unset($item['instance_id']);
            }
            return $item;
        }, $reportData);

        if (empty($reportData)) {
            throw new NoDataFoundException();
        }

        $transformer = $this->outputReportDataTransformerFactory->make(
            $instanceSettings->output_transformer ?? $report->output_transformer,
        );

        $transformedData = $transformer->transform($reportData);

        return $this->fileGenerator->toExcel($transformedData->getHeaders(), $transformedData->getContent());
    }
}
