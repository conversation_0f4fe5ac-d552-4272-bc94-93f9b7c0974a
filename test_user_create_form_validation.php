<?php

// Test walidacji formularza tworzenia użytkownika
require_once 'vendor/autoload.php';

echo "Test walidacji formularza tworzenia użytkownika\n";
echo "===============================================\n";

// Test 1: Sprawdzenie czy NewUserDtoFactory ma nowe reguły walidacji
$factorySource = file_get_contents('modules/Users/<USER>/Factory/NewUserDtoFactory.php');
if (strpos($factorySource, 'RequiredAccountDimensionItemsRule') !== false) {
    echo "✓ NewUserDtoFactory używa RequiredAccountDimensionItemsRule\n";
} else {
    echo "✗ NewUserDtoFactory nie używa RequiredAccountDimensionItemsRule\n";
}

// Test 2: Sprawd<PERSON>ie czy walidacja account_dimension_items jest w rules()
if (strpos($factorySource, "'account_dimension_items' => [") !== false) {
    echo "✓ Walidacja account_dimension_items jest dodana do rules()\n";
} else {
    echo "✗ Walidacja account_dimension_items nie jest dodana do rules()\n";
}

// Test 3: Sprawdzenie czy prepareValidationData obsługuje account_dimension_items
if (strpos($factorySource, '$accountDimensionItems = $request->get(\'account_dimension_items\', []);') !== false) {
    echo "✓ prepareValidationData obsługuje account_dimension_items\n";
} else {
    echo "✗ prepareValidationData nie obsługuje account_dimension_items\n";
}

// Test 4: Sprawdzenie czy frontend używa nowego formatu
$frontendSource = file_get_contents('resources/assets/js/components/SettingsPage/UsersCreatePage/UserCreateForm.tsx');
if (strpos($frontendSource, 'account_dimension.${index}') !== false) {
    echo "✓ Frontend używa nowego formatu account_dimension.{index}\n";
} else {
    echo "✗ Frontend nie używa nowego formatu account_dimension.{index}\n";
}

// Test 5: Sprawdzenie czy kontener używa nowego formatu
$containerSource = file_get_contents('resources/assets/js/containers/UserCreateForm/UserCreateForm.tsx');
if (strpos($containerSource, 'account_dimension: {}') !== false) {
    echo "✓ Kontener używa nowego formatu account_dimension\n";
} else {
    echo "✗ Kontener nie używa nowego formatu account_dimension\n";
}

// Test 6: Sprawdzenie czy submit przekształca dane poprawnie
if (strpos($containerSource, 'data.account_dimension') !== false && strpos($containerSource, 'accountDimensions[parseInt(index)]') !== false) {
    echo "✓ Submit przekształca dane z account_dimension na account_dimension_items\n";
} else {
    echo "✗ Submit nie przekształca danych poprawnie\n";
}

echo "\n=== PODSUMOWANIE ===\n";
echo "🎯 Implementacja została zakończona!\n\n";
echo "Teraz gdy frontend wyśle dane w formacie:\n";
echo "{\n";
echo "  \"account_dimension\": {\n";
echo "    \"0\": 123,\n";
echo "    \"1\": 456\n";
echo "  }\n";
echo "}\n\n";
echo "Backend:\n";
echo "1. Przekształci to na account_dimension_items w submit()\n";
echo "2. Sprawdzi walidację dla account_dimension_items\n";
echo "3. Zwróci błędy walidacji dla pól account_dimension_items.0, account_dimension_items.1 itp.\n";
echo "4. Frontend będzie mógł wyświetlić błędy dla odpowiednich pól\n";
