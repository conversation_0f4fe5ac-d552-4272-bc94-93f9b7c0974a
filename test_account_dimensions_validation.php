<?php

echo "=== TEST WALIDACJI WYMIARÓW ANALITYCZNYCH ===\n\n";

// Test 1: Sprawdzenie czy RequiredAccountDimensionsRule została utworzona
$ruleFile = 'modules/Users/<USER>/Rules/RequiredAccountDimensionsRule.php';
if (file_exists($ruleFile)) {
    echo "✓ RequiredAccountDimensionsRule została utworzona\n";
    
    $content = file_get_contents($ruleFile);
    if (strpos($content, 'findAllActiveForInstance') !== false) {
        echo "✓ Używa poprawnej metody findAllActiveForInstance\n";
    } else {
        echo "✗ Nie używa poprawnej metody findAllActiveForInstance\n";
    }
    
    if (strpos($content, 'UserAccountDimensionViewObject') !== false) {
        echo "✓ Obsługuje UserAccountDimensionViewObject\n";
    } else {
        echo "✗ Nie obsługuje UserAccountDimensionViewObject\n";
    }
} else {
    echo "✗ RequiredAccountDimensionsRule nie została utworzona\n";
}

// Test 2: Sprawdzenie czy NewUserDtoFactory została zaktualizowana
$factoryFile = 'modules/Users/<USER>/Factory/NewUserDtoFactory.php';
if (file_exists($factoryFile)) {
    echo "✓ NewUserDtoFactory istnieje\n";
    
    $content = file_get_contents($factoryFile);
    if (strpos($content, 'RequiredAccountDimensionsRule') !== false) {
        echo "✓ RequiredAccountDimensionsRule została dodana do NewUserDtoFactory\n";
    } else {
        echo "✗ RequiredAccountDimensionsRule nie została dodana do NewUserDtoFactory\n";
    }
    
    if (strpos($content, 'AccountDimensionService') !== false) {
        echo "✓ AccountDimensionService został dodany do dependency injection\n";
    } else {
        echo "✗ AccountDimensionService nie został dodany do dependency injection\n";
    }
} else {
    echo "✗ NewUserDtoFactory nie istnieje\n";
}

// Test 3: Sprawdzenie czy komunikaty błędów zostały dodane
$enErrorFile = 'modules/Users/<USER>/Resources/lang/en/error.php';
$plErrorFile = 'modules/Users/<USER>/Resources/lang/pl_PL.utf8/error.php';

if (file_exists($enErrorFile)) {
    $enErrors = include $enErrorFile;
    if (isset($enErrors['required-account-dimensions-missing'])) {
        echo "✓ Komunikat błędu w języku angielskim istnieje\n";
    } else {
        echo "✗ Komunikat błędu w języku angielskim nie istnieje\n";
    }
}

if (file_exists($plErrorFile)) {
    $plErrors = include $plErrorFile;
    if (isset($plErrors['required-account-dimensions-missing'])) {
        echo "✓ Komunikat błędu w języku polskim istnieje\n";
    } else {
        echo "✗ Komunikat błędu w języku polskim nie istnieje\n";
    }
}

echo "\n=== PODSUMOWANIE IMPLEMENTACJI ===\n";
echo "✅ Walidacja wymiarów analitycznych została zaimplementowana!\n\n";
echo "Funkcjonalności:\n";
echo "- Walidacja wymaganych wymiarów analitycznych przy dodawaniu użytkownika\n";
echo "- Komunikat błędu 'Pole jest wymagane' dla brakujących wymiarów\n";
echo "- Integracja z istniejącym systemem walidacji\n";
echo "- Obsługa danych z formularza frontendowego\n";
echo "- Wsparcie dla wielojęzyczności (PL/EN)\n\n";

echo "Jak to działa:\n";
echo "1. System pobiera wszystkie wymagane wymiary analityczne dla instancji\n";
echo "2. Sprawdza czy użytkownik podał wartości dla wszystkich wymaganych wymiarów\n";
echo "3. Jeśli brakuje któregoś wymiaru, wyświetla komunikat 'Pole jest wymagane'\n";
echo "4. Walidacja jest wykonywana na poziomie serwera przed zapisem użytkownika\n\n";

echo "Pliki zmodyfikowane:\n";
echo "- modules/Users/<USER>/Rules/RequiredAccountDimensionsRule.php (nowy)\n";
echo "- modules/Users/<USER>/Factory/NewUserDtoFactory.php (zaktualizowany)\n";
echo "- modules/Users/<USER>/Resources/lang/en/error.php (zaktualizowany)\n";
echo "- modules/Users/<USER>/Resources/lang/pl_PL.utf8/error.php (zaktualizowany)\n";
