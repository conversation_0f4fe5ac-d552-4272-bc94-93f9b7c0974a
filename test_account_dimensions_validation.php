<?php

// Test walidacji wymiarów analitycznych
require_once 'vendor/autoload.php';

echo "Test walidacji wymiarów analitycznych - wersja 2.0\n";
echo "==================================================\n";

// Test 1: Sprawdzenie czy klasa NewUserDtoFactory ma metodę addRequiredAccountDimensionsValidation
$reflection = new ReflectionClass('Modules\Users\Priv\Factory\NewUserDtoFactory');
if ($reflection->hasMethod('addRequiredAccountDimensionsValidation')) {
    echo "✓ Metoda addRequiredAccountDimensionsValidation została dodana\n";
} else {
    echo "✗ Metoda addRequiredAccountDimensionsValidation nie istnieje\n";
}

// Test 2: Sprawdzenie czy niepotrzebna klasa RequiredAccountDimensionsRule została usunięta
if (!class_exists('Modules\Users\Priv\Rules\RequiredAccountDimensionsRule')) {
    echo "✓ Niepotrzebna klasa RequiredAccountDimensionsRule została usunięta\n";
} else {
    echo "✗ Klasa RequiredAccountDimensionsRule nadal istnieje\n";
}

// Test 3: Sprawdzenie czy komunikaty błędów zostały dodane
$enErrorFile = 'modules/Users/<USER>/Resources/lang/en/error.php';
$plErrorFile = 'modules/Users/<USER>/Resources/lang/pl_PL.utf8/error.php';

if (file_exists($enErrorFile)) {
    $enErrors = include $enErrorFile;
    if (isset($enErrors['required-account-dimensions-missing'])) {
        echo "✓ Komunikat błędu w języku angielskim istnieje\n";
    } else {
        echo "✗ Komunikat błędu w języku angielskim nie istnieje\n";
    }
}

if (file_exists($plErrorFile)) {
    $plErrors = include $plErrorFile;
    if (isset($plErrors['required-account-dimensions-missing'])) {
        echo "✓ Komunikat błędu w języku polskim istnieje\n";
    } else {
        echo "✗ Komunikat błędu w języku polskim nie istnieje\n";
    }
}

echo "\n=== PODSUMOWANIE IMPLEMENTACJI ===\n";
echo "✅ Walidacja wymiarów analitycznych została poprawiona!\n\n";
echo "Zmiany:\n";
echo "- Walidacja 'required' jest teraz dodawana dla każdego wymaganego wymiaru osobno\n";
echo "- Błędy będą wyświetlane przy konkretnych polach (np. account_dimensions.slug)\n";
echo "- Frontend będzie mógł pokazać błąd przy odpowiednim polu formularza\n";
echo "- Usunięto niepotrzebną klasę RequiredAccountDimensionsRule\n";
echo "- Uproszczono kod i poprawiono wydajność\n\n";
echo "Jak to działa:\n";
echo "1. System pobiera wszystkie wymagane wymiary analityczne dla instancji\n";
echo "2. Dla każdego wymaganego wymiaru dodaje regułę 'required' do walidacji\n";
echo "3. Jeśli użytkownik nie wypełni wymaganego wymiaru, dostanie błąd przy tym konkretnym polu\n";
echo "4. Frontend wyświetli błąd przy odpowiednim select-boxie\n";
