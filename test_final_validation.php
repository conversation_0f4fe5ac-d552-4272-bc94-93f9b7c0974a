<?php

// Test finalnej walidacji wymiarów analitycznych
require_once 'vendor/autoload.php';

echo "Test finalnej walidacji wymiarów analitycznych\n";
echo "==============================================\n";

// Test 1: Sprawdzenie czy nowa klasa RequiredAccountDimensionItemsRule istnieje
if (class_exists('Modules\Users\Priv\Rules\RequiredAccountDimensionItemsRule')) {
    echo "✓ Klasa RequiredAccountDimensionItemsRule została utworzona\n";
} else {
    echo "✗ Klasa RequiredAccountDimensionItemsRule nie istnieje\n";
}

// Test 2: Sprawdzenie czy NewUserRequest obsługuje account_dimension_items
$reflection = new ReflectionClass('Modules\Users\Priv\Http\Requests\NewUserRequest');
$method = $reflection->getMethod('getAccountDimensions');
$source = file_get_contents('modules/Users/<USER>/Http/Requests/NewUserRequest.php');
if (strpos($source, 'account_dimension_items') !== false) {
    echo "✓ NewUserRequest obsługuje pole account_dimension_items\n";
} else {
    echo "✗ NewUserRequest nie obsługuje pola account_dimension_items\n";
}

// Test 3: Sprawdzenie czy NewUserDtoFactory ma nową regułę walidacji
$factorySource = file_get_contents('modules/Users/<USER>/Factory/NewUserDtoFactory.php');
if (strpos($factorySource, 'RequiredAccountDimensionItemsRule') !== false) {
    echo "✓ NewUserDtoFactory używa RequiredAccountDimensionItemsRule\n";
} else {
    echo "✗ NewUserDtoFactory nie używa RequiredAccountDimensionItemsRule\n";
}

// Test 4: Sprawdzenie komunikatów błędów
$enErrorFile = 'modules/Users/<USER>/Resources/lang/en/error.php';
$plErrorFile = 'modules/Users/<USER>/Resources/lang/pl_PL.utf8/error.php';

if (file_exists($enErrorFile)) {
    $enErrors = include $enErrorFile;
    if (isset($enErrors['required-account-dimensions-missing-specific'])) {
        echo "✓ Szczegółowy komunikat błędu w języku angielskim został dodany\n";
    } else {
        echo "✗ Szczegółowy komunikat błędu w języku angielskim nie został dodany\n";
    }
}

if (file_exists($plErrorFile)) {
    $plErrors = include $plErrorFile;
    if (isset($plErrors['required-account-dimensions-missing-specific'])) {
        echo "✓ Szczegółowy komunikat błędu w języku polskim został dodany\n";
    } else {
        echo "✗ Szczegółowy komunikat błędu w języku polskim nie został dodany\n";
    }
}

echo "\n=== FINALNE PODSUMOWANIE ===\n";
echo "🎉 Walidacja wymiarów analitycznych została poprawnie zaimplementowana!\n\n";
echo "Jak to teraz działa:\n";
echo "1. Frontend wysyła dane w formacie account_dimension_items\n";
echo "2. Backend sprawdza czy wszystkie wymagane wymiary zostały przesłane\n";
echo "3. Jeśli brakuje wymaganych wymiarów, zwraca błąd dla pola account_dimension_items\n";
echo "4. Frontend wyświetla komunikat błędu przy sekcji wymiarów analitycznych\n";
echo "5. Komunikat zawiera nazwy brakujących wymiarów\n\n";
echo "Struktura danych z frontendu:\n";
echo "account_dimension_items: [\n";
echo "  { account_dimension_slug: 'department', account_dimension_item_id: 123 },\n";
echo "  { account_dimension_slug: 'project', account_dimension_item_id: 456 }\n";
echo "]\n\n";
echo "Błąd walidacji będzie zwrócony dla pola 'account_dimension_items'\n";
echo "z komunikatem zawierającym nazwy brakujących wymiarów.\n";
