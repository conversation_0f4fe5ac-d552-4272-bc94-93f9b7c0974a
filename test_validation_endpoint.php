<?php

// Test endpointu walidacji
require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use Modules\Users\Priv\Http\Requests\NewUserRequest;
use Modules\Users\Priv\Factory\NewUserDtoFactory;

echo "Test endpointu walidacji wymiarów analitycznych\n";
echo "===============================================\n";

// Symulacja danych z frontendu (bez wymaganych wymiarów)
$testData = [
    'first_name' => 'Jan',
    'last_name' => 'Kowalski',
    'email' => '<EMAIL>',
    'sex' => 'M',
    'birth_date' => '1990-01-01',
    'company_id' => 1,
    'groups' => [1],
    'grade_id' => 1,
    'phone_number' => '*********',
    'citizenship_id' => 1,
    'language' => 'pl',
    'employee_unique_identifier' => 'JK001',
    // Brak account_dimension_items - powinno wywoła<PERSON> błąd walidacji
];

echo "Dane testowe (bez wymaganych wymiarów analitycznych):\n";
echo json_encode($testData, JSON_PRETTY_PRINT) . "\n\n";

echo "Jeśli w systemie są skonfigurowane wymagane wymiary analityczne,\n";
echo "walidacja powinna zwrócić błąd dla pola 'account_dimension_items'.\n\n";

echo "Aby przetestować w praktyce:\n";
echo "1. Otwórz formularz dodawania użytkownika na froncie\n";
echo "2. Wypełnij wszystkie pola OPRÓCZ wymaganych wymiarów analitycznych\n";
echo "3. Spróbuj zapisać użytkownika\n";
echo "4. Powinieneś zobaczyć komunikat błędu przy sekcji wymiarów analitycznych\n";
echo "5. Komunikat powinien zawierać nazwy brakujących wymiarów\n\n";

echo "Przykład oczekiwanego błędu:\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"alerts\": [\n";
echo "    {\n";
echo "      \"type\": \"error\",\n";
echo "      \"message\": \"Podane dane są nieprawidłowe\",\n";
echo "      \"errors\": {\n";
echo "        \"account_dimension_items\": [\n";
echo "          \"Następujące wymagane wymiary analityczne muszą być wypełnione: Dział, Projekt\"\n";
echo "        ]\n";
echo "      }\n";
echo "    }\n";
echo "  ]\n";
echo "}\n\n";

echo "✅ Implementacja jest gotowa do testowania!\n";
