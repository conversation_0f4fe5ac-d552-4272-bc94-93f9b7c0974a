#!/bin/bash

echo "Make storage directory"
mkdir -p storage
cp -R -u -p storage.example/* storage

echo "Mindento build started..."
composer install
php artisan cache:clear

echo "Reset database"
php artisan migrate:fresh --seed
php artisan module:migrate

echo "Front"
php artisan storage:link

echo "Passport install"
php artisan passport:install

IS_ELASTIC_ENABLED=$(grep --regexp ^SCOUT_DRIVER .env | cut -d '=' -f2)
if [ "$IS_ELASTIC_ENABLED" = "elastic" ]; then
   echo "Create elastic indexes"
   php artisan elastic:create-indexes
#   php artisan trip-planner:spot-searcher:load
fi

echo "Create billing user"
php artisan create:billing-client
php artisan create:cm-client

echo "Horizon queue configuring"
php artisan queue:restart
php artisan horizon:terminate

echo "Exchange rates seeding"
php artisan currency:save
php artisan exchangerate:history

php artisan users:identity-provider:flush-all-users
php artisan users:identity-provider:seed

#echo "Starting horizon..."
#php artisan horizon
